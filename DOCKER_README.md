# 🎤 智能语音转录系统 - Docker版本

## 📋 系统概述

这是一个完整的Docker容器化智能语音转录系统，包含：

- **🎯 Whisper转录服务**：使用OpenAI Whisper进行高精度语音转录
- **🌐 Ollama翻译服务**：使用Gemma3模型进行多语言翻译
- **📊 智能音频分析**：实时音频质量检测和自适应分段
- **🎛️ 可视化界面**：直观的参数调整和状态监控

## 🚀 快速启动

### 1. 系统要求

- Docker 20.10+
- Docker Compose 2.0+
- 至少4GB可用内存
- 推荐：NVIDIA GPU（可选，用于加速）

### 2. 一键启动

```bash
# 启动系统
./start.sh

# 停止系统
./stop.sh
```

### 3. 手动启动

```bash
# 构建并启动所有服务
docker-compose up --build -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## 🌐 访问地址

启动成功后，可以通过以下地址访问：

- **主页面**: http://localhost:9004
- **智能转录**: http://localhost:9004/static/adaptive_transcription.html
- **API文档**: http://localhost:9004/docs
- **健康检查**: http://localhost:9004/health
- **Ollama API**: http://localhost:11434

## 📦 服务架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Nginx代理     │    │  Whisper转录    │    │  Ollama翻译     │
│   (可选)        │◄──►│     服务        │◄──►│     服务        │
│   Port: 80      │    │   Port: 9004    │    │  Port: 11434    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🎛️ 功能特性

### 转录功能
- ✅ 多语言支持（中文、英文、越南文）
- ✅ 自动语言检测
- ✅ 实时音频质量分析
- ✅ 智能静音检测
- ✅ 可调参数（阈值、延迟等）

### 翻译功能
- ✅ 高质量AI翻译
- ✅ 延迟翻译确保准确性
- ✅ 三种目标语言
- ✅ 专业翻译提示词

### 界面功能
- ✅ 可视化阈值显示
- ✅ 实时状态监控
- ✅ 参数调整面板
- ✅ 结果导出功能

## 🔧 配置说明

### 环境变量

在 `docker-compose.yml` 中可以调整以下环境变量：

```yaml
environment:
  - WHISPER_MODEL_SIZE=base    # 模型大小: tiny/base/small/medium/large
  - WHISPER_DEVICE=cpu         # 设备: cpu/cuda
  - OLLAMA_HOST=ollama         # Ollama服务地址
  - OLLAMA_PORT=11434          # Ollama服务端口
```

### 数据持久化

系统使用Docker卷来持久化数据：

- `whisper_models`: Whisper模型文件
- `ollama_data`: Ollama模型和配置
- `./app/logs`: 应用日志

## 📊 监控和日志

### 查看服务状态
```bash
docker-compose ps
```

### 查看实时日志
```bash
# 所有服务日志
docker-compose logs -f

# 特定服务日志
docker-compose logs -f whisper-transcription
docker-compose logs -f ollama
```

### 健康检查
```bash
# Whisper服务健康检查
curl http://localhost:9004/health

# Ollama服务健康检查
curl http://localhost:11434/api/tags
```

## 🛠️ 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   sudo lsof -i :9004
   sudo lsof -i :11434
   
   # 停止占用进程
   sudo fuser -k 9004/tcp
   sudo fuser -k 11434/tcp
   ```

2. **内存不足**
   ```bash
   # 检查内存使用
   docker stats
   
   # 清理未使用的容器和镜像
   docker system prune -f
   ```

3. **模型下载失败**
   ```bash
   # 手动下载Gemma模型
   docker-compose exec ollama ollama pull gemma2:2b
   ```

### 重置系统
```bash
# 完全重置（删除所有数据）
docker-compose down -v
docker system prune -a -f
./start.sh
```

## 📁 文件结构

```
stt_project/
├── app/                          # 应用代码
│   ├── Dockerfile               # Whisper服务镜像
│   ├── openai_whisper_server.py # 主服务器
│   ├── static/                  # 静态文件
│   └── requirements.txt         # Python依赖
├── nginx/                       # Nginx配置
│   └── nginx.conf              # 反向代理配置
├── docker-compose.yml          # Docker编排文件
├── start.sh                    # 启动脚本
├── stop.sh                     # 停止脚本
└── DOCKER_README.md           # 本文档
```

## 🚀 部署到其他环境

### 1. 复制项目
```bash
# 复制整个项目目录
cp -r stt_project /path/to/new/location
cd /path/to/new/location
```

### 2. 修改配置（如需要）
```bash
# 编辑docker-compose.yml调整端口或配置
vim docker-compose.yml
```

### 3. 启动系统
```bash
./start.sh
```

## 📈 性能优化

### GPU加速（如果有NVIDIA GPU）
```yaml
# 在docker-compose.yml中添加GPU支持
services:
  whisper-transcription:
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
```

### 内存优化
```yaml
# 限制内存使用
services:
  whisper-transcription:
    mem_limit: 2g
  ollama:
    mem_limit: 4g
```

## 🔒 安全建议

1. **生产环境部署**：
   - 使用HTTPS
   - 配置防火墙
   - 限制访问IP

2. **数据安全**：
   - 定期备份数据卷
   - 使用安全的网络配置

## 📞 支持

如有问题，请检查：
1. Docker和Docker Compose版本
2. 系统资源（内存、磁盘空间）
3. 网络连接
4. 日志文件中的错误信息

---

**🎉 享受智能语音转录体验！**
