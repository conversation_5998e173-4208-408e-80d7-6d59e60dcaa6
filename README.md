# 實時語音轉文字系統

基於 React + TypeScript 前端和 Python Flask + Faster Whisper 後端的實時語音轉文字系統，支持 YouTube 影片和線上會議字幕生成。

## 功能特點

- ✅ **實時語音轉文字**：支持麥克風和系統音頻混合錄製
- ✅ **文件上傳轉錄**：支持多種音頻格式文件上傳轉錄
- ✅ **YouTube 影片字幕**：可捕捉 YouTube 影片音頻進行實時字幕
- ✅ **線上會議字幕**：支持各種線上會議平台的實時字幕
- ✅ **高質量轉錄**：基於 OpenAI Faster Whisper 模型
- ✅ **現代化界面**：React + Material-UI 響應式設計
- ✅ **實時通信**：WebSocket 實時數據傳輸

## 技術架構

### 前端
- **React 18** + **TypeScript**
- **Material-UI (MUI)** 組件庫
- **WebSocket** 實時通信
- **Web Audio API** 音頻處理
- **MediaRecorder API** 音頻錄製

### 後端
- **Python Flask** + **Flask-SocketIO**
- **Faster Whisper** 語音識別模型
- **WebSocket** 實時通信
- **Docker** 容器化部署

## 快速開始

### 方法一：Docker Compose（推薦）

1. **克隆項目**
```bash
git clone <repository-url>
cd stt_project
```

2. **啟動服務**
```bash
docker-compose up --build
```

3. **訪問應用**
- 前端應用：http://localhost:3000
- 後端 API：http://localhost:9004
- 後端狀態頁：http://localhost:9004

### 方法二：本地開發

#### 後端設置

1. **進入後端目錄**
```bash
cd app
```

2. **安裝依賴**
```bash
pip install -r requirements.txt
```

3. **啟動後端服務**
```bash
python app.py
```

#### 前端設置

1. **進入前端目錄**
```bash
cd frontend
```

2. **安裝依賴**
```bash
npm install
```

3. **啟動前端服務**
```bash
npm start
```

## 使用說明

### 實時語音轉文字

1. 打開前端應用 (http://localhost:3000)
2. 點擊「實時語音轉文字」標籤
3. 選擇是否包含系統音頻：
   - **關閉**：僅錄製麥克風音頻
   - **開啟**：同時錄製麥克風和系統音頻（用於 YouTube 或會議）
4. 點擊「開始語音轉文字」
5. 授權瀏覽器訪問麥克風（和螢幕錄製）權限
6. 系統會每 2 秒處理一次音頻並顯示轉錄結果

### 文件上傳轉錄

1. 點擊「文件上傳轉錄」標籤
2. 點擊選擇音頻文件（支持 WAV, MP3, M4A 等格式）
3. 點擊「開始轉錄」
4. 等待轉錄完成並查看結果

### YouTube 影片字幕

1. 開啟「包含系統音頻」選項
2. 在瀏覽器中播放 YouTube 影片
3. 開始實時語音轉文字
4. 選擇「分享整個螢幕」或「分享瀏覽器標籤」
5. 確保選擇「分享音頻」選項

### 線上會議字幕

1. 開啟「包含系統音頻」選項
2. 加入線上會議（Zoom, Teams, Meet 等）
3. 開始實時語音轉文字
4. 系統會同時捕捉會議音頻和您的麥克風音頻

## 配置選項

### 環境變量

後端支持以下環境變量：

```bash
WHISPER_MODEL_SIZE=base    # 模型大小: tiny, base, small, medium, large
WHISPER_DEVICE=cpu         # 運行設備: cpu, cuda
WHISPER_COMPUTE_TYPE=int8  # 計算類型: int8, float16, float32
```

### 模型選擇

- **tiny**: 最快，準確度較低
- **base**: 平衡速度和準確度（推薦）
- **small**: 較好準確度，速度適中
- **medium**: 高準確度，速度較慢
- **large**: 最高準確度，速度最慢

## 瀏覽器支持

- ✅ Chrome 88+
- ✅ Firefox 85+
- ✅ Safari 14+
- ✅ Edge 88+

**注意**：需要 HTTPS 或 localhost 環境才能使用麥克風和螢幕錄製功能。

## 故障排除

### 常見問題

1. **無法訪問麥克風**
   - 確保瀏覽器已授權麥克風權限
   - 檢查系統麥克風設置

2. **無法捕捉系統音頻**
   - 確保選擇了「分享音頻」選項
   - 某些瀏覽器可能不支持系統音頻捕捉

3. **WebSocket 連接失敗**
   - 檢查後端服務是否正常運行
   - 確認防火牆設置

4. **轉錄質量不佳**
   - 確保音頻清晰，減少背景噪音
   - 考慮使用更大的 Whisper 模型

### 日誌查看

```bash
# 查看後端日誌
docker-compose logs backend

# 查看前端日誌
docker-compose logs frontend
```

## 開發指南

### 項目結構

```
stt_project/
├── app/                    # 後端代碼
│   ├── app.py             # 主應用文件
│   ├── requirements.txt   # Python 依賴
│   ├── Dockerfile         # 後端 Docker 配置
│   └── templates/         # HTML 模板
├── frontend/              # 前端代碼
│   ├── src/              # React 源代碼
│   ├── public/           # 靜態文件
│   ├── package.json      # Node.js 依賴
│   └── Dockerfile        # 前端 Docker 配置
├── nginx/                # Nginx 配置
├── docker-compose.yml    # Docker Compose 配置
└── README.md            # 說明文檔
```

### 貢獻指南

1. Fork 項目
2. 創建功能分支
3. 提交更改
4. 創建 Pull Request

## 許可證

MIT License

## 支持

如有問題或建議，請創建 Issue 或聯繫開發團隊。
