<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>瀏覽器系統音頻轉錄</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #1976d2;
            text-align: center;
        }
        .section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background-color: #1976d2;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background-color: #1565c0;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        button.active {
            background-color: #4caf50;
        }
        button.recording {
            background-color: #f44336;
            animation: pulse 1s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        .status.warning {
            background-color: #fff3cd;
            color: #856404;
        }
        .model-selector {
            background-color: #e3f2fd;
            border: 2px solid #2196f3;
            padding: 20px;
            margin-bottom: 20px;
        }
        .model-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 10px 0;
        }
        .audio-controls {
            background-color: #fff3e0;
            border: 2px solid #ff9800;
            padding: 20px;
            margin-bottom: 20px;
        }
        .transcription-display {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            max-height: 500px;
            overflow-y: auto;
        }
        .transcription-item {
            margin: 8px 0;
            padding: 12px;
            border-left: 4px solid #007bff;
            background-color: white;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .transcription-item.system {
            border-left-color: #28a745;
        }
        .transcription-item.microphone {
            border-left-color: #ffc107;
        }
        .timestamp {
            font-size: 12px;
            color: #6c757d;
            margin-right: 10px;
        }
        .source-tag {
            font-size: 11px;
            padding: 3px 8px;
            border-radius: 12px;
            margin-right: 8px;
            font-weight: bold;
        }
        .source-tag.system {
            background-color: #28a745;
            color: white;
        }
        .source-tag.microphone {
            background-color: #ffc107;
            color: black;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .stat-value {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 12px;
            opacity: 0.9;
        }
        .instructions {
            background-color: #e8f5e8;
            border: 1px solid #c3e6c3;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .instructions h3 {
            color: #2e7d32;
            margin-top: 0;
        }
        .instructions ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        .instructions li {
            margin: 8px 0;
        }
        .warning-box {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .warning-box h4 {
            color: #856404;
            margin-top: 0;
        }
        input[type="file"] {
            margin: 10px 0;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 瀏覽器系統音頻轉錄</h1>
        
        <!-- 使用說明 -->
        <div class="instructions">
            <h3>📋 使用說明</h3>
            <ol>
                <li><strong>系統音頻捕獲</strong>：點擊「開始系統音頻」，選擇「整個螢幕」或「瀏覽器標籤」來捕獲系統聲音</li>
                <li><strong>YouTube 轉錄</strong>：開啟 YouTube 影片，開始系統音頻捕獲，播放影片即可實時轉錄</li>
                <li><strong>WebEx 會議</strong>：在會議中開始系統音頻捕獲，即可轉錄會議內容</li>
                <li><strong>麥克風轉錄</strong>：點擊「開始麥克風」進行語音輸入轉錄</li>
                <li><strong>文件轉錄</strong>：上傳音頻文件進行批量轉錄</li>
            </ol>
        </div>

        <!-- 警告提示 -->
        <div class="warning-box">
            <h4>⚠️ 重要提示</h4>
            <p>系統音頻捕獲需要瀏覽器的螢幕分享權限。請在彈出的權限對話框中選擇「整個螢幕」或包含音頻的「瀏覽器標籤」。</p>
        </div>
        
        <!-- 模型選擇 -->
        <div class="model-selector">
            <h3>🔧 模型選擇</h3>
            <div class="model-buttons">
                <button id="model-tiny" onclick="switchModel('tiny')">Tiny (快速)</button>
                <button id="model-base" onclick="switchModel('base')" class="active">Base (推薦)</button>
                <button id="model-small" onclick="switchModel('small')">Small (平衡)</button>
                <button id="model-medium" onclick="switchModel('medium')">Medium (高質量)</button>
                <button id="model-large" onclick="switchModel('large')">Large (最高質量)</button>
            </div>
            <div id="modelStatus" class="status info">當前模型: Base</div>
        </div>

        <!-- 音頻控制 -->
        <div class="audio-controls">
            <h3>🔊 音頻捕獲控制</h3>
            
            <div style="margin: 15px 0;">
                <button id="startSystemAudio" onclick="startSystemAudio()">🖥️ 開始系統音頻 (YouTube/WebEx)</button>
                <button id="stopSystemAudio" onclick="stopSystemAudio()" disabled>⏹️ 停止系統音頻</button>
            </div>
            
            <div style="margin: 15px 0;">
                <button id="startMicrophone" onclick="startMicrophone()">🎙️ 開始麥克風</button>
                <button id="stopMicrophone" onclick="stopMicrophone()" disabled>⏹️ 停止麥克風</button>
            </div>
            
            <div style="margin: 15px 0;">
                <input type="file" id="audioFile" accept="audio/*">
                <button onclick="uploadFile()">📁 上傳文件轉錄</button>
            </div>
            
            <div id="audioStatus" class="status info">音頻狀態: 待機中</div>
        </div>

        <!-- 統計信息 -->
        <div class="stats">
            <div class="stat-card">
                <div class="stat-value" id="totalTranscriptions">0</div>
                <div class="stat-label">總轉錄次數</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="systemTranscriptions">0</div>
                <div class="stat-label">系統音頻</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="microphoneTranscriptions">0</div>
                <div class="stat-label">麥克風</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="fileTranscriptions">0</div>
                <div class="stat-label">文件轉錄</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="currentModel">Base</div>
                <div class="stat-label">當前模型</div>
            </div>
        </div>

        <!-- 實時轉錄顯示 -->
        <div class="section">
            <h3>📝 實時轉錄結果</h3>
            <div style="margin: 10px 0;">
                <button onclick="clearTranscriptions()">🗑️ 清空記錄</button>
                <button onclick="exportTranscriptions()">💾 匯出記錄</button>
                <button onclick="copyAllTranscriptions()">📋 複製全部</button>
            </div>
            <div class="transcription-display" id="transcriptionDisplay">
                <div class="transcription-item">
                    <span class="timestamp">等待中...</span>
                    <span class="source-tag system">系統</span>
                    轉錄結果將在此顯示。請開始音頻捕獲。
                </div>
            </div>
        </div>
    </div>

    <script>
        let mediaRecorder = null;
        let audioContext = null;
        let systemAudioStream = null;
        let microphoneStream = null;
        let currentModel = 'base';
        let transcriptionCount = {
            total: 0,
            system: 0,
            microphone: 0,
            file: 0
        };

        // 頁面載入時初始化
        window.onload = function() {
            checkBackendStatus();
        };

        // 檢查後端狀態
        async function checkBackendStatus() {
            try {
                const response = await fetch('http://localhost:9004/health');
                const data = await response.json();
                
                if (data.model_loaded) {
                    updateModelStatus(`✅ 後端服務正常 - ${data.model_name}`);
                } else {
                    updateModelStatus('❌ 模型未加載');
                }
            } catch (error) {
                updateModelStatus('❌ 後端服務連接失敗');
            }
        }

        // 更新模型狀態
        function updateModelStatus(message) {
            document.getElementById('modelStatus').innerHTML = 
                `<div class="status info">${message}</div>`;
        }

        // 切換模型
        async function switchModel(modelName) {
            try {
                const response = await fetch(`http://localhost:9004/models/${modelName}`, {
                    method: 'POST'
                });
                
                const data = await response.json();
                
                if (data.success) {
                    updateModelButtons(modelName);
                    currentModel = modelName;
                    updateModelStatus(`✅ 已切換到 ${modelName} 模型`);
                    updateStats();
                } else {
                    updateModelStatus(`❌ 切換失敗: ${data.message}`);
                }
            } catch (error) {
                updateModelStatus(`❌ 切換失敗: ${error.message}`);
            }
        }

        // 更新模型按鈕狀態
        function updateModelButtons(activeModel) {
            const buttons = document.querySelectorAll('.model-buttons button');
            buttons.forEach(button => {
                button.classList.remove('active');
                if (button.id === `model-${activeModel}`) {
                    button.classList.add('active');
                }
            });
        }

        // 開始系統音頻捕獲
        async function startSystemAudio() {
            try {
                // 使用 getDisplayMedia 捕獲螢幕音頻
                systemAudioStream = await navigator.mediaDevices.getDisplayMedia({
                    video: false,
                    audio: {
                        echoCancellation: false,
                        noiseSuppression: false,
                        sampleRate: 44100
                    }
                });

                // 檢查是否包含音頻軌道
                const audioTracks = systemAudioStream.getAudioTracks();
                if (audioTracks.length === 0) {
                    throw new Error('未捕獲到音頻軌道，請確保選擇了包含音頻的來源');
                }

                // 設置音頻處理
                audioContext = new AudioContext();
                const source = audioContext.createMediaStreamSource(systemAudioStream);
                const processor = audioContext.createScriptProcessor(4096, 1, 1);

                processor.onaudioprocess = function(event) {
                    const audioData = event.inputBuffer.getChannelData(0);
                    processAudioChunk(audioData, 'system');
                };

                source.connect(processor);
                processor.connect(audioContext.destination);

                // 監聽流結束事件
                systemAudioStream.getVideoTracks().forEach(track => {
                    track.onended = () => {
                        stopSystemAudio();
                    };
                });

                document.getElementById('startSystemAudio').disabled = true;
                document.getElementById('stopSystemAudio').disabled = false;
                document.getElementById('startSystemAudio').classList.add('recording');
                
                document.getElementById('audioStatus').innerHTML = 
                    '<div class="status success">🎵 系統音頻捕獲中...</div>';

            } catch (error) {
                document.getElementById('audioStatus').innerHTML = 
                    `<div class="status error">❌ 系統音頻啟動失敗: ${error.message}</div>`;
            }
        }

        // 停止系統音頻捕獲
        function stopSystemAudio() {
            if (systemAudioStream) {
                systemAudioStream.getTracks().forEach(track => track.stop());
                systemAudioStream = null;
            }
            
            if (audioContext) {
                audioContext.close();
                audioContext = null;
            }
            
            document.getElementById('startSystemAudio').disabled = false;
            document.getElementById('stopSystemAudio').disabled = true;
            document.getElementById('startSystemAudio').classList.remove('recording');
            
            document.getElementById('audioStatus').innerHTML = 
                '<div class="status info">系統音頻已停止</div>';
        }

        // 開始麥克風
        async function startMicrophone() {
            try {
                microphoneStream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        sampleRate: 44100
                    }
                });

                audioContext = new AudioContext();
                const source = audioContext.createMediaStreamSource(microphoneStream);
                const processor = audioContext.createScriptProcessor(4096, 1, 1);

                processor.onaudioprocess = function(event) {
                    const audioData = event.inputBuffer.getChannelData(0);
                    processAudioChunk(audioData, 'microphone');
                };

                source.connect(processor);
                processor.connect(audioContext.destination);

                document.getElementById('startMicrophone').disabled = true;
                document.getElementById('stopMicrophone').disabled = false;
                document.getElementById('startMicrophone').classList.add('recording');

            } catch (error) {
                document.getElementById('audioStatus').innerHTML = 
                    `<div class="status error">❌ 麥克風啟動失敗: ${error.message}</div>`;
            }
        }

        // 停止麥克風
        function stopMicrophone() {
            if (microphoneStream) {
                microphoneStream.getTracks().forEach(track => track.stop());
                microphoneStream = null;
            }
            
            document.getElementById('startMicrophone').disabled = false;
            document.getElementById('stopMicrophone').disabled = true;
            document.getElementById('startMicrophone').classList.remove('recording');
        }

        // 處理音頻片段
        let audioBuffer = [];
        let lastProcessTime = 0;

        function processAudioChunk(audioData, source) {
            // 累積音頻數據
            audioBuffer.push(...audioData);
            
            // 每3秒處理一次
            const now = Date.now();
            if (now - lastProcessTime > 3000 && audioBuffer.length > 0) {
                lastProcessTime = now;
                
                // 轉換為 WAV 格式並發送
                const audioBlob = createWavBlob(new Float32Array(audioBuffer), 44100);
                sendAudioForTranscription(audioBlob, source);
                
                // 保留一些重疊
                audioBuffer = audioBuffer.slice(-4096);
            }
        }

        // 創建 WAV Blob
        function createWavBlob(audioData, sampleRate) {
            const length = audioData.length;
            const buffer = new ArrayBuffer(44 + length * 2);
            const view = new DataView(buffer);
            
            // WAV 文件頭
            const writeString = (offset, string) => {
                for (let i = 0; i < string.length; i++) {
                    view.setUint8(offset + i, string.charCodeAt(i));
                }
            };
            
            writeString(0, 'RIFF');
            view.setUint32(4, 36 + length * 2, true);
            writeString(8, 'WAVE');
            writeString(12, 'fmt ');
            view.setUint32(16, 16, true);
            view.setUint16(20, 1, true);
            view.setUint16(22, 1, true);
            view.setUint32(24, sampleRate, true);
            view.setUint32(28, sampleRate * 2, true);
            view.setUint16(32, 2, true);
            view.setUint16(34, 16, true);
            writeString(36, 'data');
            view.setUint32(40, length * 2, true);
            
            // 音頻數據
            let offset = 44;
            for (let i = 0; i < length; i++) {
                const sample = Math.max(-1, Math.min(1, audioData[i]));
                view.setInt16(offset, sample * 0x7FFF, true);
                offset += 2;
            }
            
            return new Blob([buffer], { type: 'audio/wav' });
        }

        // 發送音頻進行轉錄
        async function sendAudioForTranscription(audioBlob, source) {
            try {
                const formData = new FormData();
                formData.append('audio_file', audioBlob, 'audio.wav');

                const response = await fetch('http://localhost:9004/api/transcribe', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const data = await response.json();
                
                if (data.transcription && data.transcription.trim()) {
                    addTranscription(data.transcription, source, data.model_name);
                }
            } catch (error) {
                console.error('轉錄錯誤:', error);
            }
        }

        // 文件上傳轉錄
        async function uploadFile() {
            const fileInput = document.getElementById('audioFile');
            if (!fileInput.files[0]) {
                alert('請選擇音頻文件');
                return;
            }

            const file = fileInput.files[0];
            const formData = new FormData();
            formData.append('audio_file', file);

            try {
                const response = await fetch('http://localhost:9004/api/transcribe', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();
                addTranscription(data.transcription, 'file', data.model_name);
                
            } catch (error) {
                alert(`轉錄失敗: ${error.message}`);
            }
        }

        // 添加轉錄結果
        function addTranscription(text, source, modelName) {
            const display = document.getElementById('transcriptionDisplay');
            const timestamp = new Date().toLocaleTimeString();
            const sourceText = {
                'system': '系統音頻',
                'microphone': '麥克風',
                'file': '文件'
            }[source] || source;

            const item = document.createElement('div');
            item.className = `transcription-item ${source}`;
            item.innerHTML = `
                <span class="timestamp">${timestamp}</span>
                <span class="source-tag ${source}">${sourceText}</span>
                <span style="font-size: 11px; color: #666;">[${modelName || currentModel}]</span>
                <div style="margin-top: 5px; font-size: 16px;">${text}</div>
            `;

            display.appendChild(item);
            display.scrollTop = display.scrollHeight;

            // 更新統計
            transcriptionCount.total++;
            transcriptionCount[source]++;
            updateStats();
        }

        // 更新統計信息
        function updateStats() {
            document.getElementById('totalTranscriptions').textContent = transcriptionCount.total;
            document.getElementById('systemTranscriptions').textContent = transcriptionCount.system;
            document.getElementById('microphoneTranscriptions').textContent = transcriptionCount.microphone;
            document.getElementById('fileTranscriptions').textContent = transcriptionCount.file;
            document.getElementById('currentModel').textContent = currentModel;
        }

        // 清空轉錄記錄
        function clearTranscriptions() {
            document.getElementById('transcriptionDisplay').innerHTML = '';
            transcriptionCount = { total: 0, system: 0, microphone: 0, file: 0 };
            updateStats();
        }

        // 匯出轉錄記錄
        function exportTranscriptions() {
            const display = document.getElementById('transcriptionDisplay');
            const items = display.querySelectorAll('.transcription-item');

            let exportText = '轉錄記錄匯出\n';
            exportText += '================\n\n';

            items.forEach(item => {
                const timestamp = item.querySelector('.timestamp').textContent;
                const source = item.querySelector('.source-tag').textContent;
                const textDiv = item.querySelector('div');
                const text = textDiv ? textDiv.textContent : '';
                exportText += `${timestamp} [${source}] ${text}\n\n`;
            });

            const blob = new Blob([exportText], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `transcription_${new Date().toISOString().slice(0, 19)}.txt`;
            a.click();
            URL.revokeObjectURL(url);
        }

        // 複製全部轉錄
        function copyAllTranscriptions() {
            const display = document.getElementById('transcriptionDisplay');
            const items = display.querySelectorAll('.transcription-item');

            let copyText = '';
            items.forEach(item => {
                const textDiv = item.querySelector('div');
                const text = textDiv ? textDiv.textContent : '';
                if (text) {
                    copyText += text + '\n';
                }
            });

            navigator.clipboard.writeText(copyText).then(() => {
                alert('轉錄內容已複製到剪貼板');
            });
        }
    </script>
</body>
</html>
