<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OpenAI Whisper 模型選擇測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #1976d2;
            text-align: center;
        }
        .section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background-color: #1976d2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #1565c0;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        button.active {
            background-color: #4caf50;
        }
        textarea {
            width: 100%;
            height: 200px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: monospace;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        .status.warning {
            background-color: #fff3cd;
            color: #856404;
        }
        input[type="file"] {
            margin: 10px 0;
        }
        .model-selector {
            background-color: #e3f2fd;
            border: 2px solid #2196f3;
            padding: 20px;
            margin-bottom: 20px;
        }
        .model-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 10px 0;
        }
        .model-info {
            background-color: #f0f0f0;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        .performance-info {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 10px;
            margin: 10px 0;
        }
        .perf-card {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 OpenAI Whisper 模型選擇測試</h1>
        
        <!-- 模型選擇區域 -->
        <div class="model-selector">
            <h3>🔧 模型選擇</h3>
            <p>選擇不同大小的 Whisper 模型進行轉錄：</p>
            
            <div class="model-buttons">
                <button id="model-tiny" onclick="switchModel('tiny')">Tiny (39 MB)</button>
                <button id="model-base" onclick="switchModel('base')" class="active">Base (142 MB)</button>
                <button id="model-small" onclick="switchModel('small')">Small (461 MB)</button>
                <button id="model-medium" onclick="switchModel('medium')">Medium (1.5 GB)</button>
                <button id="model-large" onclick="switchModel('large')">Large (2.9 GB)</button>
            </div>
            
            <div id="modelStatus" class="status info">
                當前模型: Base | 設備: 檢查中... | 狀態: 初始化中
            </div>
            
            <div class="model-info">
                <strong>模型說明：</strong><br>
                • Tiny: 最快，質量較低，適合實時應用<br>
                • Base: 平衡速度和質量，推薦日常使用<br>
                • Small: 較好質量，中等速度<br>
                • Medium: 高質量，較慢速度<br>
                • Large: 最高質量，最慢速度
            </div>
        </div>

        <!-- 後端狀態檢查 -->
        <div class="section">
            <h3>📊 系統狀態</h3>
            <button onclick="checkBackendStatus()">檢查系統狀態</button>
            <button onclick="getModelInfo()">獲取模型信息</button>
            <div id="backendStatus"></div>
        </div>

        <!-- 文件上傳測試 -->
        <div class="section">
            <h3>📁 文件轉錄測試</h3>
            <input type="file" id="audioFile" accept="audio/*">
            <button onclick="uploadFile()">上傳並轉錄</button>
            <div id="uploadStatus"></div>
            
            <div class="performance-info">
                <div class="perf-card">
                    <strong>處理時間</strong>
                    <div id="processingTime">-</div>
                </div>
                <div class="perf-card">
                    <strong>音頻時長</strong>
                    <div id="audioDuration">-</div>
                </div>
                <div class="perf-card">
                    <strong>速度比率</strong>
                    <div id="speedRatio">-</div>
                </div>
            </div>
            
            <textarea id="uploadResult" placeholder="轉錄結果將顯示在此..."></textarea>
        </div>

        <!-- 實時錄音測試 -->
        <div class="section">
            <h3>🎙️ 實時錄音測試</h3>
            <button id="startRecord" onclick="startRecording()">開始錄音</button>
            <button id="stopRecord" onclick="stopRecording()" disabled>停止錄音</button>
            <div id="recordStatus"></div>
            <textarea id="recordResult" placeholder="實時轉錄結果將顯示在此..."></textarea>
        </div>
    </div>

    <script>
        let mediaRecorder;
        let recordedChunks = [];
        let currentModel = 'base';

        // 頁面載入時初始化
        window.onload = function() {
            checkBackendStatus();
            getModelInfo();
        };

        // 檢查後端狀態
        async function checkBackendStatus() {
            const statusDiv = document.getElementById('backendStatus');
            try {
                const response = await fetch('http://localhost:9004/health', {
                    method: 'GET',
                    mode: 'cors',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                statusDiv.innerHTML = `
                    <div class="status success">
                        ✅ 後端服務正常<br>
                        模型已載入: ${data.model_loaded ? '是' : '否'}<br>
                        當前模型: ${data.model_name}<br>
                        設備: ${data.device}<br>
                        GPU 信息: ${data.gpu_info || '無'}<br>
                        時間: ${new Date(data.timestamp).toLocaleString()}
                    </div>
                `;
                
                // 更新模型狀態
                updateModelStatus(data.model_name, data.device, '運行中');
                
            } catch (error) {
                console.error('Backend status check failed:', error);
                statusDiv.innerHTML = `
                    <div class="status error">
                        ❌ 後端服務連接失敗<br>
                        錯誤: ${error.message}<br>
                        請確認後端服務運行在 http://localhost:9004
                    </div>
                `;
            }
        }

        // 獲取模型信息
        async function getModelInfo() {
            try {
                const response = await fetch('http://localhost:9004/models', {
                    method: 'GET',
                    mode: 'cors',
                });
                
                if (response.ok) {
                    const data = await response.json();
                    console.log('模型信息:', data);
                    
                    // 更新當前模型按鈕狀態
                    updateModelButtons(data.current_model);
                    currentModel = data.current_model;
                }
            } catch (error) {
                console.error('獲取模型信息失敗:', error);
            }
        }

        // 切換模型
        async function switchModel(modelName) {
            const statusDiv = document.getElementById('modelStatus');
            
            // 更新狀態為載入中
            statusDiv.innerHTML = `<div class="status warning">🔄 正在切換到 ${modelName} 模型...</div>`;
            
            try {
                const response = await fetch(`http://localhost:9004/models/${modelName}`, {
                    method: 'POST',
                    mode: 'cors',
                });
                
                const data = await response.json();
                
                if (data.success) {
                    statusDiv.innerHTML = `<div class="status success">✅ ${data.message}</div>`;
                    updateModelButtons(modelName);
                    currentModel = modelName;
                } else {
                    statusDiv.innerHTML = `<div class="status error">❌ ${data.message}</div>`;
                }
                
            } catch (error) {
                console.error('模型切換失敗:', error);
                statusDiv.innerHTML = `<div class="status error">❌ 模型切換失敗: ${error.message}</div>`;
            }
        }

        // 更新模型按鈕狀態
        function updateModelButtons(activeModel) {
            const buttons = document.querySelectorAll('.model-buttons button');
            buttons.forEach(button => {
                button.classList.remove('active');
                if (button.id === `model-${activeModel}`) {
                    button.classList.add('active');
                }
            });
        }

        // 更新模型狀態顯示
        function updateModelStatus(modelName, device, status) {
            const statusDiv = document.getElementById('modelStatus');
            statusDiv.innerHTML = `
                <div class="status info">
                    當前模型: ${modelName} | 設備: ${device} | 狀態: ${status}
                </div>
            `;
        }

        // 文件上傳
        async function uploadFile() {
            const fileInput = document.getElementById('audioFile');
            const statusDiv = document.getElementById('uploadStatus');
            const resultArea = document.getElementById('uploadResult');

            if (!fileInput.files[0]) {
                statusDiv.innerHTML = '<div class="status error">請選擇音頻文件</div>';
                return;
            }

            const file = fileInput.files[0];
            console.log('上傳文件:', file.name, file.type, file.size);

            const formData = new FormData();
            formData.append('audio_file', file);

            statusDiv.innerHTML = '<div class="status info">正在轉錄...</div>';
            resultArea.value = '';

            const startTime = Date.now();

            try {
                const response = await fetch('http://localhost:9004/api/transcribe', {
                    method: 'POST',
                    mode: 'cors',
                    body: formData
                });

                const endTime = Date.now();
                const totalTime = (endTime - startTime) / 1000;

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }

                const data = await response.json();
                
                statusDiv.innerHTML = '<div class="status success">轉錄完成</div>';
                resultArea.value = data.transcription || '轉錄結果為空';
                
                // 更新性能信息
                document.getElementById('processingTime').textContent = `${data.processing_time.toFixed(2)}s`;
                document.getElementById('audioDuration').textContent = `${totalTime.toFixed(2)}s`;
                
                const speedRatio = totalTime / data.processing_time;
                document.getElementById('speedRatio').textContent = `${speedRatio.toFixed(1)}x`;
                
                console.log('轉錄結果:', data);
                
            } catch (error) {
                console.error('Upload error:', error);
                statusDiv.innerHTML = `<div class="status error">請求失敗: ${error.message}</div>`;
            }
        }

        // 實時錄音功能
        async function startRecording() {
            const statusDiv = document.getElementById('recordStatus');
            const resultArea = document.getElementById('recordResult');

            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                
                mediaRecorder = new MediaRecorder(stream);
                recordedChunks = [];

                mediaRecorder.ondataavailable = (event) => {
                    if (event.data.size > 0) {
                        recordedChunks.push(event.data);
                        sendAudioChunk(event.data);
                    }
                };

                mediaRecorder.start(3000); // 每3秒處理一次
                
                document.getElementById('startRecord').disabled = true;
                document.getElementById('stopRecord').disabled = false;
                
                statusDiv.innerHTML = '<div class="status info">正在錄音...</div>';
                resultArea.value = '';

            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">無法訪問麥克風: ${error.message}</div>`;
            }
        }

        function stopRecording() {
            if (mediaRecorder && mediaRecorder.state !== 'inactive') {
                mediaRecorder.stop();
                mediaRecorder.stream.getTracks().forEach(track => track.stop());
            }
            
            document.getElementById('startRecord').disabled = false;
            document.getElementById('stopRecord').disabled = true;
            document.getElementById('recordStatus').innerHTML = '<div class="status success">錄音已停止</div>';
        }

        async function sendAudioChunk(audioBlob) {
            const formData = new FormData();
            formData.append('audio_file', audioBlob, 'audio.webm');

            try {
                const response = await fetch('http://localhost:9004/api/transcribe', {
                    method: 'POST',
                    mode: 'cors',
                    body: formData
                });

                if (!response.ok) {
                    console.error('Transcription failed:', response.status);
                    return;
                }

                const data = await response.json();
                
                if (data.transcription && data.transcription.trim()) {
                    const resultArea = document.getElementById('recordResult');
                    const timestamp = new Date().toLocaleTimeString();
                    resultArea.value += `[${timestamp}] [${currentModel}] ${data.transcription}\n`;
                    resultArea.scrollTop = resultArea.scrollHeight;
                }
            } catch (error) {
                console.error('轉錄錯誤:', error);
            }
        }
    </script>
</body>
</html>
