# HTTPS 伺服器：使用自簽名憑證反向代理至 Flask 應用程式
server {
    listen 443 ssl;
    server_name localhost;

    ssl_certificate /etc/nginx/certs/selfsigned.crt;
    ssl_certificate_key /etc/nginx/certs/selfsigned.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;

    # 增加允許的上傳大小，這裡設定為 100 MB
    client_max_body_size 100M;


    location / {
        proxy_pass http://app:9004;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}

# HTTP 伺服器：將所有請求導向 HTTPS
server {
    listen 80;
    server_name localhost;
    return 301 https://$host$request_uri;
}

