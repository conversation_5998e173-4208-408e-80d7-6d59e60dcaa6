<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>語音轉文字測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1976d2;
            text-align: center;
        }
        .section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background-color: #1976d2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #1565c0;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        textarea {
            width: 100%;
            height: 200px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: monospace;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        input[type="file"] {
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>語音轉文字測試系統</h1>
        
        <!-- 後端狀態檢查 -->
        <div class="section">
            <h3>後端服務狀態</h3>
            <button onclick="checkBackendStatus()">檢查後端狀態</button>
            <div id="backendStatus"></div>
        </div>

        <!-- 文件上傳測試 -->
        <div class="section">
            <h3>文件上傳轉錄測試</h3>
            <input type="file" id="audioFile" accept="audio/*">
            <button onclick="uploadFile()">上傳並轉錄</button>
            <div id="uploadStatus"></div>
            <textarea id="uploadResult" placeholder="轉錄結果將顯示在此..."></textarea>
        </div>

        <!-- 實時錄音測試 -->
        <div class="section">
            <h3>實時錄音測試</h3>
            <button id="startRecord" onclick="startRecording()">開始錄音</button>
            <button id="stopRecord" onclick="stopRecording()" disabled>停止錄音</button>
            <div id="recordStatus"></div>
            <textarea id="recordResult" placeholder="實時轉錄結果將顯示在此..."></textarea>
        </div>
    </div>

    <script>
        let mediaRecorder;
        let recordedChunks = [];
        let socket;

        // 檢查後端狀態
        async function checkBackendStatus() {
            const statusDiv = document.getElementById('backendStatus');
            try {
                const response = await fetch('http://localhost:9004/health');
                const data = await response.json();
                statusDiv.innerHTML = `
                    <div class="status success">
                        ✅ 後端服務正常<br>
                        模型已載入: ${data.model_loaded ? '是' : '否'}<br>
                        時間: ${new Date(data.timestamp).toLocaleString()}
                    </div>
                `;
            } catch (error) {
                statusDiv.innerHTML = `
                    <div class="status error">
                        ❌ 後端服務連接失敗<br>
                        錯誤: ${error.message}
                    </div>
                `;
            }
        }

        // 文件上傳
        async function uploadFile() {
            const fileInput = document.getElementById('audioFile');
            const statusDiv = document.getElementById('uploadStatus');
            const resultArea = document.getElementById('uploadResult');

            if (!fileInput.files[0]) {
                statusDiv.innerHTML = '<div class="status error">請選擇音頻文件</div>';
                return;
            }

            const formData = new FormData();
            formData.append('audio_file', fileInput.files[0]);

            statusDiv.innerHTML = '<div class="status info">正在轉錄...</div>';
            resultArea.value = '';

            try {
                const response = await fetch('http://localhost:9004/api/transcribe', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();
                
                if (response.ok) {
                    statusDiv.innerHTML = '<div class="status success">轉錄完成</div>';
                    resultArea.value = data.transcription;
                } else {
                    statusDiv.innerHTML = `<div class="status error">轉錄失敗: ${data.error}</div>`;
                }
            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">請求失敗: ${error.message}</div>`;
            }
        }

        // 開始錄音
        async function startRecording() {
            const statusDiv = document.getElementById('recordStatus');
            const resultArea = document.getElementById('recordResult');

            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                
                mediaRecorder = new MediaRecorder(stream);
                recordedChunks = [];

                mediaRecorder.ondataavailable = (event) => {
                    if (event.data.size > 0) {
                        recordedChunks.push(event.data);
                        // 發送音頻數據到後端進行轉錄
                        sendAudioChunk(event.data);
                    }
                };

                mediaRecorder.start(2000); // 每2秒產生一個數據塊
                
                document.getElementById('startRecord').disabled = true;
                document.getElementById('stopRecord').disabled = false;
                
                statusDiv.innerHTML = '<div class="status info">正在錄音...</div>';
                resultArea.value = '';

            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">無法訪問麥克風: ${error.message}</div>`;
            }
        }

        // 停止錄音
        function stopRecording() {
            if (mediaRecorder && mediaRecorder.state !== 'inactive') {
                mediaRecorder.stop();
                mediaRecorder.stream.getTracks().forEach(track => track.stop());
            }
            
            document.getElementById('startRecord').disabled = false;
            document.getElementById('stopRecord').disabled = true;
            document.getElementById('recordStatus').innerHTML = '<div class="status success">錄音已停止</div>';
        }

        // 發送音頻塊到後端
        async function sendAudioChunk(audioBlob) {
            const formData = new FormData();
            formData.append('audio_file', audioBlob, 'audio.webm');

            try {
                const response = await fetch('http://localhost:9004/api/transcribe', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();
                
                if (response.ok && data.transcription.trim()) {
                    const resultArea = document.getElementById('recordResult');
                    resultArea.value += data.transcription + '\n';
                    resultArea.scrollTop = resultArea.scrollHeight;
                }
            } catch (error) {
                console.error('轉錄錯誤:', error);
            }
        }

        // 頁面載入時檢查後端狀態
        window.onload = function() {
            checkBackendStatus();
        };
    </script>
</body>
</html>
