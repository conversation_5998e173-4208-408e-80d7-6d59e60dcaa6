<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增強版語音轉文字測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #1976d2;
            text-align: center;
        }
        .section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background-color: #1976d2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #1565c0;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        textarea {
            width: 100%;
            height: 200px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: monospace;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        input[type="file"] {
            margin: 10px 0;
        }
        .youtube-section {
            background-color: #fff3e0;
            border: 2px solid #ff9800;
        }
        .youtube-input {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .subtitle-overlay {
            position: fixed;
            bottom: 100px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            z-index: 9999;
            max-width: 80%;
            text-align: center;
            display: none;
        }
        .tab-buttons {
            display: flex;
            margin-bottom: 20px;
        }
        .tab-button {
            flex: 1;
            padding: 15px;
            background: #f0f0f0;
            border: 1px solid #ddd;
            cursor: pointer;
            text-align: center;
        }
        .tab-button.active {
            background: #1976d2;
            color: white;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 增強版語音轉文字測試系統</h1>
        
        <!-- 標籤切換 -->
        <div class="tab-buttons">
            <div class="tab-button active" onclick="switchTab('basic')">基礎測試</div>
            <div class="tab-button" onclick="switchTab('youtube')">YouTube 字幕</div>
            <div class="tab-button" onclick="switchTab('realtime')">實時錄音</div>
        </div>

        <!-- 基礎測試標籤 -->
        <div id="basic" class="tab-content active">
            <!-- 後端狀態檢查 -->
            <div class="section">
                <h3>後端服務狀態</h3>
                <button onclick="checkBackendStatus()">檢查後端狀態</button>
                <div id="backendStatus"></div>
            </div>

            <!-- 文件上傳測試 -->
            <div class="section">
                <h3>文件上傳轉錄測試</h3>
                <input type="file" id="audioFile" accept="audio/*">
                <button onclick="uploadFile()">上傳並轉錄</button>
                <div id="uploadStatus"></div>
                <textarea id="uploadResult" placeholder="轉錄結果將顯示在此..."></textarea>
            </div>
        </div>

        <!-- YouTube 字幕標籤 -->
        <div id="youtube" class="tab-content">
            <div class="section youtube-section">
                <h3>🎬 YouTube 影片字幕測試</h3>
                <p><strong>使用說明：</strong></p>
                <ol>
                    <li>在下方輸入 YouTube 影片 URL</li>
                    <li>點擊「載入影片」</li>
                    <li>點擊「開始字幕」啟動音頻捕獲</li>
                    <li>播放影片，字幕會顯示在影片下方</li>
                </ol>
                
                <input type="url" id="youtubeUrl" class="youtube-input" 
                       placeholder="輸入 YouTube 影片 URL (例如: https://www.youtube.com/watch?v=...)">
                <br>
                <button onclick="loadYouTubeVideo()">載入影片</button>
                <button id="startYouTubeCapture" onclick="startYouTubeCapture()" disabled>開始字幕</button>
                <button id="stopYouTubeCapture" onclick="stopYouTubeCapture()" disabled>停止字幕</button>
                
                <div id="youtubeStatus"></div>
                
                <!-- YouTube 影片嵌入區域 -->
                <div id="youtubePlayer" style="margin: 20px 0;"></div>
                
                <!-- 字幕顯示區域 -->
                <div id="youtubeSubtitles" style="background: #000; color: #fff; padding: 10px; min-height: 50px; border-radius: 5px; margin: 10px 0;">
                    字幕將顯示在此處...
                </div>
            </div>
        </div>

        <!-- 實時錄音標籤 -->
        <div id="realtime" class="tab-content">
            <div class="section">
                <h3>實時錄音測試</h3>
                <button id="startRecord" onclick="startRecording()">開始錄音</button>
                <button id="stopRecord" onclick="stopRecording()" disabled>停止錄音</button>
                <div id="recordStatus"></div>
                <textarea id="recordResult" placeholder="實時轉錄結果將顯示在此..."></textarea>
            </div>
        </div>
    </div>

    <!-- 浮動字幕覆蓋層 -->
    <div id="subtitleOverlay" class="subtitle-overlay"></div>

    <script>
        let mediaRecorder;
        let recordedChunks = [];
        let youtubeAudioContext;
        let youtubeMediaRecorder;
        let youtubeStream;

        // 標籤切換功能
        function switchTab(tabName) {
            // 隱藏所有標籤內容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });
            
            // 顯示選中的標籤
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        // 檢查後端狀態
        async function checkBackendStatus() {
            const statusDiv = document.getElementById('backendStatus');
            try {
                const response = await fetch('http://localhost:9004/health', {
                    method: 'GET',
                    mode: 'cors',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                statusDiv.innerHTML = `
                    <div class="status success">
                        ✅ 後端服務正常<br>
                        模型已載入: ${data.model_loaded ? '是' : '否'}<br>
                        時間: ${new Date(data.timestamp).toLocaleString()}
                    </div>
                `;
            } catch (error) {
                console.error('Backend status check failed:', error);
                statusDiv.innerHTML = `
                    <div class="status error">
                        ❌ 後端服務連接失敗<br>
                        錯誤: ${error.message}<br>
                        請確認後端服務運行在 http://localhost:9004
                    </div>
                `;
            }
        }

        // 文件上傳
        async function uploadFile() {
            const fileInput = document.getElementById('audioFile');
            const statusDiv = document.getElementById('uploadStatus');
            const resultArea = document.getElementById('uploadResult');

            if (!fileInput.files[0]) {
                statusDiv.innerHTML = '<div class="status error">請選擇音頻文件</div>';
                return;
            }

            const file = fileInput.files[0];
            console.log('上傳文件:', file.name, file.type, file.size);

            const formData = new FormData();
            formData.append('audio_file', file);

            statusDiv.innerHTML = '<div class="status info">正在轉錄...</div>';
            resultArea.value = '';

            try {
                const response = await fetch('http://localhost:9004/api/transcribe', {
                    method: 'POST',
                    mode: 'cors',
                    body: formData
                });

                console.log('Response status:', response.status);

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }

                const data = await response.json();
                console.log('Response data:', data);
                
                statusDiv.innerHTML = '<div class="status success">轉錄完成</div>';
                resultArea.value = data.transcription || data.text || '轉錄結果為空';
                
            } catch (error) {
                console.error('Upload error:', error);
                statusDiv.innerHTML = `<div class="status error">請求失敗: ${error.message}</div>`;
            }
        }

        // YouTube 功能
        function loadYouTubeVideo() {
            const url = document.getElementById('youtubeUrl').value;
            const statusDiv = document.getElementById('youtubeStatus');
            const playerDiv = document.getElementById('youtubePlayer');

            if (!url) {
                statusDiv.innerHTML = '<div class="status error">請輸入 YouTube URL</div>';
                return;
            }

            try {
                // 提取 YouTube 影片 ID
                const videoId = extractYouTubeVideoId(url);
                if (!videoId) {
                    throw new Error('無效的 YouTube URL');
                }

                // 創建嵌入式播放器
                playerDiv.innerHTML = `
                    <iframe width="100%" height="400" 
                            src="https://www.youtube.com/embed/${videoId}?enablejsapi=1" 
                            frameborder="0" allowfullscreen>
                    </iframe>
                `;

                statusDiv.innerHTML = '<div class="status success">影片載入成功！現在可以開始字幕功能。</div>';
                document.getElementById('startYouTubeCapture').disabled = false;

            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">載入失敗: ${error.message}</div>`;
            }
        }

        function extractYouTubeVideoId(url) {
            const regex = /(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/;
            const match = url.match(regex);
            return match ? match[1] : null;
        }

        async function startYouTubeCapture() {
            const statusDiv = document.getElementById('youtubeStatus');

            try {
                // 方法1: 嘗試使用 getDisplayMedia 捕獲螢幕和音頻
                try {
                    youtubeStream = await navigator.mediaDevices.getDisplayMedia({
                        video: true,  // 需要包含視頻才能捕獲音頻
                        audio: true
                    });
                    statusDiv.innerHTML = '<div class="status info">✅ 使用螢幕捕獲模式（請選擇包含音頻的標籤頁或螢幕）</div>';
                } catch (displayError) {
                    // 方法2: 回退到麥克風捕獲
                    console.log('Display capture failed, falling back to microphone:', displayError);
                    youtubeStream = await navigator.mediaDevices.getUserMedia({
                        audio: {
                            echoCancellation: false,
                            noiseSuppression: false,
                            autoGainControl: false
                        }
                    });
                    statusDiv.innerHTML = '<div class="status info">⚠️ 使用麥克風模式（請確保音量適中避免回音）</div>';
                }

                youtubeMediaRecorder = new MediaRecorder(youtubeStream, {
                    mimeType: MediaRecorder.isTypeSupported('audio/webm;codecs=opus')
                        ? 'audio/webm;codecs=opus'
                        : 'audio/webm'
                });

                youtubeMediaRecorder.ondataavailable = async (event) => {
                    if (event.data && event.data.size > 0) {
                        await sendYouTubeAudioChunk(event.data);
                    }
                };

                youtubeMediaRecorder.onerror = (event) => {
                    console.error('MediaRecorder error:', event.error);
                    statusDiv.innerHTML = `<div class="status error">錄製錯誤: ${event.error}</div>`;
                };

                youtubeMediaRecorder.start(3000); // 每3秒處理一次

                document.getElementById('startYouTubeCapture').disabled = true;
                document.getElementById('stopYouTubeCapture').disabled = false;

                const currentStatus = statusDiv.innerHTML;
                statusDiv.innerHTML = currentStatus + '<br>🎤 正在捕獲音頻並生成字幕...';

            } catch (error) {
                console.error('Audio capture error:', error);
                statusDiv.innerHTML = `
                    <div class="status error">
                        音頻捕獲失敗: ${error.message}<br>
                        <small>請嘗試以下解決方案：<br>
                        1. 使用 Chrome 瀏覽器<br>
                        2. 確保已授權麥克風權限<br>
                        3. 嘗試重新載入頁面</small>
                    </div>
                `;
            }
        }

        function stopYouTubeCapture() {
            if (youtubeMediaRecorder) {
                youtubeMediaRecorder.stop();
            }
            if (youtubeStream) {
                youtubeStream.getTracks().forEach(track => track.stop());
            }
            
            document.getElementById('startYouTubeCapture').disabled = false;
            document.getElementById('stopYouTubeCapture').disabled = true;
            document.getElementById('youtubeStatus').innerHTML = '<div class="status info">字幕已停止</div>';
        }

        async function sendYouTubeAudioChunk(audioBlob) {
            const formData = new FormData();
            formData.append('audio_file', audioBlob, 'youtube_audio.webm');

            try {
                const response = await fetch('http://localhost:9004/api/transcribe', {
                    method: 'POST',
                    mode: 'cors',
                    body: formData
                });

                if (!response.ok) {
                    console.error('YouTube transcription failed:', response.status);
                    return;
                }

                const data = await response.json();
                
                if (data.transcription && data.transcription.trim()) {
                    const subtitleDiv = document.getElementById('youtubeSubtitles');
                    const timestamp = new Date().toLocaleTimeString();
                    subtitleDiv.innerHTML = `[${timestamp}] ${data.transcription}`;
                    
                    // 同時顯示在浮動覆蓋層
                    showFloatingSubtitle(data.transcription);
                }
            } catch (error) {
                console.error('YouTube transcription error:', error);
            }
        }

        function showFloatingSubtitle(text) {
            const overlay = document.getElementById('subtitleOverlay');
            overlay.textContent = text;
            overlay.style.display = 'block';
            
            // 5秒後自動隱藏
            setTimeout(() => {
                overlay.style.display = 'none';
            }, 5000);
        }

        // 實時錄音功能（保持原有功能）
        async function startRecording() {
            const statusDiv = document.getElementById('recordStatus');
            const resultArea = document.getElementById('recordResult');

            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                
                mediaRecorder = new MediaRecorder(stream);
                recordedChunks = [];

                mediaRecorder.ondataavailable = (event) => {
                    if (event.data.size > 0) {
                        recordedChunks.push(event.data);
                        sendAudioChunk(event.data);
                    }
                };

                mediaRecorder.start(2000);
                
                document.getElementById('startRecord').disabled = true;
                document.getElementById('stopRecord').disabled = false;
                
                statusDiv.innerHTML = '<div class="status info">正在錄音...</div>';
                resultArea.value = '';

            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">無法訪問麥克風: ${error.message}</div>`;
            }
        }

        function stopRecording() {
            if (mediaRecorder && mediaRecorder.state !== 'inactive') {
                mediaRecorder.stop();
                mediaRecorder.stream.getTracks().forEach(track => track.stop());
            }
            
            document.getElementById('startRecord').disabled = false;
            document.getElementById('stopRecord').disabled = true;
            document.getElementById('recordStatus').innerHTML = '<div class="status success">錄音已停止</div>';
        }

        async function sendAudioChunk(audioBlob) {
            const formData = new FormData();
            formData.append('audio_file', audioBlob, 'audio.webm');

            try {
                const response = await fetch('http://localhost:9004/api/transcribe', {
                    method: 'POST',
                    mode: 'cors',
                    body: formData
                });

                if (!response.ok) {
                    console.error('Transcription failed:', response.status);
                    return;
                }

                const data = await response.json();
                
                if (data.transcription && data.transcription.trim()) {
                    const resultArea = document.getElementById('recordResult');
                    const timestamp = new Date().toLocaleTimeString();
                    resultArea.value += `[${timestamp}] ${data.transcription}\n`;
                    resultArea.scrollTop = resultArea.scrollHeight;
                }
            } catch (error) {
                console.error('轉錄錯誤:', error);
            }
        }

        // 頁面載入時檢查後端狀態
        window.onload = function() {
            checkBackendStatus();
        };
    </script>
</body>
</html>
