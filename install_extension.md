# 🎤 實時語音轉文字字幕系統 - 完整安裝指南

## 🚀 快速開始

### 第一步：確保後端服務運行

您的後端服務應該已經在運行中。如果沒有，請執行：

```bash
cd /home/<USER>/桌面/LLM_RAG/stt_project/app
python app.py
```

確認看到類似輸出：
```
INFO:__main__:正在載入 Whisper 模型: base (設備: cpu)
INFO:__main__:Whisper 模型載入成功
INFO:__main__:啟動 Flask-SocketIO 服務器...
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:9004
 * Running on http://[::1]:9004
```

### 第二步：安裝 Chrome 擴展

1. **打開 Chrome 擴展管理頁面**
   - 在地址欄輸入：`chrome://extensions/`
   - 或按 `Ctrl+Shift+Delete` → 更多工具 → 擴展程序

2. **啟用開發者模式**
   - 點擊右上角的「開發者模式」開關

3. **載入擴展**
   - 點擊「載入未封裝項目」
   - 導航到：`/home/<USER>/桌面/LLM_RAG/stt_project/browser-extension`
   - 選擇該資料夾並點擊「選擇資料夾」

4. **確認安裝成功**
   - 擴展列表中出現「實時語音轉文字字幕」
   - 瀏覽器工具欄出現麥克風圖標 🎤

## 🎯 立即測試

### YouTube 影片字幕測試

1. **打開 YouTube**
   - 訪問：https://www.youtube.com
   - 播放任意影片

2. **啟動字幕**
   - 點擊瀏覽器工具欄的 🎤 圖標
   - 在彈出窗口中點擊「開始字幕」
   - 授權音頻捕獲權限（選擇當前標籤頁）

3. **查看效果**
   - 字幕會出現在影片底部
   - 即時顯示語音轉文字結果

### Google Meet 會議字幕測試

1. **加入會議**
   - 訪問：https://meet.google.com
   - 加入或創建會議

2. **啟動字幕**
   - 點擊擴展圖標
   - 開始字幕功能
   - 授權音頻權限

3. **測試效果**
   - 說話時會看到即時字幕
   - 字幕位置自動適配會議界面

## ⚙️ 自定義設置

### 訪問設置頁面

1. 點擊擴展圖標
2. 點擊「設置」按鈕
3. 或右鍵擴展圖標 → 選項

### 推薦設置

**字幕外觀：**
- 位置：底部
- 大小：中
- 顏色：白色 (#ffffff)
- 背景：黑色 (#000000)，透明度 80%

**行為設置：**
- ✅ 顯示通知
- ❌ 自動啟動（建議手動控制）

**後端設置：**
- URL：http://localhost:9004
- 語言：中文 (zh)

## 🔧 故障排除

### 問題 1：擴展無法載入

**症狀：** 載入擴展時出現錯誤

**解決方案：**
```bash
# 檢查文件完整性
ls -la /home/<USER>/桌面/LLM_RAG/stt_project/browser-extension/
# 應該看到：manifest.json, background.js, content.js 等文件
```

### 問題 2：無法捕獲音頻

**症狀：** 點擊開始字幕後沒有反應

**解決方案：**
1. 確保網站使用 HTTPS 或 localhost
2. 檢查 Chrome 權限設置
3. 重新載入擴展

### 問題 3：後端連接失敗

**症狀：** 顯示「後端服務未連接」

**解決方案：**
```bash
# 測試後端連接
curl http://localhost:9004/health

# 應該返回類似：
# {"status":"healthy","model_loaded":true,"timestamp":"..."}
```

### 問題 4：字幕不顯示

**症狀：** 音頻捕獲成功但沒有字幕

**檢查步驟：**
1. 確認音頻有聲音
2. 檢查瀏覽器控制台錯誤 (F12)
3. 查看擴展背景頁日誌

## 📱 支持的平台

| 平台 | 狀態 | 測試建議 |
|------|------|----------|
| YouTube | ✅ 完全支持 | 播放中文/英文影片測試 |
| Google Meet | ✅ 完全支持 | 創建測試會議 |
| Zoom Web | ✅ 完全支持 | 使用瀏覽器版本 |
| Teams Web | ✅ 完全支持 | 使用瀏覽器版本 |

## 🎉 使用技巧

### 最佳實踐

1. **音質優化**
   - 使用耳機避免回音
   - 確保環境安靜
   - 說話清晰

2. **性能優化**
   - 關閉不必要的標籤頁
   - 確保網絡穩定
   - 定期重啟瀏覽器

3. **隱私保護**
   - 音頻數據僅發送到本地後端
   - 不會上傳到外部服務器
   - 可隨時停止捕獲

### 快捷操作

- **快速開始**：點擊擴展圖標 → 開始字幕
- **快速停止**：再次點擊擴展圖標 → 停止字幕
- **調整樣式**：擴展彈窗中的快速設置
- **詳細設置**：右鍵擴展圖標 → 選項

## 📞 獲取幫助

如果遇到問題：

1. **查看日誌**
   - 擴展管理頁面 → 詳細信息 → 檢查視圖
   - 瀏覽器控制台 (F12)

2. **重置設置**
   - 設置頁面 → 重置為預設值

3. **重新安裝**
   - 移除擴展 → 重新載入

---

🎊 **恭喜！您現在可以在 YouTube 和線上會議中享受即時語音轉文字字幕了！**
