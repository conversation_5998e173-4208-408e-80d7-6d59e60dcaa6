<!DOCTYPE html>
<html lang="zh-TW">
<head>
  <meta charset="UTF-8">
  <title>簡化版會議轉錄系統</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      color: white;
    }
    
    .container {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 20px;
      padding: 30px;
      color: #333;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }
    
    h1 {
      text-align: center;
      color: #2c3e50;
      margin-bottom: 30px;
    }
    
    .section {
      background: #f8f9fa;
      border-radius: 15px;
      padding: 20px;
      margin: 20px 0;
      border: 2px solid #e9ecef;
    }
    
    button {
      padding: 12px 24px;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
      border: none;
      border-radius: 8px;
      margin: 5px;
      transition: all 0.3s ease;
    }
    
    button.primary {
      background: linear-gradient(45deg, #28a745, #20c997);
      color: white;
    }
    
    button.danger {
      background: linear-gradient(45deg, #dc3545, #fd7e14);
      color: white;
    }
    
    button:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    
    button:disabled {
      background: #6c757d;
      cursor: not-allowed;
      opacity: 0.6;
    }
    
    button.recording {
      animation: pulse 1.5s infinite;
    }
    
    @keyframes pulse {
      0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
      70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
      100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
    }
    
    .status {
      padding: 15px;
      margin: 10px 0;
      border-radius: 8px;
      font-weight: bold;
    }
    
    .status.success {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    
    .status.error {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    
    .status.info {
      background: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }
    
    .status.warning {
      background: #fff3cd;
      color: #856404;
      border: 1px solid #ffeaa7;
    }
    
    .transcription-display {
      background: white;
      border: 1px solid #dee2e6;
      border-radius: 10px;
      padding: 20px;
      margin: 20px 0;
      max-height: 500px;
      overflow-y: auto;
    }
    
    .transcription-item {
      background: #f8f9fa;
      margin: 10px 0;
      padding: 15px;
      border-radius: 8px;
      border-left: 4px solid #007bff;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .timestamp {
      font-size: 12px;
      color: #6c757d;
      margin-bottom: 5px;
    }
    
    .text-content {
      font-size: 16px;
      line-height: 1.5;
    }
    
    input[type="file"] {
      margin: 10px 0;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      background: white;
    }
    
    .warning-box {
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      padding: 15px;
      border-radius: 10px;
      margin: 15px 0;
      color: #856404;
    }
    
    .info-box {
      background: #d1ecf1;
      border: 1px solid #bee5eb;
      padding: 15px;
      border-radius: 10px;
      margin: 15px 0;
      color: #0c5460;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🎤 簡化版會議轉錄系統</h1>
    
    <!-- 重要說明 -->
    <div class="warning-box">
      <h3>⚠️ 重要說明</h3>
      <p><strong>如果遇到 "Not supported" 錯誤，請：</strong></p>
      <ol>
        <li>確保使用 <strong>Chrome 或 Edge 瀏覽器</strong>（Firefox 可能不支援）</li>
        <li>確保使用 <strong>HTTPS 或 localhost</strong>（不能是 file:// 協議）</li>
        <li>嘗試使用 <strong>文件上傳功能</strong> 作為替代方案</li>
      </ol>
    </div>
    
    <!-- 後端狀態檢查 -->
    <div class="section">
      <h3>📊 系統狀態</h3>
      <button onclick="checkBackend()">🔄 檢查後端狀態</button>
      <button onclick="startLocalServer()">🚀 啟動本地服務器</button>
      <div id="backendStatus" class="status info">點擊檢查後端狀態</div>
    </div>
    
    <!-- 文件上傳轉錄 -->
    <div class="section">
      <h3>📁 文件轉錄（推薦方式）</h3>
      <p>上傳會議錄音文件進行轉錄：</p>
      <input type="file" id="audioFile" accept="audio/*">
      <button onclick="uploadFile()" class="primary">📤 上傳並轉錄</button>
      <div id="uploadStatus" class="status info">選擇音頻文件後點擊上傳</div>
    </div>
    
    <!-- 實時音頻捕獲 -->
    <div class="section">
      <h3>🎙️ 實時音頻捕獲</h3>
      <div class="info-box">
        <p><strong>使用說明：</strong></p>
        <p>1. <strong>麥克風模式</strong>：捕獲您的語音輸入</p>
        <p>2. <strong>系統音頻模式</strong>：嘗試捕獲系統聲音（需要瀏覽器支援）</p>
      </div>
      
      <button id="startMic" onclick="startMicrophone()" class="primary">🎙️ 開始麥克風</button>
      <button id="stopMic" onclick="stopMicrophone()" class="danger" disabled>⏹️ 停止麥克風</button>
      <br><br>
      <button id="startSystem" onclick="startSystemAudio()" class="primary">🖥️ 嘗試系統音頻</button>
      <button id="stopSystem" onclick="stopSystemAudio()" class="danger" disabled>⏹️ 停止系統音頻</button>
      
      <div id="audioStatus" class="status info">音頻狀態：待機中</div>
    </div>
    
    <!-- 轉錄結果 -->
    <div class="section">
      <h3>📝 轉錄結果</h3>
      <button onclick="clearTranscriptions()">🗑️ 清空</button>
      <button onclick="exportTranscriptions()">💾 匯出</button>
      <div class="transcription-display" id="transcriptionDisplay">
        <div class="transcription-item">
          <div class="timestamp">等待中...</div>
          <div class="text-content">轉錄結果將在此顯示</div>
        </div>
      </div>
    </div>
  </div>

  <script>
    let mediaRecorder = null;
    let audioContext = null;
    let microphoneStream = null;
    let systemAudioStream = null;
    let isRecording = false;
    let audioBuffer = [];
    let lastProcessTime = 0;

    // 檢查後端狀態
    async function checkBackend() {
      const statusDiv = document.getElementById('backendStatus');
      statusDiv.innerHTML = '<div class="status info">🔄 檢查中...</div>';
      
      try {
        const response = await fetch('http://localhost:9004/health', {
          method: 'GET',
          mode: 'cors'
        });
        
        if (response.ok) {
          const data = await response.json();
          statusDiv.innerHTML = `
            <div class="status success">
              ✅ 後端連接成功<br>
              模型：${data.model_name}<br>
              設備：${data.device}<br>
              GPU：${data.gpu_info || '無'}
            </div>
          `;
        } else {
          throw new Error(`HTTP ${response.status}`);
        }
      } catch (error) {
        statusDiv.innerHTML = `
          <div class="status error">
            ❌ 後端連接失敗<br>
            錯誤：${error.message}<br>
            <small>請確保後端服務器運行在 http://localhost:9004</small>
          </div>
        `;
      }
    }

    // 啟動本地服務器提示
    function startLocalServer() {
      alert(`請在終端中運行以下命令啟動服務器：

cd /home/<USER>/桌面/LLM_RAG/stt_project/app
python3 openai_whisper_server.py

或者：

uvicorn openai_whisper_server:app --host 0.0.0.0 --port 9004

然後點擊"檢查後端狀態"按鈕。`);
    }

    // 文件上傳轉錄
    async function uploadFile() {
      const fileInput = document.getElementById('audioFile');
      const statusDiv = document.getElementById('uploadStatus');
      
      if (!fileInput.files[0]) {
        statusDiv.innerHTML = '<div class="status error">❌ 請選擇音頻文件</div>';
        return;
      }

      const file = fileInput.files[0];
      statusDiv.innerHTML = '<div class="status info">🔄 上傳中...</div>';

      try {
        const formData = new FormData();
        formData.append('audio_file', file);

        const response = await fetch('http://localhost:9004/api/transcribe', {
          method: 'POST',
          mode: 'cors',
          body: formData
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`);
        }

        const data = await response.json();
        
        statusDiv.innerHTML = '<div class="status success">✅ 轉錄完成</div>';
        addTranscription(data.transcription, 'file');
        
      } catch (error) {
        statusDiv.innerHTML = `<div class="status error">❌ 轉錄失敗：${error.message}</div>`;
      }
    }

    // 開始麥克風
    async function startMicrophone() {
      try {
        microphoneStream = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: true,
            noiseSuppression: true,
            sampleRate: 44100
          }
        });

        setupAudioProcessing(microphoneStream, 'microphone');
        
        document.getElementById('startMic').disabled = true;
        document.getElementById('stopMic').disabled = false;
        document.getElementById('startMic').classList.add('recording');
        
        document.getElementById('audioStatus').innerHTML = 
          '<div class="status success">🎙️ 麥克風錄音中...</div>';
        
        isRecording = true;

      } catch (error) {
        document.getElementById('audioStatus').innerHTML = 
          `<div class="status error">❌ 麥克風啟動失敗：${error.message}</div>`;
      }
    }

    // 停止麥克風
    function stopMicrophone() {
      if (microphoneStream) {
        microphoneStream.getTracks().forEach(track => track.stop());
        microphoneStream = null;
      }
      
      isRecording = false;
      
      document.getElementById('startMic').disabled = false;
      document.getElementById('stopMic').disabled = true;
      document.getElementById('startMic').classList.remove('recording');
      
      document.getElementById('audioStatus').innerHTML = 
        '<div class="status info">⏹️ 麥克風已停止</div>';
    }

    // 嘗試系統音頻
    async function startSystemAudio() {
      try {
        // 檢查瀏覽器支援
        if (!navigator.mediaDevices || !navigator.mediaDevices.getDisplayMedia) {
          throw new Error('瀏覽器不支援 getDisplayMedia API');
        }

        systemAudioStream = await navigator.mediaDevices.getDisplayMedia({
          video: false,
          audio: {
            echoCancellation: false,
            noiseSuppression: false,
            sampleRate: 44100
          }
        });

        // 檢查是否有音頻軌道
        const audioTracks = systemAudioStream.getAudioTracks();
        if (audioTracks.length === 0) {
          throw new Error('未捕獲到音頻軌道，請確保選擇了包含音頻的來源');
        }

        setupAudioProcessing(systemAudioStream, 'system');
        
        document.getElementById('startSystem').disabled = true;
        document.getElementById('stopSystem').disabled = false;
        document.getElementById('startSystem').classList.add('recording');
        
        document.getElementById('audioStatus').innerHTML = 
          '<div class="status success">🖥️ 系統音頻捕獲中...</div>';
        
        isRecording = true;

      } catch (error) {
        document.getElementById('audioStatus').innerHTML = 
          `<div class="status error">❌ 系統音頻失敗：${error.message}<br>
          <small>建議：1. 使用 Chrome/Edge 瀏覽器 2. 確保選擇"分享音頻" 3. 或使用文件上傳功能</small></div>`;
      }
    }

    // 停止系統音頻
    function stopSystemAudio() {
      if (systemAudioStream) {
        systemAudioStream.getTracks().forEach(track => track.stop());
        systemAudioStream = null;
      }
      
      isRecording = false;
      
      document.getElementById('startSystem').disabled = false;
      document.getElementById('stopSystem').disabled = true;
      document.getElementById('startSystem').classList.remove('recording');
      
      document.getElementById('audioStatus').innerHTML = 
        '<div class="status info">⏹️ 系統音頻已停止</div>';
    }

    // 設置音頻處理
    function setupAudioProcessing(stream, sourceType) {
      audioContext = new AudioContext();
      const source = audioContext.createMediaStreamSource(stream);
      const processor = audioContext.createScriptProcessor(4096, 1, 1);

      processor.onaudioprocess = function(event) {
        if (!isRecording) return;
        
        const audioData = event.inputBuffer.getChannelData(0);
        processAudioChunk(audioData, sourceType);
      };

      source.connect(processor);
      processor.connect(audioContext.destination);
    }

    // 處理音頻片段
    function processAudioChunk(audioData, sourceType) {
      audioBuffer.push(...audioData);
      
      const now = Date.now();
      if (now - lastProcessTime > 3000 && audioBuffer.length > 0) {
        lastProcessTime = now;
        
        const audioBlob = createWavBlob(new Float32Array(audioBuffer), 44100);
        sendAudioForTranscription(audioBlob, sourceType);
        
        audioBuffer = audioBuffer.slice(-4096);
      }
    }

    // 創建 WAV Blob
    function createWavBlob(audioData, sampleRate) {
      const length = audioData.length;
      const buffer = new ArrayBuffer(44 + length * 2);
      const view = new DataView(buffer);
      
      const writeString = (offset, string) => {
        for (let i = 0; i < string.length; i++) {
          view.setUint8(offset + i, string.charCodeAt(i));
        }
      };
      
      writeString(0, 'RIFF');
      view.setUint32(4, 36 + length * 2, true);
      writeString(8, 'WAVE');
      writeString(12, 'fmt ');
      view.setUint32(16, 16, true);
      view.setUint16(20, 1, true);
      view.setUint16(22, 1, true);
      view.setUint32(24, sampleRate, true);
      view.setUint32(28, sampleRate * 2, true);
      view.setUint16(32, 2, true);
      view.setUint16(34, 16, true);
      writeString(36, 'data');
      view.setUint32(40, length * 2, true);
      
      let offset = 44;
      for (let i = 0; i < length; i++) {
        const sample = Math.max(-1, Math.min(1, audioData[i]));
        view.setInt16(offset, sample * 0x7FFF, true);
        offset += 2;
      }
      
      return new Blob([buffer], { type: 'audio/wav' });
    }

    // 發送音頻進行轉錄
    async function sendAudioForTranscription(audioBlob, sourceType) {
      try {
        const formData = new FormData();
        formData.append('audio_file', audioBlob, 'audio.wav');

        const response = await fetch('http://localhost:9004/api/transcribe', {
          method: 'POST',
          mode: 'cors',
          body: formData
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`);
        }

        const data = await response.json();
        
        if (data.transcription && data.transcription.trim()) {
          addTranscription(data.transcription, sourceType);
        }
      } catch (error) {
        console.error('轉錄錯誤:', error);
      }
    }

    // 添加轉錄結果
    function addTranscription(text, sourceType) {
      const display = document.getElementById('transcriptionDisplay');
      const timestamp = new Date().toLocaleTimeString();
      
      const sourceNames = {
        'microphone': '🎙️ 麥克風',
        'system': '🖥️ 系統音頻',
        'file': '📁 文件'
      };

      const item = document.createElement('div');
      item.className = 'transcription-item';
      item.innerHTML = `
        <div class="timestamp">${timestamp} - ${sourceNames[sourceType] || sourceType}</div>
        <div class="text-content">${text}</div>
      `;

      display.appendChild(item);
      display.scrollTop = display.scrollHeight;
    }

    // 清空轉錄
    function clearTranscriptions() {
      document.getElementById('transcriptionDisplay').innerHTML = '';
    }

    // 匯出轉錄
    function exportTranscriptions() {
      const display = document.getElementById('transcriptionDisplay');
      const items = display.querySelectorAll('.transcription-item');

      let exportText = '會議轉錄記錄\n================\n\n';

      items.forEach(item => {
        const timestamp = item.querySelector('.timestamp').textContent;
        const textContent = item.querySelector('.text-content').textContent;
        exportText += `${timestamp}\n${textContent}\n\n`;
      });

      const blob = new Blob([exportText], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `meeting_transcription_${new Date().toISOString().slice(0, 19)}.txt`;
      a.click();
      URL.revokeObjectURL(url);
    }

    // 頁面載入時檢查後端
    window.onload = function() {
      checkBackend();
    };
  </script>
</body>
</html>
