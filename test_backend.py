#!/usr/bin/env python3
"""
测试后端 API 的脚本
"""

import requests
import os

def test_health():
    """测试健康检查"""
    try:
        response = requests.get('http://localhost:9004/health')
        print(f"健康检查: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"模型已载入: {data.get('model_loaded', False)}")
            return True
    except Exception as e:
        print(f"健康检查失败: {e}")
    return False

def test_file_upload():
    """测试文件上传"""
    audio_file = "/home/<USER>/下載/ponsunlun_6.wav"
    
    if not os.path.exists(audio_file):
        print(f"音频文件不存在: {audio_file}")
        return False
    
    try:
        with open(audio_file, 'rb') as f:
            files = {'audio_file': f}
            response = requests.post('http://localhost:9004/api/transcribe', files=files)
        
        print(f"文件上传: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"转录结果: {data.get('transcription', '无结果')}")
            return True
        else:
            print(f"错误: {response.text}")
            
    except Exception as e:
        print(f"文件上传失败: {e}")
    
    return False

if __name__ == "__main__":
    print("=== 后端 API 测试 ===")
    
    print("\n1. 测试健康检查...")
    health_ok = test_health()
    
    if health_ok:
        print("\n2. 测试文件上传...")
        upload_ok = test_file_upload()
        
        if upload_ok:
            print("\n✅ 所有测试通过！")
        else:
            print("\n❌ 文件上传测试失败")
    else:
        print("\n❌ 健康检查失败，跳过其他测试")
