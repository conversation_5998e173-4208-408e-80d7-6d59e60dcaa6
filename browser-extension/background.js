// 背景服务工作器
class STTBackgroundService {
  constructor() {
    this.isCapturing = false;
    this.mediaRecorder = null;
    this.stream = null;
    this.backendUrl = 'http://localhost:9004';
    this.setupEventListeners();
  }

  setupEventListeners() {
    // 监听来自 popup 和 content script 的消息
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // 保持消息通道开放
    });

    // 监听扩展安装
    chrome.runtime.onInstalled.addListener(() => {
      console.log('STT Extension installed');
      this.initializeExtension();
    });
  }

  async initializeExtension() {
    // 设置默认配置
    const defaultSettings = {
      backendUrl: 'http://localhost:9004',
      subtitlePosition: 'bottom',
      subtitleSize: 'medium',
      subtitleColor: '#ffffff',
      subtitleBackground: 'rgba(0, 0, 0, 0.7)',
      autoStart: false,
      language: 'zh'
    };

    chrome.storage.sync.set(defaultSettings);
  }

  async handleMessage(message, sender, sendResponse) {
    try {
      switch (message.action) {
        case 'startCapture':
          await this.startCapture(message.options);
          sendResponse({ success: true });
          break;

        case 'stopCapture':
          await this.stopCapture();
          sendResponse({ success: true });
          break;

        case 'getStatus':
          sendResponse({ 
            isCapturing: this.isCapturing,
            backendConnected: await this.checkBackendConnection()
          });
          break;

        case 'checkBackend':
          const connected = await this.checkBackendConnection();
          sendResponse({ connected });
          break;

        case 'getSettings':
          const settings = await chrome.storage.sync.get();
          sendResponse(settings);
          break;

        case 'saveSettings':
          await chrome.storage.sync.set(message.settings);
          sendResponse({ success: true });
          break;

        default:
          sendResponse({ error: 'Unknown action' });
      }
    } catch (error) {
      console.error('Background script error:', error);
      sendResponse({ error: error.message });
    }
  }

  async startCapture(options = {}) {
    if (this.isCapturing) {
      throw new Error('Already capturing');
    }

    try {
      // 获取当前活动标签页
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      // 开始标签页音频捕获
      this.stream = await chrome.tabCapture.capture({
        audio: true,
        video: false
      });

      if (!this.stream) {
        throw new Error('Failed to capture tab audio');
      }

      // 设置 MediaRecorder
      this.mediaRecorder = new MediaRecorder(this.stream, {
        mimeType: 'audio/webm;codecs=opus'
      });

      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data && event.data.size > 0) {
          this.sendAudioToBackend(event.data, tab.id);
        }
      };

      this.mediaRecorder.onerror = (error) => {
        console.error('MediaRecorder error:', error);
        this.stopCapture();
      };

      // 每2秒产生一个音频块
      this.mediaRecorder.start(2000);
      this.isCapturing = true;

      // 通知 content script 开始显示字幕
      chrome.tabs.sendMessage(tab.id, {
        action: 'startSubtitles'
      });

      console.log('Audio capture started');

    } catch (error) {
      console.error('Failed to start capture:', error);
      throw error;
    }
  }

  async stopCapture() {
    if (!this.isCapturing) {
      return;
    }

    try {
      if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
        this.mediaRecorder.stop();
      }

      if (this.stream) {
        this.stream.getTracks().forEach(track => track.stop());
        this.stream = null;
      }

      this.isCapturing = false;

      // 通知所有标签页停止字幕
      const tabs = await chrome.tabs.query({});
      tabs.forEach(tab => {
        chrome.tabs.sendMessage(tab.id, {
          action: 'stopSubtitles'
        }).catch(() => {}); // 忽略错误，可能标签页已关闭
      });

      console.log('Audio capture stopped');

    } catch (error) {
      console.error('Failed to stop capture:', error);
    }
  }

  async sendAudioToBackend(audioBlob, tabId) {
    try {
      const settings = await chrome.storage.sync.get(['backendUrl']);
      const backendUrl = settings.backendUrl || this.backendUrl;

      const formData = new FormData();
      formData.append('audio_file', audioBlob, 'audio.webm');

      const response = await fetch(`${backendUrl}/api/transcribe`, {
        method: 'POST',
        body: formData
      });

      if (response.ok) {
        const result = await response.json();
        
        if (result.transcription && result.transcription.trim()) {
          // 发送转录结果到 content script
          chrome.tabs.sendMessage(tabId, {
            action: 'showSubtitle',
            text: result.transcription.trim(),
            timestamp: Date.now()
          }).catch(error => {
            console.log('Failed to send subtitle to tab:', error);
          });
        }
      } else {
        console.error('Backend transcription failed:', response.status);
      }

    } catch (error) {
      console.error('Failed to send audio to backend:', error);
    }
  }

  async checkBackendConnection() {
    try {
      const settings = await chrome.storage.sync.get(['backendUrl']);
      const backendUrl = settings.backendUrl || this.backendUrl;

      const response = await fetch(`${backendUrl}/health`, {
        method: 'GET',
        timeout: 5000
      });

      return response.ok;
    } catch (error) {
      return false;
    }
  }
}

// 初始化背景服务
const sttService = new STTBackgroundService();
