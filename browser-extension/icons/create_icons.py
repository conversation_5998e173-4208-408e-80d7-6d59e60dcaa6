#!/usr/bin/env python3
"""
创建浏览器扩展图标的脚本
需要安装 Pillow: pip install Pillow
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_icon(size, filename):
    """创建指定大小的图标"""
    # 创建图像
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 绘制圆形背景
    margin = size // 8
    draw.ellipse([margin, margin, size-margin, size-margin], 
                fill=(102, 126, 234, 255), outline=(76, 75, 162, 255), width=2)
    
    # 绘制麦克风图标
    mic_width = size // 4
    mic_height = size // 3
    mic_x = (size - mic_width) // 2
    mic_y = (size - mic_height) // 2 - size // 10
    
    # 麦克风主体
    draw.rounded_rectangle([mic_x, mic_y, mic_x + mic_width, mic_y + mic_height], 
                          radius=mic_width//4, fill=(255, 255, 255, 255))
    
    # 麦克风支架
    stand_width = mic_width // 6
    stand_height = size // 8
    stand_x = mic_x + (mic_width - stand_width) // 2
    stand_y = mic_y + mic_height
    
    draw.rectangle([stand_x, stand_y, stand_x + stand_width, stand_y + stand_height], 
                  fill=(255, 255, 255, 255))
    
    # 底座
    base_width = mic_width // 2
    base_height = size // 16
    base_x = mic_x + (mic_width - base_width) // 2
    base_y = stand_y + stand_height
    
    draw.rectangle([base_x, base_y, base_x + base_width, base_y + base_height], 
                  fill=(255, 255, 255, 255))
    
    # 保存图像
    img.save(filename, 'PNG')
    print(f"Created {filename} ({size}x{size})")

def main():
    """创建所有需要的图标尺寸"""
    sizes = [16, 48, 128]
    
    for size in sizes:
        filename = f"icon{size}.png"
        create_icon(size, filename)
    
    print("All icons created successfully!")

if __name__ == "__main__":
    main()
