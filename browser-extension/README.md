# 實時語音轉文字字幕 - Chrome 擴展

這是一個 Chrome 瀏覽器擴展，可以為 YouTube 影片和線上會議提供即時語音轉文字字幕功能。

## 功能特點

- ✅ **YouTube 影片字幕**：自動為 YouTube 影片生成即時字幕
- ✅ **線上會議字幕**：支持 Google Meet、Zoom、Microsoft Teams
- ✅ **音頻捕獲**：使用 Chrome Tab Capture API 捕獲標籤頁音頻
- ✅ **實時轉錄**：基於 Faster Whisper 的高質量語音識別
- ✅ **自定義樣式**：可調整字幕位置、大小、顏色等
- ✅ **多語言支持**：支持中文、英文、日文、韓文等

## 安裝方法

### 方法一：開發者模式安裝（推薦）

1. **確保後端服務運行**
   ```bash
   cd /path/to/stt_project/app
   python app.py
   ```

2. **打開 Chrome 擴展管理頁面**
   - 在 Chrome 地址欄輸入：`chrome://extensions/`
   - 或者：選單 → 更多工具 → 擴展程序

3. **啟用開發者模式**
   - 點擊右上角的「開發者模式」開關

4. **載入擴展**
   - 點擊「載入未封裝項目」
   - 選擇 `browser-extension` 資料夾
   - 點擊「選擇資料夾」

5. **確認安裝**
   - 擴展應該出現在擴展列表中
   - 瀏覽器工具欄會出現麥克風圖標

### 方法二：打包安裝

1. **打包擴展**
   - 在擴展管理頁面點擊「打包擴展程序」
   - 選擇 `browser-extension` 資料夾
   - 生成 `.crx` 文件

2. **安裝打包文件**
   - 將 `.crx` 文件拖拽到擴展管理頁面
   - 確認安裝

## 使用方法

### 基本使用

1. **啟動後端服務**
   ```bash
   cd stt_project/app
   python app.py
   ```

2. **打開支持的網站**
   - YouTube: https://www.youtube.com
   - Google Meet: https://meet.google.com
   - Zoom: https://zoom.us
   - Microsoft Teams: https://teams.microsoft.com

3. **啟動字幕**
   - 點擊瀏覽器工具欄的麥克風圖標
   - 在彈出窗口中點擊「開始字幕」
   - 授權音頻捕獲權限

4. **查看字幕**
   - 字幕會自動顯示在頁面底部
   - 可以在設置中調整樣式和位置

### YouTube 使用

1. 打開任意 YouTube 影片
2. 啟動擴展字幕功能
3. 播放影片，字幕會即時顯示
4. 支持全屏模式

### 線上會議使用

1. 加入 Google Meet / Zoom / Teams 會議
2. 啟動擴展字幕功能
3. 會議中的語音會即時轉換為字幕
4. 字幕位置會自動適配會議界面

## 設置選項

### 字幕外觀

- **位置**：頂部、底部、中央
- **大小**：小、中、大
- **顏色**：自定義文字顏色
- **背景**：自定義背景顏色和透明度

### 行為設置

- **自動啟動**：在支持的網站自動開始字幕
- **通知**：顯示狀態通知
- **語言**：設置識別語言

### 後端設置

- **服務地址**：配置 Faster Whisper 後端 URL
- **連接測試**：測試後端服務連接

## 故障排除

### 常見問題

1. **擴展無法載入**
   - 確保 Chrome 版本 ≥ 88
   - 檢查 manifest.json 語法
   - 查看擴展管理頁面的錯誤信息

2. **無法捕獲音頻**
   - 確保網站使用 HTTPS 或 localhost
   - 檢查 Chrome 音頻權限設置
   - 嘗試重新載入擴展

3. **後端連接失敗**
   - 確認後端服務正在運行（localhost:9004）
   - 檢查防火牆設置
   - 測試 http://localhost:9004/health

4. **字幕不顯示**
   - 檢查頁面是否支持
   - 確認音頻有聲音輸出
   - 查看瀏覽器控制台錯誤

5. **字幕質量差**
   - 確保音頻清晰
   - 調整 Whisper 模型大小
   - 檢查網絡連接

### 調試方法

1. **查看擴展日誌**
   ```
   Chrome → 擴展管理 → 詳細信息 → 檢查視圖：背景頁
   ```

2. **查看內容腳本日誌**
   ```
   F12 → Console → 查看錯誤信息
   ```

3. **測試後端連接**
   ```bash
   curl http://localhost:9004/health
   ```

## 支持的網站

| 網站 | 狀態 | 說明 |
|------|------|------|
| YouTube | ✅ 完全支持 | 包括全屏模式 |
| Google Meet | ✅ 完全支持 | 自動適配界面 |
| Zoom | ✅ 完全支持 | Web 版本 |
| Microsoft Teams | ✅ 完全支持 | Web 版本 |
| 其他網站 | ⚠️ 基本支持 | 可能需要調整樣式 |

## 技術架構

### 擴展組件

- **Background Script**: 音頻捕獲和後端通信
- **Content Script**: 字幕顯示和頁面交互
- **Popup**: 控制界面
- **Options**: 設置頁面

### API 使用

- **Tab Capture API**: 捕獲標籤頁音頻
- **Storage API**: 保存用戶設置
- **Runtime API**: 組件間通信

### 權限說明

- `activeTab`: 訪問當前標籤頁
- `tabCapture`: 捕獲音頻流
- `storage`: 保存設置
- `scripting`: 注入內容腳本
- `desktopCapture`: 桌面音頻捕獲

## 開發指南

### 項目結構

```
browser-extension/
├── manifest.json          # 擴展配置
├── background.js          # 背景腳本
├── content.js            # 內容腳本
├── popup.html/js         # 彈出界面
├── options.html/js       # 設置頁面
├── subtitle.css          # 字幕樣式
└── icons/               # 圖標文件
```

### 本地開發

1. 修改代碼後重新載入擴展
2. 使用 Chrome DevTools 調試
3. 查看控制台錯誤信息
4. 測試不同網站兼容性

### 發布準備

1. 更新版本號
2. 測試所有功能
3. 優化性能
4. 準備應用商店資料

## 許可證

MIT License

## 支持

如有問題請創建 Issue 或聯繫開發團隊。
