// Options 页面脚本
class STTOptions {
  constructor() {
    this.defaultSettings = {
      backendUrl: 'http://localhost:9004',
      subtitlePosition: 'bottom',
      subtitleSize: 'medium',
      subtitleColor: '#ffffff',
      subtitleBackground: '#000000',
      backgroundOpacity: 80,
      autoStart: false,
      showNotifications: true,
      language: 'zh'
    };
    
    this.init();
  }

  async init() {
    this.setupEventListeners();
    await this.loadSettings();
    this.updatePreview();
  }

  setupEventListeners() {
    // 保存按钮
    document.getElementById('saveBtn').addEventListener('click', () => {
      this.saveSettings();
    });

    // 重置按钮
    document.getElementById('resetBtn').addEventListener('click', () => {
      this.resetSettings();
    });

    // 测试连接按钮
    document.getElementById('testBtn').addEventListener('click', () => {
      this.testConnection();
    });

    // 实时预览更新
    const previewInputs = [
      'subtitlePosition', 'subtitleSize', 'subtitleColor', 
      'subtitleBackground', 'backgroundOpacity'
    ];
    
    previewInputs.forEach(id => {
      const element = document.getElementById(id);
      element.addEventListener('input', () => this.updatePreview());
      element.addEventListener('change', () => this.updatePreview());
    });

    // 透明度滑块
    document.getElementById('backgroundOpacity').addEventListener('input', (e) => {
      document.getElementById('opacityValue').textContent = e.target.value;
      this.updatePreview();
    });
  }

  async loadSettings() {
    try {
      const settings = await chrome.storage.sync.get();
      const finalSettings = { ...this.defaultSettings, ...settings };
      
      // 更新 UI
      Object.keys(finalSettings).forEach(key => {
        const element = document.getElementById(key);
        if (element) {
          if (element.type === 'checkbox') {
            element.checked = finalSettings[key];
          } else {
            element.value = finalSettings[key];
          }
        }
      });

      // 更新透明度显示
      document.getElementById('opacityValue').textContent = finalSettings.backgroundOpacity;
      
    } catch (error) {
      console.error('Failed to load settings:', error);
      this.showMessage('載入設置失敗', 'error');
    }
  }

  async saveSettings() {
    try {
      const settings = {};
      
      // 收集所有设置
      Object.keys(this.defaultSettings).forEach(key => {
        const element = document.getElementById(key);
        if (element) {
          if (element.type === 'checkbox') {
            settings[key] = element.checked;
          } else if (element.type === 'range') {
            settings[key] = parseInt(element.value);
          } else {
            settings[key] = element.value;
          }
        }
      });

      // 保存到 Chrome 存储
      await chrome.storage.sync.set(settings);
      
      this.showMessage('設置已儲存', 'success');
      
    } catch (error) {
      console.error('Failed to save settings:', error);
      this.showMessage('儲存設置失敗', 'error');
    }
  }

  async resetSettings() {
    if (confirm('確定要重置為預設值嗎？')) {
      try {
        await chrome.storage.sync.set(this.defaultSettings);
        await this.loadSettings();
        this.updatePreview();
        this.showMessage('已重置為預設值', 'success');
        
      } catch (error) {
        console.error('Failed to reset settings:', error);
        this.showMessage('重置失敗', 'error');
      }
    }
  }

  async testConnection() {
    const backendUrl = document.getElementById('backendUrl').value;
    const testBtn = document.getElementById('testBtn');
    
    testBtn.disabled = true;
    testBtn.textContent = '測試中...';
    
    try {
      const response = await fetch(`${backendUrl}/health`, {
        method: 'GET',
        timeout: 5000
      });
      
      if (response.ok) {
        const data = await response.json();
        this.showMessage(`連接成功！模型已載入: ${data.model_loaded ? '是' : '否'}`, 'success');
      } else {
        this.showMessage(`連接失敗：HTTP ${response.status}`, 'error');
      }
      
    } catch (error) {
      this.showMessage(`連接失敗：${error.message}`, 'error');
    } finally {
      testBtn.disabled = false;
      testBtn.textContent = '測試連接';
    }
  }

  updatePreview() {
    const previewSubtitle = document.getElementById('previewSubtitle');
    const previewArea = document.getElementById('previewArea');
    
    if (!previewSubtitle || !previewArea) return;

    // 获取当前设置
    const position = document.getElementById('subtitlePosition').value;
    const size = document.getElementById('subtitleSize').value;
    const color = document.getElementById('subtitleColor').value;
    const backgroundColor = document.getElementById('subtitleBackground').value;
    const opacity = document.getElementById('backgroundOpacity').value;

    // 应用样式
    previewSubtitle.style.color = color;
    previewSubtitle.style.backgroundColor = this.hexToRgba(backgroundColor, opacity / 100);
    
    // 大小设置
    const sizeMap = {
      small: '14px',
      medium: '16px',
      large: '20px'
    };
    previewSubtitle.style.fontSize = sizeMap[size];
    
    // 位置设置（在预览中只是视觉效果）
    const positionMap = {
      top: 'flex-start',
      center: 'center',
      bottom: 'flex-end'
    };
    previewArea.style.alignItems = positionMap[position];
  }

  hexToRgba(hex, alpha) {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
  }

  showMessage(message, type) {
    const statusMessage = document.getElementById('statusMessage');
    statusMessage.textContent = message;
    statusMessage.className = `status-message status-${type}`;
    statusMessage.style.display = 'block';
    
    // 3秒后自动隐藏
    setTimeout(() => {
      statusMessage.style.display = 'none';
    }, 3000);
  }
}

// 初始化选项页面
document.addEventListener('DOMContentLoaded', () => {
  new STTOptions();
});
