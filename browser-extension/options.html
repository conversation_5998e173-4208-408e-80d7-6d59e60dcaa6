<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>語音轉文字字幕 - 設置</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }

        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }

        .section h2 {
            margin: 0 0 20px 0;
            font-size: 20px;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group:last-child {
            margin-bottom: 0;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }

        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }

        .color-input {
            width: 60px;
            height: 40px;
            padding: 0;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .range-input {
            width: 100%;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background-color: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background-color: #5a6fd8;
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
        }

        .btn-success {
            background-color: #28a745;
            color: white;
        }

        .btn-success:hover {
            background-color: #218838;
        }

        .button-group {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 30px;
        }

        .preview-area {
            background: #000;
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            margin-top: 15px;
            position: relative;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .preview-subtitle {
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: 500;
            max-width: 80%;
            word-wrap: break-word;
            line-height: 1.4;
        }

        .status-message {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            display: none;
        }

        .status-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .help-text {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }

        .supported-sites {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .site-card {
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 6px;
            text-align: center;
            background: white;
        }

        .site-card.supported {
            border-color: #28a745;
            background-color: #f8fff9;
        }

        .site-card h4 {
            margin: 0 0 5px 0;
            color: #333;
        }

        .site-card p {
            margin: 0;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎤 語音轉文字字幕設置</h1>
            <p>自定義您的字幕體驗</p>
        </div>

        <div class="content">
            <div id="statusMessage" class="status-message"></div>

            <!-- 後端設置 -->
            <div class="section">
                <h2>🔧 後端服務設置</h2>
                <div class="form-group">
                    <label for="backendUrl">後端服務 URL</label>
                    <input type="url" id="backendUrl" placeholder="http://localhost:9004">
                    <div class="help-text">Faster Whisper 後端服務的地址</div>
                </div>
                <div class="form-group">
                    <label for="language">語言設置</label>
                    <select id="language">
                        <option value="zh">中文</option>
                        <option value="en">English</option>
                        <option value="ja">日本語</option>
                        <option value="ko">한국어</option>
                        <option value="auto">自動檢測</option>
                    </select>
                </div>
            </div>

            <!-- 字幕外觀設置 -->
            <div class="section">
                <h2>🎨 字幕外觀設置</h2>
                <div class="form-group">
                    <label for="subtitlePosition">字幕位置</label>
                    <select id="subtitlePosition">
                        <option value="bottom">底部</option>
                        <option value="top">頂部</option>
                        <option value="center">中央</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="subtitleSize">字幕大小</label>
                    <select id="subtitleSize">
                        <option value="small">小</option>
                        <option value="medium">中</option>
                        <option value="large">大</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="subtitleColor">字幕顏色</label>
                    <input type="color" id="subtitleColor" class="color-input" value="#ffffff">
                </div>
                <div class="form-group">
                    <label for="subtitleBackground">背景顏色</label>
                    <input type="color" id="subtitleBackground" class="color-input" value="#000000">
                </div>
                <div class="form-group">
                    <label for="backgroundOpacity">背景透明度</label>
                    <input type="range" id="backgroundOpacity" class="range-input" min="0" max="100" value="80">
                    <div class="help-text">當前值: <span id="opacityValue">80</span>%</div>
                </div>
                
                <!-- 預覽區域 -->
                <div class="preview-area" id="previewArea">
                    <div class="preview-subtitle" id="previewSubtitle">
                        這是字幕預覽效果
                    </div>
                </div>
            </div>

            <!-- 行為設置 -->
            <div class="section">
                <h2>⚙️ 行為設置</h2>
                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="autoStart">
                        <label for="autoStart">在支援的網站自動啟動字幕</label>
                    </div>
                </div>
                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="showNotifications">
                        <label for="showNotifications">顯示通知</label>
                    </div>
                </div>
            </div>

            <!-- 支援的網站 -->
            <div class="section">
                <h2>🌐 支援的網站</h2>
                <div class="supported-sites">
                    <div class="site-card supported">
                        <h4>YouTube</h4>
                        <p>完全支援</p>
                    </div>
                    <div class="site-card supported">
                        <h4>Google Meet</h4>
                        <p>完全支援</p>
                    </div>
                    <div class="site-card supported">
                        <h4>Zoom</h4>
                        <p>完全支援</p>
                    </div>
                    <div class="site-card supported">
                        <h4>Microsoft Teams</h4>
                        <p>完全支援</p>
                    </div>
                </div>
            </div>

            <div class="button-group">
                <button class="btn btn-secondary" id="resetBtn">重置為預設值</button>
                <button class="btn btn-primary" id="testBtn">測試連接</button>
                <button class="btn btn-success" id="saveBtn">儲存設置</button>
            </div>
        </div>
    </div>

    <script src="options.js"></script>
</body>
</html>
