<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>語音轉文字字幕</title>
    <style>
        body {
            width: 320px;
            min-height: 400px;
            margin: 0;
            padding: 16px;
            font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
            color: white;
        }

        .header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }

        .status-card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .status-item:last-child {
            margin-bottom: 0;
        }

        .status-label {
            font-size: 14px;
            color: #666;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-left: 8px;
        }

        .status-connected {
            background-color: #4caf50;
        }

        .status-disconnected {
            background-color: #f44336;
        }

        .status-capturing {
            background-color: #ff9800;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .control-buttons {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .btn {
            padding: 12px 16px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-primary {
            background-color: #4caf50;
            color: white;
        }

        .btn-primary:hover {
            background-color: #45a049;
        }

        .btn-danger {
            background-color: #f44336;
            color: white;
        }

        .btn-danger:hover {
            background-color: #da190b;
        }

        .btn-secondary {
            background-color: #f5f5f5;
            color: #333;
            border: 1px solid #ddd;
        }

        .btn-secondary:hover {
            background-color: #e9e9e9;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .site-info {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 16px;
            font-size: 12px;
            color: #666;
        }

        .site-supported {
            color: #4caf50;
            font-weight: 500;
        }

        .site-unsupported {
            color: #ff9800;
        }

        .quick-settings {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-top: 16px;
        }

        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .setting-item:last-child {
            margin-bottom: 0;
        }

        .setting-label {
            font-size: 13px;
            color: #666;
        }

        select {
            padding: 4px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 12px;
        }

        .footer {
            text-align: center;
            margin-top: 16px;
        }

        .footer a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            font-size: 12px;
        }

        .footer a:hover {
            color: white;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎤 語音轉文字字幕</h1>
    </div>

    <div class="site-info" id="siteInfo">
        <span id="siteStatus">檢測當前網站...</span>
    </div>

    <div class="status-card">
        <div class="status-item">
            <span class="status-label">後端服務</span>
            <div style="display: flex; align-items: center;">
                <span id="backendStatus">檢查中...</span>
                <div class="status-indicator" id="backendIndicator"></div>
            </div>
        </div>
        <div class="status-item">
            <span class="status-label">音頻捕獲</span>
            <div style="display: flex; align-items: center;">
                <span id="captureStatus">未啟動</span>
                <div class="status-indicator" id="captureIndicator"></div>
            </div>
        </div>
    </div>

    <div class="control-buttons">
        <button class="btn btn-primary" id="startBtn">
            ▶️ 開始字幕
        </button>
        <button class="btn btn-danger" id="stopBtn" disabled>
            ⏹️ 停止字幕
        </button>
        <button class="btn btn-secondary" id="settingsBtn">
            ⚙️ 設置
        </button>
    </div>

    <div class="quick-settings">
        <div class="setting-item">
            <span class="setting-label">字幕位置</span>
            <select id="positionSelect">
                <option value="bottom">底部</option>
                <option value="top">頂部</option>
                <option value="center">中央</option>
            </select>
        </div>
        <div class="setting-item">
            <span class="setting-label">字幕大小</span>
            <select id="sizeSelect">
                <option value="small">小</option>
                <option value="medium">中</option>
                <option value="large">大</option>
            </select>
        </div>
    </div>

    <div class="footer">
        <a href="#" id="optionsLink">更多設置</a>
    </div>

    <script src="popup.js"></script>
</body>
</html>
