// Content Script - 在网页中运行
class STTContentScript {
  constructor() {
    this.subtitleContainer = null;
    this.isActive = false;
    this.subtitleQueue = [];
    this.currentSubtitle = null;
    this.settings = {};
    this.setupMessageListener();
    this.loadSettings();
  }

  setupMessageListener() {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true;
    });
  }

  async loadSettings() {
    try {
      this.settings = await chrome.storage.sync.get();
    } catch (error) {
      console.error('Failed to load settings:', error);
      this.settings = this.getDefaultSettings();
    }
  }

  getDefaultSettings() {
    return {
      subtitlePosition: 'bottom',
      subtitleSize: 'medium',
      subtitleColor: '#ffffff',
      subtitleBackground: 'rgba(0, 0, 0, 0.7)',
      autoStart: false,
      language: 'zh'
    };
  }

  handleMessage(message, sender, sendResponse) {
    switch (message.action) {
      case 'startSubtitles':
        this.startSubtitles();
        sendResponse({ success: true });
        break;

      case 'stopSubtitles':
        this.stopSubtitles();
        sendResponse({ success: true });
        break;

      case 'showSubtitle':
        this.showSubtitle(message.text, message.timestamp);
        sendResponse({ success: true });
        break;

      case 'updateSettings':
        this.settings = { ...this.settings, ...message.settings };
        this.updateSubtitleStyle();
        sendResponse({ success: true });
        break;

      default:
        sendResponse({ error: 'Unknown action' });
    }
  }

  startSubtitles() {
    if (this.isActive) return;

    this.isActive = true;
    this.createSubtitleContainer();
    console.log('Subtitles started on:', window.location.hostname);
  }

  stopSubtitles() {
    if (!this.isActive) return;

    this.isActive = false;
    this.removeSubtitleContainer();
    console.log('Subtitles stopped');
  }

  createSubtitleContainer() {
    if (this.subtitleContainer) {
      this.removeSubtitleContainer();
    }

    // 创建字幕容器
    this.subtitleContainer = document.createElement('div');
    this.subtitleContainer.id = 'stt-subtitle-container';
    this.subtitleContainer.className = 'stt-subtitle-container';

    // 根据网站调整位置
    this.adjustPositionForSite();

    // 应用样式
    this.updateSubtitleStyle();

    // 添加到页面
    document.body.appendChild(this.subtitleContainer);

    // 创建字幕文本元素
    this.currentSubtitle = document.createElement('div');
    this.currentSubtitle.className = 'stt-subtitle-text';
    this.subtitleContainer.appendChild(this.currentSubtitle);
  }

  adjustPositionForSite() {
    const hostname = window.location.hostname;
    
    if (hostname.includes('youtube.com')) {
      // YouTube 特殊处理
      this.subtitleContainer.classList.add('stt-youtube');
      
      // 检测是否为全屏模式
      const checkFullscreen = () => {
        const isFullscreen = document.fullscreenElement || 
                            document.webkitFullscreenElement || 
                            document.mozFullScreenElement;
        
        if (isFullscreen) {
          this.subtitleContainer.classList.add('stt-fullscreen');
        } else {
          this.subtitleContainer.classList.remove('stt-fullscreen');
        }
      };

      document.addEventListener('fullscreenchange', checkFullscreen);
      document.addEventListener('webkitfullscreenchange', checkFullscreen);
      document.addEventListener('mozfullscreenchange', checkFullscreen);
      
    } else if (hostname.includes('meet.google.com')) {
      this.subtitleContainer.classList.add('stt-google-meet');
      
    } else if (hostname.includes('zoom.us')) {
      this.subtitleContainer.classList.add('stt-zoom');
      
    } else if (hostname.includes('teams.microsoft.com')) {
      this.subtitleContainer.classList.add('stt-teams');
    }
  }

  updateSubtitleStyle() {
    if (!this.subtitleContainer) return;

    const { subtitlePosition, subtitleSize, subtitleColor, subtitleBackground } = this.settings;

    // 位置
    this.subtitleContainer.className = `stt-subtitle-container stt-position-${subtitlePosition}`;
    
    // 大小
    this.subtitleContainer.classList.add(`stt-size-${subtitleSize}`);

    // 颜色和背景
    if (this.currentSubtitle) {
      this.currentSubtitle.style.color = subtitleColor;
      this.currentSubtitle.style.backgroundColor = subtitleBackground;
    }
  }

  showSubtitle(text, timestamp) {
    if (!this.isActive || !this.currentSubtitle) return;

    // 清除之前的字幕
    this.currentSubtitle.textContent = text;
    
    // 添加淡入效果
    this.currentSubtitle.classList.remove('stt-fade-in');
    void this.currentSubtitle.offsetWidth; // 强制重排
    this.currentSubtitle.classList.add('stt-fade-in');

    // 自动清除字幕（5秒后）
    setTimeout(() => {
      if (this.currentSubtitle && this.currentSubtitle.textContent === text) {
        this.currentSubtitle.classList.add('stt-fade-out');
        setTimeout(() => {
          if (this.currentSubtitle) {
            this.currentSubtitle.textContent = '';
            this.currentSubtitle.classList.remove('stt-fade-out');
          }
        }, 500);
      }
    }, 5000);
  }

  removeSubtitleContainer() {
    if (this.subtitleContainer) {
      this.subtitleContainer.remove();
      this.subtitleContainer = null;
      this.currentSubtitle = null;
    }
  }

  // 检测页面变化（SPA 应用）
  observePageChanges() {
    const observer = new MutationObserver((mutations) => {
      // 检测 URL 变化或重要 DOM 变化
      if (this.isActive) {
        this.adjustPositionForSite();
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }
}

// 初始化 content script
const sttContent = new STTContentScript();

// 页面加载完成后开始观察变化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    sttContent.observePageChanges();
  });
} else {
  sttContent.observePageChanges();
}

// 检测自动启动
chrome.storage.sync.get(['autoStart']).then(result => {
  if (result.autoStart) {
    // 延迟启动，等待页面完全加载
    setTimeout(() => {
      chrome.runtime.sendMessage({ action: 'startCapture' });
    }, 2000);
  }
});
