// Popup 脚本
class STTPopup {
  constructor() {
    this.isCapturing = false;
    this.backendConnected = false;
    this.currentTab = null;
    this.supportedSites = [
      'youtube.com',
      'meet.google.com',
      'zoom.us',
      'teams.microsoft.com'
    ];
    
    this.init();
  }

  async init() {
    await this.getCurrentTab();
    this.setupEventListeners();
    this.loadSettings();
    this.updateStatus();
    this.checkSiteSupport();
    
    // 定期更新状态
    setInterval(() => this.updateStatus(), 2000);
  }

  async getCurrentTab() {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    this.currentTab = tab;
  }

  setupEventListeners() {
    // 开始按钮
    document.getElementById('startBtn').addEventListener('click', () => {
      this.startCapture();
    });

    // 停止按钮
    document.getElementById('stopBtn').addEventListener('click', () => {
      this.stopCapture();
    });

    // 设置按钮
    document.getElementById('settingsBtn').addEventListener('click', () => {
      chrome.runtime.openOptionsPage();
    });

    // 选项链接
    document.getElementById('optionsLink').addEventListener('click', (e) => {
      e.preventDefault();
      chrome.runtime.openOptionsPage();
    });

    // 快速设置
    document.getElementById('positionSelect').addEventListener('change', (e) => {
      this.updateSetting('subtitlePosition', e.target.value);
    });

    document.getElementById('sizeSelect').addEventListener('change', (e) => {
      this.updateSetting('subtitleSize', e.target.value);
    });
  }

  async loadSettings() {
    try {
      const settings = await chrome.storage.sync.get();
      
      // 更新快速设置UI
      document.getElementById('positionSelect').value = settings.subtitlePosition || 'bottom';
      document.getElementById('sizeSelect').value = settings.subtitleSize || 'medium';
      
    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  }

  async updateSetting(key, value) {
    try {
      await chrome.storage.sync.set({ [key]: value });
      
      // 通知 content script 更新设置
      if (this.currentTab) {
        chrome.tabs.sendMessage(this.currentTab.id, {
          action: 'updateSettings',
          settings: { [key]: value }
        }).catch(() => {}); // 忽略错误
      }
      
    } catch (error) {
      console.error('Failed to update setting:', error);
    }
  }

  async updateStatus() {
    try {
      // 检查后端连接
      const backendResponse = await chrome.runtime.sendMessage({ action: 'checkBackend' });
      this.backendConnected = backendResponse.connected;

      // 检查捕获状态
      const statusResponse = await chrome.runtime.sendMessage({ action: 'getStatus' });
      this.isCapturing = statusResponse.isCapturing;

      this.updateUI();
      
    } catch (error) {
      console.error('Failed to update status:', error);
    }
  }

  updateUI() {
    // 更新后端状态
    const backendStatus = document.getElementById('backendStatus');
    const backendIndicator = document.getElementById('backendIndicator');
    
    if (this.backendConnected) {
      backendStatus.textContent = '已連接';
      backendIndicator.className = 'status-indicator status-connected';
    } else {
      backendStatus.textContent = '未連接';
      backendIndicator.className = 'status-indicator status-disconnected';
    }

    // 更新捕获状态
    const captureStatus = document.getElementById('captureStatus');
    const captureIndicator = document.getElementById('captureIndicator');
    
    if (this.isCapturing) {
      captureStatus.textContent = '錄製中';
      captureIndicator.className = 'status-indicator status-capturing';
    } else {
      captureStatus.textContent = '未啟動';
      captureIndicator.className = 'status-indicator status-disconnected';
    }

    // 更新按钮状态
    const startBtn = document.getElementById('startBtn');
    const stopBtn = document.getElementById('stopBtn');
    
    startBtn.disabled = this.isCapturing || !this.backendConnected;
    stopBtn.disabled = !this.isCapturing;
  }

  checkSiteSupport() {
    if (!this.currentTab) return;

    const siteInfo = document.getElementById('siteInfo');
    const siteStatus = document.getElementById('siteStatus');
    
    const hostname = new URL(this.currentTab.url).hostname;
    const isSupported = this.supportedSites.some(site => hostname.includes(site));
    
    if (isSupported) {
      siteStatus.innerHTML = `<span class="site-supported">✅ 支援 ${hostname}</span>`;
    } else {
      siteStatus.innerHTML = `<span class="site-unsupported">⚠️ ${hostname} 未完全測試</span>`;
    }
  }

  async startCapture() {
    try {
      const response = await chrome.runtime.sendMessage({ 
        action: 'startCapture',
        options: {}
      });
      
      if (response.success) {
        console.log('Capture started successfully');
      } else {
        throw new Error(response.error || 'Failed to start capture');
      }
      
    } catch (error) {
      console.error('Failed to start capture:', error);
      alert(`啟動失敗: ${error.message}`);
    }
  }

  async stopCapture() {
    try {
      const response = await chrome.runtime.sendMessage({ action: 'stopCapture' });
      
      if (response.success) {
        console.log('Capture stopped successfully');
      } else {
        throw new Error(response.error || 'Failed to stop capture');
      }
      
    } catch (error) {
      console.error('Failed to stop capture:', error);
      alert(`停止失敗: ${error.message}`);
    }
  }
}

// 初始化 popup
document.addEventListener('DOMContentLoaded', () => {
  new STTPopup();
});
