/* 字幕样式 */
.stt-subtitle-container {
  position: fixed;
  z-index: 999999;
  pointer-events: none;
  font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  width: 100%;
  display: flex;
  justify-content: center;
  transition: all 0.3s ease;
}

/* 位置设置 */
.stt-position-top {
  top: 20px;
}

.stt-position-bottom {
  bottom: 80px;
}

.stt-position-center {
  top: 50%;
  transform: translateY(-50%);
}

/* 字幕文本 */
.stt-subtitle-text {
  background-color: rgba(0, 0, 0, 0.8);
  color: #ffffff;
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
  text-align: center;
  max-width: 80%;
  word-wrap: break-word;
  line-height: 1.4;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 大小设置 */
.stt-size-small .stt-subtitle-text {
  font-size: 14px;
  padding: 6px 12px;
}

.stt-size-medium .stt-subtitle-text {
  font-size: 16px;
  padding: 8px 16px;
}

.stt-size-large .stt-subtitle-text {
  font-size: 20px;
  padding: 10px 20px;
}

/* 动画效果 */
.stt-fade-in {
  animation: sttFadeIn 0.3s ease-out;
}

.stt-fade-out {
  animation: sttFadeOut 0.5s ease-out;
  opacity: 0;
}

@keyframes sttFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes sttFadeOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-10px);
  }
}

/* YouTube 特殊样式 */
.stt-youtube {
  bottom: 120px; /* 避开 YouTube 控制栏 */
}

.stt-youtube.stt-fullscreen {
  bottom: 80px; /* 全屏模式调整 */
}

.stt-youtube .stt-subtitle-text {
  background-color: rgba(0, 0, 0, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Google Meet 特殊样式 */
.stt-google-meet {
  bottom: 100px;
}

.stt-google-meet .stt-subtitle-text {
  background-color: rgba(32, 33, 36, 0.95);
  color: #e8eaed;
  border: 1px solid rgba(95, 99, 104, 0.3);
}

/* Zoom 特殊样式 */
.stt-zoom {
  bottom: 90px;
}

.stt-zoom .stt-subtitle-text {
  background-color: rgba(45, 45, 45, 0.95);
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Microsoft Teams 特殊样式 */
.stt-teams {
  bottom: 100px;
}

.stt-teams .stt-subtitle-text {
  background-color: rgba(37, 36, 35, 0.95);
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stt-subtitle-container {
    padding: 0 10px;
  }
  
  .stt-subtitle-text {
    max-width: 90%;
    font-size: 14px !important;
    padding: 6px 12px !important;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .stt-subtitle-text {
    background-color: #000000;
    color: #ffffff;
    border: 2px solid #ffffff;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .stt-subtitle-container {
    transition: none;
  }
  
  .stt-fade-in,
  .stt-fade-out {
    animation: none;
  }
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .stt-subtitle-text {
    background-color: rgba(0, 0, 0, 0.9);
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
}

/* 亮色主题适配 */
@media (prefers-color-scheme: light) {
  .stt-subtitle-text {
    background-color: rgba(255, 255, 255, 0.95);
    color: #000000;
    border: 1px solid rgba(0, 0, 0, 0.2);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
}
