version: '3.8'

services:
  # Whisper转录服务
  whisper-transcription:
    build:
      context: ./app
      dockerfile: Dockerfile
    container_name: whisper-transcription-service
    ports:
      - "9004:9004"
    volumes:
      - ./app/static:/app/static:ro
      - ./app/logs:/app/logs
      - whisper_models:/app/models
    environment:
      - PYTHONUNBUFFERED=1
      - WHISPER_MODEL_SIZE=base
      - WHISPER_DEVICE=cpu
      - OLLAMA_HOST=ollama
      - OLLAMA_PORT=11434
    depends_on:
      ollama:
        condition: service_healthy
    networks:
      - stt-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9004/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Ollama LLM服务 (用于翻译)
  ollama:
    image: ollama/ollama:latest
    container_name: ollama-service
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_ORIGINS=*
      - OLLAMA_HOST=0.0.0.0
    networks:
      - stt-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

volumes:
  whisper_models:
    driver: local
  ollama_data:
    driver: local

networks:
  stt-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

