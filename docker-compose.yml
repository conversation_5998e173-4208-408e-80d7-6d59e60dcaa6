version: '3.8'

services:
  # 后端服务 (Flask + SocketIO + Faster Whisper)
  backend:
    build: ./app
    container_name: stt_backend
    volumes:
      - ./app:/app
      - ./app/models:/app/models
    environment:
      - WHISPER_MODEL_SIZE=base
      - WHISPER_DEVICE=cpu
      - WHISPER_COMPUTE_TYPE=int8
    ports:
      - "9004:9004"
    extra_hosts:
      - "host.docker.internal:host-gateway"
    restart: always
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9004/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端服务 (React)
  frontend:
    build: ./frontend
    container_name: stt_frontend
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - REACT_APP_WEBSOCKET_URL=ws://localhost:9004
      - CHOKIDAR_USEPOLLING=true
    ports:
      - "3000:3000"
    depends_on:
      - backend
    restart: always

  # Nginx 反向代理
  nginx:
    image: nginx:latest
    container_name: stt_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/certs:/etc/nginx/certs
    depends_on:
      - backend
      - frontend
    restart: always

