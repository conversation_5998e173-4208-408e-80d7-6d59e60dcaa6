version: '3'

services:
  app:
    build: ./app
    container_name: stt_app
    volumes:
      - /home/<USER>/HTTPS:/app
    expose:
      - "9004"
    ports:
      - "9004:9004"
    extra_hosts:
     - "host.docker.internal:host-gateway"     
      
    restart: always

  nginx:
    image: nginx:latest
    container_name: nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/certs:/etc/nginx/certs
    depends_on:
      - app
    restart: always

