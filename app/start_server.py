#!/usr/bin/env python3
"""
启动服务器脚本
"""

import sys
import logging
from test_app import app, initialize_whisper_model

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    print("🚀 启动语音转文字服务器...")
    print("📋 配置信息:")
    print("   - 模型: Whisper base")
    print("   - 设备: GPU (CUDA)")
    print("   - 端口: 9004")
    
    # 初始化模型
    print("\n🔄 初始化 Whisper 模型...")
    if initialize_whisper_model():
        print("✅ 模型初始化成功!")
        print("\n🌐 启动 Flask 服务器...")
        print("   访问地址: http://localhost:9004")
        print("   健康检查: http://localhost:9004/health")
        print("   按 Ctrl+C 停止服务器")
        print("-" * 50)
        
        try:
            app.run(host="0.0.0.0", port=9004, debug=False)
        except KeyboardInterrupt:
            print("\n👋 服务器已停止")
        except Exception as e:
            print(f"\n❌ 服务器错误: {e}")
            sys.exit(1)
    else:
        print("❌ 模型初始化失败!")
        sys.exit(1)

if __name__ == "__main__":
    main()
