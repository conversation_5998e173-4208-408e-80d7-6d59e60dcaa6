# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/
server.log

# Models (will be downloaded in container)
models/
*.pt
*.pth
*.bin

# Temporary files
tmp/
temp/
*.tmp

# Git
.git/
.gitignore

# Docker
Dockerfile
.dockerignore
docker-compose.yml

# Documentation
README.md
*.md

# Test files
test_*.py
*_test.py
tests/
