#!/usr/bin/env python3
"""
双 Whisper 对比 FastAPI 服务器
同时使用 OpenAI Whisper 和 Faster Whisper 进行转录对比
"""

import logging
import io
import numpy as np
import soundfile as sf
from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from faster_whisper import WhisperModel
import whisper  # OpenAI Whisper
import torch
from typing import Optional, Dict, Any, List
from pydantic import BaseModel
import uvicorn
import time
import tempfile
import os

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 创建 FastAPI 应用
app = FastAPI(
    title="双 Whisper 对比 API",
    description="同时使用 OpenAI Whisper 和 Faster Whisper 进行语音转文字对比",
    version="2.0.0"
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局变量
faster_whisper_model: Optional[WhisperModel] = None
openai_whisper_model: Optional[Any] = None

# 响应模型
class HealthResponse(BaseModel):
    status: str
    faster_whisper_loaded: bool
    openai_whisper_loaded: bool
    timestamp: str
    device: str

class SingleTranscriptionResult(BaseModel):
    transcription: str
    language: str
    language_probability: float
    segments: List[dict]
    processing_time: float
    model_name: str

class DualTranscriptionResponse(BaseModel):
    faster_whisper: SingleTranscriptionResult
    openai_whisper: SingleTranscriptionResult
    comparison: dict

def initialize_models() -> tuple[bool, bool]:
    """初始化两个 Whisper 模型"""
    global faster_whisper_model, openai_whisper_model
    
    device = "cuda" if torch.cuda.is_available() else "cpu"
    
    logger.info("🔍 检查 GPU 可用性...")
    if torch.cuda.is_available():
        gpu_count = torch.cuda.device_count()
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        logger.info(f"✅ 检测到 CUDA，GPU 数量: {gpu_count}")
        logger.info(f"🎮 GPU 名称: {gpu_name}")
        logger.info(f"💾 GPU 内存: {gpu_memory:.1f} GB")
    else:
        logger.warning("⚠️ 使用 CPU")
    
    # 初始化 Faster Whisper
    faster_success = False
    try:
        logger.info("🔄 正在加载 Faster Whisper large-v3...")
        compute_type = "float16" if device == "cuda" else "int8"
        faster_whisper_model = WhisperModel("large-v3", device=device, compute_type=compute_type)
        logger.info("✅ Faster Whisper 加载成功")
        faster_success = True
    except Exception as e:
        logger.error(f"❌ Faster Whisper 加载失败: {e}")
        try:
            logger.info("🔄 尝试 Faster Whisper base...")
            faster_whisper_model = WhisperModel("base", device=device, compute_type=compute_type)
            logger.info("✅ Faster Whisper base 加载成功")
            faster_success = True
        except Exception as e2:
            logger.error(f"❌ Faster Whisper base 也失败: {e2}")
    
    # 初始化 OpenAI Whisper (使用 CPU 避免 GPU 内存冲突)
    openai_success = False
    openai_device = "cpu"  # 强制使用 CPU
    try:
        logger.info(f"🔄 正在加载 OpenAI Whisper base (设备: {openai_device})...")
        openai_whisper_model = whisper.load_model("base", device=openai_device)
        logger.info("✅ OpenAI Whisper 加载成功 (CPU)")
        openai_success = True
    except Exception as e:
        logger.error(f"❌ OpenAI Whisper 加载失败: {e}")
        try:
            logger.info("🔄 尝试 OpenAI Whisper tiny...")
            openai_whisper_model = whisper.load_model("tiny", device=openai_device)
            logger.info("✅ OpenAI Whisper tiny 加载成功 (CPU)")
            openai_success = True
        except Exception as e2:
            logger.error(f"❌ OpenAI Whisper tiny 也失败: {e2}")
    
    return faster_success, openai_success

def transcribe_with_faster_whisper(audio_array: np.ndarray) -> SingleTranscriptionResult:
    """使用 Faster Whisper 转录"""
    global faster_whisper_model
    
    if faster_whisper_model is None:
        raise HTTPException(status_code=500, detail="Faster Whisper 模型未加载")
    
    start_time = time.time()
    
    try:
        segments, info = faster_whisper_model.transcribe(
            audio_array,
            language="zh",
            beam_size=5,
            best_of=5,
            temperature=0.0,
            condition_on_previous_text=False,
            vad_filter=False
        )
        
        transcription_text = ""
        segments_list = []
        
        for segment in segments:
            transcription_text += segment.text
            segments_list.append({
                "start": segment.start,
                "end": segment.end,
                "text": segment.text
            })
        
        processing_time = time.time() - start_time
        
        return SingleTranscriptionResult(
            transcription=transcription_text.strip(),
            language=info.language,
            language_probability=float(info.language_probability),
            segments=segments_list,
            processing_time=processing_time,
            model_name="Faster Whisper"
        )
        
    except Exception as e:
        logger.error(f"Faster Whisper 转录错误: {e}")
        raise HTTPException(status_code=500, detail=f"Faster Whisper 转录失败: {str(e)}")

def transcribe_with_openai_whisper(audio_array: np.ndarray, sample_rate: int) -> SingleTranscriptionResult:
    """使用 OpenAI Whisper 转录"""
    global openai_whisper_model
    
    if openai_whisper_model is None:
        raise HTTPException(status_code=500, detail="OpenAI Whisper 模型未加载")
    
    start_time = time.time()
    
    try:
        # OpenAI Whisper 需要音频文件，所以我们创建临时文件
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
            sf.write(temp_file.name, audio_array, sample_rate)
            
            # 转录
            result = openai_whisper_model.transcribe(
                temp_file.name,
                language="zh",
                temperature=0.0,
                beam_size=5,
                best_of=5,
                condition_on_previous_text=False
            )
            
            # 清理临时文件
            os.unlink(temp_file.name)
        
        processing_time = time.time() - start_time
        
        # 处理 segments
        segments_list = []
        if "segments" in result:
            for segment in result["segments"]:
                segments_list.append({
                    "start": segment.get("start", 0),
                    "end": segment.get("end", 0),
                    "text": segment.get("text", "")
                })
        
        return SingleTranscriptionResult(
            transcription=result["text"].strip(),
            language=result.get("language", "zh"),
            language_probability=1.0,  # OpenAI Whisper 不提供概率
            segments=segments_list,
            processing_time=processing_time,
            model_name="OpenAI Whisper"
        )
        
    except Exception as e:
        logger.error(f"OpenAI Whisper 转录错误: {e}")
        raise HTTPException(status_code=500, detail=f"OpenAI Whisper 转录失败: {str(e)}")

def compare_results(faster_result: SingleTranscriptionResult, openai_result: SingleTranscriptionResult) -> dict:
    """比较两个转录结果"""
    
    faster_text = faster_result.transcription
    openai_text = openai_result.transcription
    
    # 基本统计
    comparison = {
        "length_comparison": {
            "faster_whisper": len(faster_text),
            "openai_whisper": len(openai_text),
            "difference": abs(len(faster_text) - len(openai_text))
        },
        "processing_time_comparison": {
            "faster_whisper": faster_result.processing_time,
            "openai_whisper": openai_result.processing_time,
            "faster_is_quicker": faster_result.processing_time < openai_result.processing_time
        },
        "text_similarity": calculate_similarity(faster_text, openai_text),
        "recommendations": []
    }
    
    # 添加建议
    if comparison["text_similarity"] < 0.5:
        comparison["recommendations"].append("两个模型结果差异较大，建议检查音频质量")
    
    if faster_result.processing_time < openai_result.processing_time * 0.8:
        comparison["recommendations"].append("Faster Whisper 速度明显更快")
    
    if len(faster_text) > len(openai_text) * 2:
        comparison["recommendations"].append("Faster Whisper 可能有重复问题")
    elif len(openai_text) > len(faster_text) * 2:
        comparison["recommendations"].append("OpenAI Whisper 可能有重复问题")
    
    return comparison

def calculate_similarity(text1: str, text2: str) -> float:
    """计算两个文本的相似度（简单版本）"""
    if not text1 or not text2:
        return 0.0
    
    # 简单的字符级相似度
    set1 = set(text1)
    set2 = set(text2)
    
    intersection = len(set1.intersection(set2))
    union = len(set1.union(set2))
    
    return intersection / union if union > 0 else 0.0

@app.on_event("startup")
async def startup_event():
    """应用启动时初始化模型"""
    logger.info("🚀 启动双 Whisper 对比服务器...")
    
    faster_success, openai_success = initialize_models()
    
    if not faster_success and not openai_success:
        logger.error("❌ 所有模型都加载失败!")
        raise Exception("模型初始化失败")
    
    logger.info("✅ 服务器启动完成!")

@app.get("/", response_model=dict)
async def root():
    """根路径"""
    return {
        "message": "双 Whisper 对比 API 服务",
        "version": "2.0.0",
        "docs": "/docs",
        "health": "/health"
    }

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查端点"""
    device_info = "cuda" if torch.cuda.is_available() else "cpu"
    
    return HealthResponse(
        status="healthy",
        faster_whisper_loaded=faster_whisper_model is not None,
        openai_whisper_loaded=openai_whisper_model is not None,
        timestamp="2025-07-18T00:00:00Z",
        device=device_info
    )

@app.post("/api/transcribe", response_model=DualTranscriptionResponse)
async def transcribe_file(audio_file: UploadFile = File(...)):
    """双模型转录对比端点"""
    try:
        # 验证文件
        if not audio_file.content_type or not audio_file.content_type.startswith('audio/'):
            allowed_extensions = ['.wav', '.mp3', '.m4a', '.flac', '.ogg', '.webm']
            if not any(audio_file.filename.lower().endswith(ext) for ext in allowed_extensions):
                raise HTTPException(status_code=400, detail="不支持的文件类型")
        
        # 读取音频数据
        audio_data = await audio_file.read()
        if len(audio_data) == 0:
            raise HTTPException(status_code=400, detail="上传的文件为空")
        
        logger.info(f"收到音频文件: {audio_file.filename}, 大小: {len(audio_data)} bytes")
        
        # 音频预处理
        audio_io = io.BytesIO(audio_data)
        audio_array, sample_rate = sf.read(audio_io, dtype='float32')
        
        logger.info(f"音频信息: 采样率={sample_rate}, 形状={audio_array.shape}, 时长={len(audio_array)/sample_rate:.2f}秒")
        
        # 确保单声道
        if len(audio_array.shape) > 1:
            audio_array = np.mean(audio_array, axis=1)
        
        # 数据预处理
        audio_array = np.array(audio_array, dtype=np.float32)
        max_val = np.max(np.abs(audio_array))
        if max_val > 0:
            audio_array = audio_array / max_val
        audio_array = np.nan_to_num(audio_array, nan=0.0, posinf=1.0, neginf=-1.0)
        
        # 并行转录（如果两个模型都可用）
        results = {}
        
        if faster_whisper_model is not None:
            logger.info("🎯 开始 Faster Whisper 转录...")
            try:
                results["faster_whisper"] = transcribe_with_faster_whisper(audio_array)
                logger.info(f"Faster Whisper 完成: {results['faster_whisper'].processing_time:.2f}秒")
            except Exception as e:
                logger.error(f"Faster Whisper 失败: {e}")
                raise HTTPException(status_code=500, detail=f"Faster Whisper 转录失败: {str(e)}")
        
        if openai_whisper_model is not None:
            logger.info("🎯 开始 OpenAI Whisper 转录...")
            try:
                results["openai_whisper"] = transcribe_with_openai_whisper(audio_array, sample_rate)
                logger.info(f"OpenAI Whisper 完成: {results['openai_whisper'].processing_time:.2f}秒")
            except Exception as e:
                logger.error(f"OpenAI Whisper 失败: {e}")
                raise HTTPException(status_code=500, detail=f"OpenAI Whisper 转录失败: {str(e)}")
        
        if not results:
            raise HTTPException(status_code=500, detail="没有可用的模型")
        
        # 如果只有一个模型可用，创建虚拟结果
        if "faster_whisper" not in results:
            results["faster_whisper"] = SingleTranscriptionResult(
                transcription="模型未加载",
                language="unknown",
                language_probability=0.0,
                segments=[],
                processing_time=0.0,
                model_name="Faster Whisper (未加载)"
            )
        
        if "openai_whisper" not in results:
            results["openai_whisper"] = SingleTranscriptionResult(
                transcription="模型未加载",
                language="unknown",
                language_probability=0.0,
                segments=[],
                processing_time=0.0,
                model_name="OpenAI Whisper (未加载)"
            )
        
        # 比较结果
        comparison = compare_results(results["faster_whisper"], results["openai_whisper"])
        
        logger.info("📊 转录对比完成")
        logger.info(f"Faster Whisper: {results['faster_whisper'].transcription[:50]}...")
        logger.info(f"OpenAI Whisper: {results['openai_whisper'].transcription[:50]}...")
        
        return DualTranscriptionResponse(
            faster_whisper=results["faster_whisper"],
            openai_whisper=results["openai_whisper"],
            comparison=comparison
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"转录错误: {e}")
        raise HTTPException(status_code=500, detail=f"转录失败: {str(e)}")

def main():
    """启动服务器"""
    uvicorn.run(
        "dual_whisper_server:app",
        host="0.0.0.0",
        port=9004,
        reload=False,
        log_level="info"
    )

if __name__ == "__main__":
    main()
