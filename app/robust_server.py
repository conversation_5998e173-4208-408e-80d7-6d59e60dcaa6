#!/usr/bin/env python3
"""
强化版语音转文字服务器 - 专门处理重复问题
"""

import logging
import io
import numpy as np
import soundfile as sf
from flask import Flask, request, jsonify
from flask_cors import CORS
from faster_whisper import WhisperModel
import re

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 创建 Flask 应用
app = Flask(__name__)
CORS(app)

# 全局变量
whisper_model = None

def initialize_whisper_model():
    """初始化 Whisper 模型"""
    global whisper_model
    
    device = "cuda"
    compute_type = "float16"
    
    print("🔍 检查 GPU 可用性...")
    
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            print(f"✅ 检测到 CUDA，GPU 数量: {gpu_count}")
            print(f"🎮 GPU 名称: {gpu_name}")
            print(f"💾 GPU 内存: {gpu_memory:.1f} GB")
        else:
            print("⚠️ CUDA 不可用，回退到 CPU")
            device = "cpu"
            compute_type = "int8"
    except ImportError:
        print("⚠️ PyTorch 未安装，回退到 CPU")
        device = "cpu"
        compute_type = "int8"
    
    try:
        print(f"🔄 正在加载 Whisper 模型: large-v3 (设备: {device}, 计算类型: {compute_type})")
        whisper_model = WhisperModel("large-v3", device=device, compute_type=compute_type)
        print(f"✅ Whisper 模型加载成功 (设备: {device})")
        return True
    except Exception as e:
        print(f"❌ 加载 large-v3 失败: {e}")
        try:
            print("🔄 尝试 base 模型...")
            whisper_model = WhisperModel("base", device=device, compute_type=compute_type)
            print(f"✅ Whisper base 模型加载成功")
            return True
        except Exception as e2:
            print(f"❌ 所有模型都失败: {e2}")
            return False

def remove_repetitions(text):
    """强力去除重复内容"""
    if not text or len(text.strip()) == 0:
        return ""
    
    # 清理文本
    text = text.strip()
    
    # 方法1: 去除连续重复的字符
    text = re.sub(r'(.)\1{3,}', r'\1', text)  # 去除连续4个以上相同字符
    
    # 方法2: 去除重复的词组
    words = text.replace(',', ' ').replace('，', ' ').split()
    if len(words) <= 1:
        return text
    
    # 检测并去除重复模式
    result_words = []
    i = 0
    
    while i < len(words):
        current_word = words[i]
        
        # 检查是否开始重复模式
        pattern_found = False
        
        # 尝试不同长度的模式 (1-5个词)
        for pattern_length in range(1, min(6, len(words) - i)):
            pattern = words[i:i + pattern_length]
            
            # 计算这个模式重复了多少次
            repeat_count = 1
            pos = i + pattern_length
            
            while pos + pattern_length <= len(words):
                if words[pos:pos + pattern_length] == pattern:
                    repeat_count += 1
                    pos += pattern_length
                else:
                    break
            
            # 如果重复超过2次，只保留一次
            if repeat_count > 2:
                result_words.extend(pattern)
                i = pos
                pattern_found = True
                logger.info(f"去除重复模式: {' '.join(pattern)} (重复{repeat_count}次)")
                break
        
        if not pattern_found:
            result_words.append(current_word)
            i += 1
    
    # 重新组合文本
    cleaned_text = ' '.join(result_words)
    
    # 方法3: 去除句子级别的重复
    sentences = re.split(r'[。！？.!?]', cleaned_text)
    unique_sentences = []
    
    for sentence in sentences:
        sentence = sentence.strip()
        if sentence and sentence not in unique_sentences:
            unique_sentences.append(sentence)
    
    final_text = '。'.join(unique_sentences)
    if final_text and not final_text.endswith(('。', '！', '？', '.', '!', '?')):
        final_text += '。'
    
    return final_text

def transcribe_audio(audio_data: bytes) -> dict:
    """使用 Faster Whisper 进行语音转文字"""
    global whisper_model
    
    if whisper_model is None:
        raise Exception("Whisper 模型未初始化")
    
    try:
        # 音频预处理
        audio_io = io.BytesIO(audio_data)
        audio_array, sample_rate = sf.read(audio_io, dtype='float32')
        
        logger.info(f"音频信息: 采样率={sample_rate}, 形状={audio_array.shape}, 时长={len(audio_array)/sample_rate:.2f}秒")
        
        # 确保单声道
        if len(audio_array.shape) > 1:
            audio_array = np.mean(audio_array, axis=1)
        
        # 数据预处理
        audio_array = np.array(audio_array, dtype=np.float32)
        max_val = np.max(np.abs(audio_array))
        if max_val > 0:
            audio_array = audio_array / max_val
        audio_array = np.nan_to_num(audio_array, nan=0.0, posinf=1.0, neginf=-1.0)
        
        # 多策略转录
        strategies = [
            # 策略1: 保守参数 + 中文
            {
                "language": "zh",
                "beam_size": 1,
                "best_of": 1,
                "temperature": 0.0,
                "condition_on_previous_text": False,
                "vad_filter": True,
                "vad_parameters": dict(threshold=0.5, min_speech_duration_ms=250)
            },
            # 策略2: 无VAD + 中文
            {
                "language": "zh",
                "beam_size": 1,
                "best_of": 1,
                "temperature": 0.1,
                "condition_on_previous_text": False,
                "vad_filter": False
            },
            # 策略3: 自动检测语言
            {
                "language": None,
                "beam_size": 1,
                "best_of": 1,
                "temperature": 0.0,
                "condition_on_previous_text": False,
                "vad_filter": False
            }
        ]
        
        best_result = None
        best_score = -1
        
        for i, params in enumerate(strategies):
            try:
                logger.info(f"尝试策略 {i+1}: {params}")
                segments, info = whisper_model.transcribe(audio_array, **params)
                
                # 收集原始结果
                raw_text = ""
                segments_list = []
                
                for segment in segments:
                    raw_text += segment.text
                    segments_list.append({
                        "start": segment.start,
                        "end": segment.end,
                        "text": segment.text
                    })
                
                # 清理重复
                cleaned_text = remove_repetitions(raw_text)
                
                # 评估质量
                score = evaluate_result_quality(cleaned_text, raw_text, info)
                
                logger.info(f"策略 {i+1} - 原始长度: {len(raw_text)}, 清理后: {len(cleaned_text)}, 分数: {score:.3f}")
                logger.info(f"策略 {i+1} - 清理后文本: {cleaned_text[:100]}...")
                
                if score > best_score:
                    best_score = score
                    best_result = {
                        "transcription": cleaned_text,
                        "raw_transcription": raw_text,
                        "language": info.language,
                        "language_probability": info.language_probability,
                        "segments": segments_list,
                        "strategy": i + 1,
                        "quality_score": score
                    }
                
                # 如果找到高质量结果，提前退出
                if score > 0.8:
                    logger.info(f"找到高质量结果，使用策略 {i+1}")
                    break
                    
            except Exception as e:
                logger.warning(f"策略 {i+1} 失败: {e}")
                continue
        
        if best_result is None:
            raise Exception("所有转录策略都失败了")
        
        logger.info(f"最终选择策略 {best_result['strategy']}, 质量分数: {best_result['quality_score']:.3f}")
        return best_result
        
    except Exception as e:
        logger.error(f"转录错误: {e}")
        raise Exception(f"转录失败: {e}")

def evaluate_result_quality(cleaned_text, raw_text, info):
    """评估转录结果质量"""
    score = 0.0
    
    # 基础分数
    if len(cleaned_text.strip()) > 0:
        score += 0.2
    
    # 语言匹配
    if info.language in ['zh', 'zh-cn', 'zh-tw']:
        score += 0.3
    
    # 重复改善程度
    if len(raw_text) > 0:
        improvement_ratio = 1 - (len(cleaned_text) / len(raw_text))
        if improvement_ratio > 0.5:  # 显著减少了长度
            score += 0.3
        elif improvement_ratio > 0.2:
            score += 0.1
    
    # 中文字符比例
    if len(cleaned_text) > 0:
        chinese_chars = sum(1 for char in cleaned_text if '\u4e00' <= char <= '\u9fff')
        chinese_ratio = chinese_chars / len(cleaned_text)
        score += chinese_ratio * 0.2
    
    return min(score, 1.0)

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查端点"""
    return jsonify({
        "status": "healthy",
        "model_loaded": whisper_model is not None,
        "timestamp": "2025-07-18T00:00:00Z",
        "version": "robust-v1"
    })

@app.route('/api/transcribe', methods=['POST'])
def transcribe_file():
    """文件转录端点"""
    try:
        if 'audio_file' not in request.files:
            return jsonify({"error": "没有上传音频文件"}), 400
        
        audio_file = request.files['audio_file']
        if audio_file.filename == '':
            return jsonify({"error": "没有选择文件"}), 400
        
        # 读取音频数据
        audio_data = audio_file.read()
        logger.info(f"收到音频文件: {audio_file.filename}, 大小: {len(audio_data)} bytes")
        
        # 进行转录
        result = transcribe_audio(audio_data)
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"文件转录错误: {e}")
        return jsonify({"error": str(e)}), 500

def main():
    print("🚀 启动强化版语音转文字服务器...")
    print("📋 配置信息:")
    print("   - 模型: Whisper large-v3")
    print("   - 特性: 强力反重复处理")
    print("   - 设备: GPU (CUDA) 优先")
    print("   - 端口: 9004")
    
    if initialize_whisper_model():
        print("✅ 模型初始化成功!")
        print("\n🌐 启动 Flask 服务器...")
        print("   访问地址: http://localhost:9004")
        print("   健康检查: http://localhost:9004/health")
        print("-" * 50)
        
        try:
            app.run(host="0.0.0.0", port=9004, debug=False)
        except KeyboardInterrupt:
            print("\n👋 服务器已停止")
    else:
        print("❌ 模型初始化失败!")

if __name__ == "__main__":
    main()
