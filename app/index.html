<!DOCTYPE html>
<html lang="zh-TW">
<head>
  <meta charset="UTF-8">
  <title>系統影音及 STT 即時串流 & 音訊轉文字</title>
  <!-- 引入 FFmpeg.wasm -->
  <script src="https://unpkg.com/@ffmpeg/ffmpeg@0.11.6/dist/ffmpeg.min.js"></script>
  <style>
    body {
      background-color: pink;
      font-family: Arial, sans-serif;
      margin: 0 auto;
      padding: 20px;
      max-width: 1200px;
    }
    
    button, .btn {
      margin: 5px 10px 5px 0;
      padding: 10px 20px;
      font-size: 16px;
      cursor: pointer;
      background-color: #33d413;
      color: rgb(48, 16, 224);
      border: none;
      border-radius: 4px;
      text-decoration: none;
      display: inline-block;
    }

    button:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
    }

    select {
      margin: 5px 10px 5px 0;
      padding: 5px;
      font-size: 16px;
    }

    video {
      display: block;
      margin: 20px 0;
      max-width: 100%;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    textarea {
      width: 100%;
      height: 150px;
      margin-top: 10px;
      font-size: 16px;
      padding: 10px;
      box-sizing: border-box;
    }

    #recordingsList, #sttSection, #uploadSection {
      margin-top: 20px;
    }

    .recording-item {
      margin-bottom: 30px;
      padding: 20px;
      background-color: #f8f9fa;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
  </style>
</head>
<body>
  <h1>系統影音及 STT 即時串流 (含系統聲音與麥克風音訊) & 音訊轉文字</h1>
  
  <!-- 第一部分：系統影音及麥克風捕捉 (錄製影片) -->
  <section id="captureSection">
    <h2>【一】 系統影音及麥克風捕捉 (錄製影片)</h2>
    <label for="videoQuality">選擇影像品質：</label>
    <select id="videoQuality">
      <option value="1920,1080,60">1080p 60fps</option>
      <option value="1280,720,30">720p 30fps</option>
      <option value="640,480,15">480p 15fps</option>
    </select>

    <label for="audioQuality">選擇音頻品質：</label>
    <select id="audioQuality">
      <option value="192000,44101">192 kbps, 44.1 kHz</option>
      <option value="128000,44101">128 kbps, 44.1 kHz</option>
      <option value="64000,22050">64 kbps, 22.05 kHz</option>
    </select>

    <button id="startCapture">開始捕捉 (錄製影片)</button>
    <button id="stopCapture" disabled>停止捕捉 (錄製影片)</button>
    
    <h3>錄製結果</h3>
    <div id="recordingsList"></div>
  </section>

  <hr>

  <!-- 第二部分：STT 即時串流 -->
  <section id="sttSection">
    <h2>【二】 語音轉文字 (STT 即時串流)</h2>
    <p>本功能會捕捉系統音與麥克風音訊混合後，以約 2 秒為一段送往 ASR API 進行轉錄，並即時顯示文字結果。</p>
    <button id="startSTT">開始 STT 即時串流</button>
    <button id="stopSTT" disabled>停止 STT 即時串流</button>
    <textarea id="sttResult" placeholder="STT 轉錄結果將顯示在此..." readonly></textarea>
  </section>

  <hr>

  <!-- 第三部分：上傳音訊文件進行轉錄 -->
  <section id="uploadSection">
    <h2>上傳音訊文件進行轉錄</h2>
    <!-- 隱藏原生檔案輸入，並以 label 模擬按鈕 -->
    <label for="audioFile" class="btn">選擇檔案</label>
    <input type="file" id="audioFile" accept="audio/*" style="display:none;">
    <span id="fileNameDisplay"></span>
    <button id="uploadBtn">上傳並轉錄</button>
    <textarea id="result" placeholder="轉錄結果將顯示在此..." readonly></textarea>
  </section>

  <script>
    // ========== FFmpeg 初始化 ==========
    const { createFFmpeg, fetchFile } = FFmpeg;
    const ffmpeg = createFFmpeg({ log: true });
    let ffmpegLoaded = false;
    async function loadFFmpeg() {
      if (!ffmpegLoaded) {
        await ffmpeg.load();
        ffmpegLoaded = true;
      }
    }

    // ========== 【一】 系統影音及麥克風捕捉 (錄製影片) ==========
    const startCaptureButton = document.getElementById('startCapture');
    const stopCaptureButton = document.getElementById('stopCapture');
    const videoQualitySelect = document.getElementById('videoQuality');
    const audioQualitySelect = document.getElementById('audioQuality');
    const recordingsList = document.getElementById('recordingsList');

    let mediaRecorder;
    let recordedChunks = [];
    let capturedStream; // 螢幕及系統聲音串流
    let micStream;      // 麥克風串流
    let audioContext;

    startCaptureButton.addEventListener('click', async () => {
      try {
        const [videoWidth, videoHeight, frameRate] = videoQualitySelect.value.split(',').map(Number);
        const [audioBitRate, audioSampleRate] = audioQualitySelect.value.split(',').map(Number);

        // 捕捉螢幕影音（含系統聲音）
        capturedStream = await navigator.mediaDevices.getDisplayMedia({
          video: {
            width: { ideal: videoWidth },
            height: { ideal: videoHeight },
            frameRate: { ideal: frameRate }
          },
          audio: {
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false,
            sampleRate: audioSampleRate,
            channelCount: 2
          }
        });

        // 捕捉本地麥克風音訊
        micStream = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false
          }
        });

        // 建立 AudioContext 與混合目的地
        audioContext = new AudioContext();
        const destination = audioContext.createMediaStreamDestination();

        if (capturedStream.getAudioTracks().length > 0) {
          const systemAudioSource = audioContext.createMediaStreamSource(capturedStream);
          systemAudioSource.connect(destination);
        }
        if (micStream.getAudioTracks().length > 0) {
          const micAudioSource = audioContext.createMediaStreamSource(micStream);
          micAudioSource.connect(destination);
        }

        // 最終混合串流包含螢幕視訊軌與混合後的音訊軌
        const finalStream = new MediaStream([
          ...capturedStream.getVideoTracks(),
          ...destination.stream.getAudioTracks()
        ]);

        mediaRecorder = new MediaRecorder(finalStream, {
          mimeType: 'video/webm;codecs=vp8,opus',
          videoBitsPerSecond: videoWidth * videoHeight * frameRate * 0.07,
          audioBitsPerSecond: audioBitRate
        });

        mediaRecorder.ondataavailable = event => {
          if (event.data.size > 0) {
            recordedChunks.push(event.data);
          }
        };

        mediaRecorder.onstop = () => {
          const videoBlob = new Blob(recordedChunks, { type: 'video/webm' });
          const videoUrl = URL.createObjectURL(videoBlob);
          const listItem = document.createElement('div');
          listItem.className = 'recording-item';

          const video = document.createElement('video');
          video.controls = true;
          video.src = videoUrl;
          listItem.appendChild(video);

          const downloadButton = document.createElement('button');
          downloadButton.textContent = '下載錄影';
          downloadButton.onclick = () => {
            const a = document.createElement('a');
            a.href = videoUrl;
            a.download = `recording_${Date.now()}.webm`;
            a.click();
          };
          listItem.appendChild(downloadButton);

          recordingsList.appendChild(listItem);
          recordedChunks = [];
        };

        mediaRecorder.start();
        startCaptureButton.disabled = true;
        stopCaptureButton.disabled = false;
      } catch (err) {
        console.error('捕捉失敗:', err);
        alert(`無法捕捉影音。錯誤訊息: ${err.message}`);
      }
    });

    stopCaptureButton.addEventListener('click', () => {
      if (mediaRecorder && mediaRecorder.state !== 'inactive') {
        mediaRecorder.stop();
        startCaptureButton.disabled = false;
        stopCaptureButton.disabled = true;
      }
      if (capturedStream) {
        capturedStream.getTracks().forEach(track => track.stop());
      }
      if (micStream) {
        micStream.getTracks().forEach(track => track.stop());
      }
      if (audioContext) {
        audioContext.close();
      }
    });

    // ========== 【二】 STT 即時串流功能 ==========
    const startSTTButton = document.getElementById('startSTT');
    const stopSTTButton = document.getElementById('stopSTT');
    const sttResultArea = document.getElementById('sttResult');

    let sttMediaRecorder;
    let sttStream;
    let sttAudioContext; // 用於混合 STT 音源

    startSTTButton.addEventListener('click', async () => {
      try {
        // 取得音頻設定（參考同一個下拉選單）
        const [audioBitRate, audioSampleRate] = audioQualitySelect.value.split(',').map(Number);

        // 捕捉系統音 (僅音頻)
        const systemStream = await navigator.mediaDevices.getDisplayMedia({
          video: false,
          audio: {
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false,
            sampleRate: audioSampleRate,
            channelCount: 2
          }
        });

        // 捕捉麥克風音訊
        const microphoneStream = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false
          }
        });

        // 建立 AudioContext 並混合兩路音訊
        sttAudioContext = new AudioContext();
        const sttDestination = sttAudioContext.createMediaStreamDestination();

        if (systemStream.getAudioTracks().length > 0) {
          const systemSource = sttAudioContext.createMediaStreamSource(systemStream);
          systemSource.connect(sttDestination);
        }
        if (microphoneStream.getAudioTracks().length > 0) {
          const micSource = sttAudioContext.createMediaStreamSource(microphoneStream);
          micSource.connect(sttDestination);
        }

        // 最終 STT 混合串流 (僅音頻)
        sttStream = sttDestination.stream;

        // 初始化 ffmpeg（若尚未載入）
        await loadFFmpeg();

        // 建立 MediaRecorder，設定 timeslice 參數（每 2 秒取得一個片段）
        sttMediaRecorder = new MediaRecorder(sttStream, { mimeType: 'audio/webm' });
        sttMediaRecorder.ondataavailable = async (event) => {
          if (event.data && event.data.size > 0) {
            // 處理每一段錄製的音頻片段
            processSTTChunk(event.data);
          }
        };

        sttMediaRecorder.start(2000);
        startSTTButton.disabled = true;
        stopSTTButton.disabled = false;
        sttResultArea.value = "開始 STT 串流...\n";
      } catch (err) {
        console.error("STT 即時串流啟動錯誤:", err);
        alert("無法啟動 STT 即時串流: " + err.message);
      }
    });

    stopSTTButton.addEventListener('click', () => {
      if (sttMediaRecorder && sttMediaRecorder.state !== 'inactive') {
        sttMediaRecorder.stop();
        startSTTButton.disabled = false;
        stopSTTButton.disabled = true;
      }
      if (sttStream) {
        sttStream.getTracks().forEach(track => track.stop());
      }
      if (sttAudioContext) {
        sttAudioContext.close();
      }
    });

    // 處理 STT 片段：使用 ffmpeg.wasm 轉換 WebM 為 WAV，再以 POST 傳送到 ASR API
    async function processSTTChunk(blob) {
      try {
        // 寫入檔案至 ffmpeg 虛擬檔案系統
        ffmpeg.FS('writeFile', 'input.webm', await fetchFile(blob));
        // 轉檔參數：輸出 16kHz、單聲道 WAV 檔
        await ffmpeg.run('-i', 'input.webm', '-ar', '16000', '-ac', '1', 'output.wav');
        const wavData = ffmpeg.FS('readFile', 'output.wav');
        const wavBlob = new Blob([wavData.buffer], { type: 'audio/wav' });

        // 清除暫存檔案
        ffmpeg.FS('unlink', 'input.webm');
        ffmpeg.FS('unlink', 'output.wav');

        // 建立 FormData 並送出至 ASR API
        const formData = new FormData();
        formData.append('audio_file', wavBlob, 'audio.wav');

        // 將此處的 URL 由 http://localhost:9002/asr 改為 http://localhost:9004/asr
        const response = await fetch('http://localhost:9004/asr', {
          method: 'POST',
          body: formData
        });

        if (response.ok) {
          const transcription = await response.text();
          // 將轉錄結果即時附加到文字區
          sttResultArea.value += transcription + "\n";
        } else {
          sttResultArea.value += "ASR 請求失敗，狀態碼: " + response.status + "\n";
        }
      } catch (e) {
        console.error("處理 STT 片段錯誤:", e);
        sttResultArea.value += "處理 STT 片段錯誤: " + e.message + "\n";
      }
    }
  </script>

  <script>
    // ========== 第三部分：上傳音訊文件進行轉錄 ==========
    const uploadBtn = document.getElementById("uploadBtn");
    const resultArea = document.getElementById("result");
    const fileInput = document.getElementById("audioFile");
    const fileNameDisplay = document.getElementById("fileNameDisplay");

    // 當選擇檔案後，顯示檔案名稱
    fileInput.addEventListener("change", function() {
      if (fileInput.files.length > 0) {
         fileNameDisplay.textContent = fileInput.files[0].name;
      } else {
         fileNameDisplay.textContent = "";
      }
    });

    uploadBtn.addEventListener("click", function() {
      if (fileInput.files.length === 0) {
        alert("請選擇檔案");
        return;
      }
      const formData = new FormData();
      formData.append("audio_file", fileInput.files[0]);

      // 顯示轉錄中訊息
      resultArea.value = "正在轉錄...";

      fetch("/transcribe", {
        method: "POST",
        body: formData
      })
      .then(response => response.json())
      .then(data => {
        if (data.error) {
          resultArea.value = "錯誤：" + data.error;
        } else {
          resultArea.value = "轉錄結果：" + data.transcription;
        }
      })
      .catch(error => {
        resultArea.value = "錯誤：" + error;
      });
    });
  </script>
</body>
</html>

