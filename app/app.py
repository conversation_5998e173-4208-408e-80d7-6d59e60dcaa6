import os
import io
import json
import asyncio
import logging
from datetime import datetime
from typing import Optional, Dict, Any

from flask import Flask, request, jsonify, render_template
from flask_socketio import Socket<PERSON>, emit
from flask_cors import CORS
import numpy as np
import soundfile as sf
from faster_whisper import WhisperModel

# 配置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 創建 Flask 應用
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'

# 啟用 CORS 和 SocketIO
CORS(app, origins=["http://localhost:3000", "http://localhost:9004"])
socketio = SocketIO(app, cors_allowed_origins=["http://localhost:3000", "http://localhost:9004"])

# Faster Whisper 模型配置
MODEL_SIZE = os.getenv('WHISPER_MODEL_SIZE', 'base')  # tiny, base, small, medium, large
DEVICE = os.getenv('WHISPER_DEVICE', 'cpu')  # cpu, cuda
COMPUTE_TYPE = os.getenv('WHISPER_COMPUTE_TYPE', 'int8')  # int8, float16, float32

# 全局變量
whisper_model: Optional[WhisperModel] = None
active_connections: Dict[str, Any] = {}

def initialize_whisper_model():
    """初始化 Faster Whisper 模型"""
    global whisper_model
    try:
        logger.info(f"正在載入 Whisper 模型: {MODEL_SIZE} (設備: {DEVICE})")
        whisper_model = WhisperModel(
            MODEL_SIZE,
            device=DEVICE,
            compute_type=COMPUTE_TYPE,
            download_root="./models"
        )
        logger.info("Whisper 模型載入成功")
        return True
    except Exception as e:
        logger.error(f"載入 Whisper 模型失敗: {e}")
        return False

def transcribe_audio(audio_data: bytes, language: str = "zh") -> Dict[str, Any]:
    """使用 Faster Whisper 進行語音轉文字"""
    global whisper_model

    if whisper_model is None:
        raise Exception("Whisper 模型未初始化")

    try:
        # 將音頻數據轉換為 numpy 數組
        audio_io = io.BytesIO(audio_data)

        # 嘗試不同的音頻格式
        try:
            audio_array, sample_rate = sf.read(audio_io, dtype='float32')
        except Exception as e:
            logger.warning(f"soundfile 讀取失敗: {e}, 嘗試其他方法")
            # 重置 BytesIO 位置
            audio_io.seek(0)

            # 嘗試使用 librosa（如果可用）
            try:
                import librosa
                audio_array, sample_rate = librosa.load(audio_io, sr=None, dtype=np.float32)
            except ImportError:
                # 如果沒有 librosa，嘗試直接讀取為 WAV
                import wave
                audio_io.seek(0)
                with wave.open(audio_io, 'rb') as wav_file:
                    frames = wav_file.readframes(-1)
                    sample_rate = wav_file.getframerate()
                    audio_array = np.frombuffer(frames, dtype=np.int16).astype(np.float32) / 32768.0

        logger.info(f"音頻信息: 採樣率={sample_rate}, 形狀={audio_array.shape}, 數據類型={audio_array.dtype}, 時長={len(audio_array)/sample_rate:.2f}秒")

        # 確保音頻是單聲道
        if len(audio_array.shape) > 1:
            audio_array = np.mean(audio_array, axis=1)

        # 確保數據類型為 float32 並且在正確範圍內
        audio_array = np.array(audio_array, dtype=np.float32)

        # 音頻預處理：正規化到 [-1, 1] 範圍
        max_val = np.max(np.abs(audio_array))
        if max_val > 0:
            audio_array = audio_array / max_val

        # 確保沒有 NaN 或 inf 值
        audio_array = np.nan_to_num(audio_array, nan=0.0, posinf=1.0, neginf=-1.0)

        # 進行轉錄 - 使用簡化但有效的參數
        logger.info(f"開始轉錄，語言: {language}")

        segments, info = whisper_model.transcribe(
            audio_array,
            language=None,  # 讓模型自動檢測語言
            beam_size=5,    # 增加 beam_size 提高質量
            best_of=5,      # 增加 best_of 提高質量
            temperature=0.2,  # 使用較低但非零的溫度
            condition_on_previous_text=False,
            vad_filter=False
        )

        logger.info(f"檢測到語言: {info.language}, 概率: {info.language_probability:.3f}")

        # 收集轉錄結果
        transcription_text = ""
        segments_list = []

        for segment in segments:
            transcription_text += segment.text
            segments_list.append({
                "start": segment.start,
                "end": segment.end,
                "text": segment.text,
                "avg_logprob": segment.avg_logprob,
                "no_speech_prob": segment.no_speech_prob
            })

        return {
            "text": transcription_text.strip(),
            "language": info.language,
            "language_probability": info.language_probability,
            "duration": info.duration,
            "segments": segments_list
        }

    except Exception as e:
        logger.error(f"轉錄錯誤: {e}")
        raise Exception(f"轉錄失敗: {str(e)}")

# Flask 路由
@app.route('/')
def index():
    """主頁面"""
    return render_template('index.html')

@app.route('/api/transcribe', methods=['POST'])
def transcribe_file():
    """文件上傳轉錄 API"""
    try:
        if 'audio_file' not in request.files:
            return jsonify({'error': '沒有上傳音頻文件'}), 400

        file = request.files['audio_file']
        if file.filename == '':
            return jsonify({'error': '沒有選擇文件'}), 400

        # 讀取音頻數據
        audio_data = file.read()

        # 進行轉錄
        result = transcribe_audio(audio_data)

        return jsonify({
            'transcription': result['text'],
            'language': result['language'],
            'duration': result['duration'],
            'segments': result['segments']
        })

    except Exception as e:
        logger.error(f"文件轉錄錯誤: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/health')
def health_check():
    """健康檢查"""
    return jsonify({
        'status': 'healthy',
        'model_loaded': whisper_model is not None,
        'timestamp': datetime.now().isoformat()
    })

# WebSocket 事件處理
@socketio.on('connect')
def handle_connect():
    """客戶端連接"""
    client_id = request.sid
    active_connections[client_id] = {
        'connected_at': datetime.now(),
        'transcription_count': 0
    }
    logger.info(f"客戶端連接: {client_id}")
    emit('status', {'message': '已連接到語音轉文字服務'})

@socketio.on('disconnect')
def handle_disconnect():
    """客戶端斷開連接"""
    client_id = request.sid
    if client_id in active_connections:
        del active_connections[client_id]
    logger.info(f"客戶端斷開連接: {client_id}")

@socketio.on('audio_data')
def handle_audio_data(data):
    """處理實時音頻數據"""
    client_id = request.sid

    try:
        # 進行轉錄
        result = transcribe_audio(data)

        # 更新連接統計
        if client_id in active_connections:
            active_connections[client_id]['transcription_count'] += 1

        # 發送轉錄結果
        emit('transcription', {
            'id': f"{client_id}_{datetime.now().timestamp()}",
            'text': result['text'],
            'timestamp': datetime.now().timestamp() * 1000,
            'confidence': 1.0 - (result.get('segments', [{}])[0].get('no_speech_prob', 0) if result.get('segments') else 0),
            'language': result['language']
        })

    except Exception as e:
        logger.error(f"實時轉錄錯誤: {e}")
        emit('error', {'message': f'轉錄錯誤: {str(e)}'})

if __name__ == "__main__":
    # 初始化 Whisper 模型
    if initialize_whisper_model():
        logger.info("啟動 Flask-SocketIO 服務器...")
        socketio.run(app, host="0.0.0.0", port=9004, debug=True)
    else:
        logger.error("無法啟動服務器：Whisper 模型載入失敗")

