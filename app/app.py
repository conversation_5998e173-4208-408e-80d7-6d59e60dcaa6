import requests

# 設定 ASR Webservice 的 URL
# 修改前（可能參考您原本的設定）
# ASR_URL = 'http://localhost:9002/asr'

# 修改後，改成 9004 與 Nginx 反向代理一致
ASR_URL = 'http://localhost:9004/asr'


# 測試用的音訊檔案路徑，請確認此檔案存在且格式正確
AUDIO_FILE_PATH = 'sample.wav'

def test_asr_service():
    try:
        # 以二進位模式開啟音訊檔案
        with open(AUDIO_FILE_PATH, 'rb') as audio_file:
            # 注意這裡的 key 已修改為 "audio_file"，符合服務端需求
            files = {'audio_file': audio_file}
            print(f"正在向 {ASR_URL} 傳送請求...")
            
            # 發送 POST 請求，上傳音訊檔案
            response = requests.post(ASR_URL, files=files)

        # 檢查 HTTP 回應狀態碼
        if response.status_code == 200:
            print("ASR Webservice 正常運作！")
            print("回應內容：")
            print(response.text)
        else:
            print(f"回應狀態碼: {response.status_code}")
            print("發生錯誤，請確認服務是否正常運作。")
            print("回應內容：")
            print(response.text)

    except FileNotFoundError:
        print(f"找不到音訊檔案：{AUDIO_FILE_PATH}")
    except Exception as e:
        print("呼叫 ASR Webservice 時發生例外錯誤：", e)

   
if __name__ == "__main__":
    app.run(host="0.0.0.0", port=9004, debug=True)

