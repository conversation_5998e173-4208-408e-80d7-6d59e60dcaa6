FROM python:3.10-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    gcc \
    g++ \
    portaudio19-dev \
    ffmpeg \
    libsndfile1 \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# 复制并安装 Python 依赖
COPY requirements.txt .
RUN pip install --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# 创建模型目录
RUN mkdir -p /app/models

# 复制应用代码
COPY . /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV WHISPER_MODEL_SIZE=base
ENV WHISPER_DEVICE=cpu
ENV WHISPER_COMPUTE_TYPE=int8

# 对外开放 9004 端口
EXPOSE 9004

# 启动 Flask-SocketIO 应用
CMD ["python", "app.py"]

