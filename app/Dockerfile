FROM python:3.9-slim

# 安裝必要的編譯工具與開發庫
RUN apt-get update && apt-get install -y \
    build-essential \
    gcc \
    g++ \
    portaudio19-dev \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# 複製程式碼（建立映像時可備份，但啟動時將被 volume 掛載覆蓋）
COPY . /app

# 更新 pip 並安裝需求
RUN pip install --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# 對外開放 9004 埠
EXPOSE 9004

# 啟動 Flask 應用程式（請確保 app.py 中有建立 Flask 實例並正確綁定）
CMD ["python", "app.py"]

