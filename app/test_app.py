#!/usr/bin/env python3
"""
简化的测试应用
"""

import logging
from flask import Flask, request, jsonify
from flask_cors import CORS
import io
import soundfile as sf
import numpy as np
from faster_whisper import WhisperModel

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# 全局变量
whisper_model = None

def initialize_whisper_model():
    """初始化 Whisper 模型"""
    global whisper_model

    device = "cuda"
    compute_type = "float16"

    # 檢查 GPU 可用性
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            logger.info(f"檢測到 CUDA，GPU 數量: {gpu_count}")
            logger.info(f"GPU 名稱: {gpu_name}")
            logger.info(f"GPU 記憶體: {gpu_memory:.1f} GB")
        else:
            logger.warning("CUDA 不可用，回退到 CPU")
            device = "cpu"
            compute_type = "int8"
    except ImportError:
        logger.warning("PyTorch 未安裝，回退到 CPU")
        device = "cpu"
        compute_type = "int8"

    try:
        logger.info(f"正在載入 Whisper 模型: base (設備: {device}, 計算類型: {compute_type})")
        whisper_model = WhisperModel("base", device=device, compute_type=compute_type)
        logger.info(f"Whisper 模型載入成功 (設備: {device})")
        return True
    except Exception as e:
        logger.error(f"載入 Whisper 模型失敗: {e}")
        # 如果 GPU 失敗，嘗試 CPU
        if device == "cuda":
            logger.info("嘗試使用 CPU 載入模型...")
            try:
                whisper_model = WhisperModel("base", device="cpu", compute_type="int8")
                logger.info("Whisper 模型載入成功 (CPU 回退)")
                return True
            except Exception as cpu_e:
                logger.error(f"CPU 回退也失敗: {cpu_e}")
        return False

def transcribe_audio(audio_data: bytes, language: str = "zh") -> dict:
    """使用 Faster Whisper 進行語音轉文字"""
    global whisper_model
    
    if whisper_model is None:
        raise Exception("Whisper 模型未初始化")
    
    try:
        # 將音頻數據轉換為 numpy 數組
        audio_io = io.BytesIO(audio_data)
        audio_array, sample_rate = sf.read(audio_io, dtype='float32')
        
        logger.info(f"音頻信息: 採樣率={sample_rate}, 形狀={audio_array.shape}, 數據類型={audio_array.dtype}, 時長={len(audio_array)/sample_rate:.2f}秒")
        
        # 確保音頻是單聲道
        if len(audio_array.shape) > 1:
            audio_array = np.mean(audio_array, axis=1)
        
        # 確保數據類型為 float32 並且在正確範圍內
        audio_array = np.array(audio_array, dtype=np.float32)
        
        # 音頻預處理：正規化到 [-1, 1] 範圍
        max_val = np.max(np.abs(audio_array))
        if max_val > 0:
            audio_array = audio_array / max_val
        
        # 確保沒有 NaN 或 inf 值
        audio_array = np.nan_to_num(audio_array, nan=0.0, posinf=1.0, neginf=-1.0)
        
        # 進行轉錄
        logger.info(f"開始轉錄，語言: {language}")
        
        segments, info = whisper_model.transcribe(
            audio_array,
            language=None,  # 讓模型自動檢測語言
            beam_size=5,    # 增加 beam_size 提高質量
            best_of=5,      # 增加 best_of 提高質量
            temperature=0.2,  # 使用較低但非零的溫度
            condition_on_previous_text=False,
            vad_filter=False
        )
        
        logger.info(f"檢測到語言: {info.language}, 概率: {info.language_probability:.3f}")
        
        # 收集轉錄結果
        transcription_text = ""
        segments_list = []
        
        for segment in segments:
            transcription_text += segment.text
            segments_list.append({
                "start": segment.start,
                "end": segment.end,
                "text": segment.text
            })
        
        logger.info(f"轉錄完成，結果長度: {len(transcription_text)}")
        logger.info(f"轉錄結果: {transcription_text[:100]}...")  # 只顯示前100個字符
        
        return {
            "transcription": transcription_text.strip(),
            "language": info.language,
            "language_probability": info.language_probability,
            "segments": segments_list
        }
        
    except Exception as e:
        logger.error(f"轉錄錯誤: {e}")
        raise Exception(f"轉錄失敗: {e}")

@app.route('/health', methods=['GET'])
def health_check():
    """健康檢查端點"""
    return jsonify({
        "status": "healthy",
        "model_loaded": whisper_model is not None,
        "timestamp": "2025-07-18T00:00:00Z"
    })

@app.route('/api/transcribe', methods=['POST'])
def transcribe_file():
    """文件轉錄端點"""
    try:
        if 'audio_file' not in request.files:
            return jsonify({"error": "沒有上傳音頻文件"}), 400
        
        audio_file = request.files['audio_file']
        if audio_file.filename == '':
            return jsonify({"error": "沒有選擇文件"}), 400
        
        # 讀取音頻數據
        audio_data = audio_file.read()
        logger.info(f"收到音頻文件: {audio_file.filename}, 大小: {len(audio_data)} bytes")
        
        # 進行轉錄
        result = transcribe_audio(audio_data)
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"文件轉錄錯誤: {e}")
        return jsonify({"error": str(e)}), 500

if __name__ == "__main__":
    print("Starting test application...")
    
    # 初始化 Whisper 模型
    if initialize_whisper_model():
        print("Model initialized successfully. Starting server...")
        app.run(host="0.0.0.0", port=9004, debug=True)
    else:
        print("Failed to initialize model")
