#!/usr/bin/env python3
"""
OpenAI Whisper GPU FastAPI 服务器
专门使用 OpenAI Whisper 进行语音转文字
(基於原有架構的優化版)
"""

import logging
import io
import numpy as np
import soundfile as sf
from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
import whisper  # OpenAI Whisper
import torch
from typing import Optional, Dict, Any, List
from pydantic import BaseModel
import uvicorn
import time
import tempfile
import os
import httpx
import asyncio

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 创建 FastAPI 应用
app = FastAPI(
    title="OpenAI Whisper GPU API (優化版)",
    description="基於原有架構優化的高質量語音轉文字服務，具備智慧內存管理。",
    version="3.0.1"
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加静态文件服务
app.mount("/static", StaticFiles(directory="static"), name="static")

# 全局变量
whisper_models: Dict[str, Any] = {}  # 存儲多個模型 (優化後，此字典在任何時候只會包含一個模型以節省內存)
current_model_name: str = "large"   # 【優化】這裡設定您希望伺服器啟動時預加載的模型
device: str = "cpu"

# 響應模型 (保持不變)
class HealthResponse(BaseModel):
    status: str
    model_loaded: bool
    timestamp: str
    device: str
    model_name: str
    gpu_info: Optional[str] = None

class TranscriptionRequest(BaseModel):
    transcription_language: Optional[str] = "auto"

class TranscriptionResponse(BaseModel):
    transcription: str
    language: str
    detected_language: str
    segments: List[dict]
    processing_time: float
    model_name: str
    device_used: str
    transcription_language: str

class ModelSwitchResponse(BaseModel):
    success: bool
    message: str
    current_model: str
    available_models: List[str]

class TranslationRequest(BaseModel):
    text: str
    target_language: str

class TranslationResponse(BaseModel):
    original_text: str
    translated_text: str
    target_language: str
    processing_time: float

def initialize_whisper_model() -> bool:
    """
    【優化】初始化 OpenAI Whisper 模型。
    此函數現在負責檢測硬體環境，並調用核心的 load_model 函數來加載初始模型。
    """
    global device, current_model_name

    logger.info("🔍 檢查 GPU 可用性...")
    if torch.cuda.is_available():
        device = "cuda"
        gpu_count = torch.cuda.device_count()
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        logger.info(f"✅ 檢測到 CUDA，GPU 數量: {gpu_count}, 名稱: {gpu_name}, 內存: {gpu_memory:.1f} GB")
    else:
        device = "cpu"
        logger.warning("⚠️ CUDA 不可用，將使用 CPU。")

    # 【優化】直接調用 load_model 來加載在全局變量中定義的初始模型
    # 這使得配置更集中，避免了在函數內部寫死模型名稱 ("base" 或 "small")
    logger.info(f"🚀 準備加載初始模型: '{current_model_name}'")
    return load_model(current_model_name)

def load_model(model_name: str) -> bool:
    """
    【重大優化】動態加載指定的模型，並智慧地卸載舊模型以釋放內存。
    此函數是伺服器能夠長時間穩定運行的關鍵。
    """
    global whisper_models, current_model_name, device

    # 如果要加載的模型已經是當前模型，則無需任何操作
    if model_name == current_model_name and model_name in whisper_models:
        logger.info(f"✅ 模型 '{model_name}' 已經是當前加載的模型，無需切換。")
        return True

    # 步驟 1: 卸載舊模型 (如果存在)
    # 這是為了防止內存/顯存溢出
    if current_model_name in whisper_models:
        logger.info(f"🧹 正在卸載舊模型 '{current_model_name}' 以釋放資源...")
        del whisper_models[current_model_name]
        if device == "cuda":
            torch.cuda.empty_cache()  # 關鍵：清空 GPU 緩存
        logger.info(f"✅ 成功卸載模型 '{current_model_name}'。")

    # 步驟 2: 加載新模型
    try:
        logger.info(f"🔄 正在加載新模型 OpenAI Whisper '{model_name}' (設備: {device})...")
        whisper_models[model_name] = whisper.load_model(model_name, device=device)
        current_model_name = model_name  # 更新當前模型的名稱
        logger.info(f"✅ 成功加載模型 '{model_name}'。")
        return True
    except Exception as e:
        logger.error(f"❌ 加載模型 '{model_name}' 失敗: {e}", exc_info=True)
        # 如果加載失敗，將當前模型名稱清空，以表示服務處於降級狀態
        current_model_name = ""
        return False

def get_current_model():
    """獲取當前模型 (保持不變)"""
    global whisper_models, current_model_name
    model = whisper_models.get(current_model_name)
    if model is None and current_model_name:
         logger.warning(f"警告：請求獲取模型 '{current_model_name}'，但該模型未在內存中。")
    return model

def transcribe_audio(audio_data: bytes, transcription_language: str = "auto") -> Dict[str, Any]:
    """
    【性能優化】使用 OpenAI Whisper 進行語音轉文字。
    移除了不必要的磁碟寫入操作，直接在內存中處理音頻。
    """
    global current_model_name, device

    current_model = get_current_model()
    if current_model is None:
        raise HTTPException(status_code=503, detail="服務不可用：Whisper 模型當前未成功加載。")

    start_time = time.time()
    language_map = {"auto": None, "chinese": "zh", "english": "en", "vietnamese": "vi"}
    target_language = language_map.get(transcription_language)
    logger.info(f"🎯 開始轉錄，語言設置: {transcription_language} -> {target_language or '自動檢測'}")
    
    try:
        # 音頻預處理：從內存中的字節流讀取數據
        audio_io = io.BytesIO(audio_data)
        audio_array, sample_rate = sf.read(audio_io, dtype='float32')
        logger.info(f"🎧 音頻信息: 採樣率={sample_rate}, 形狀={audio_array.shape}, 時長={len(audio_array)/sample_rate:.2f}秒")
        
        # 確保單聲道
        if audio_array.ndim > 1:
            audio_array = np.mean(audio_array, axis=1)
        
        # 【優化】直接將 NumPy 數組傳遞給模型，避免磁碟 I/O
        # 舊方法: current_model.transcribe(temp_file.name, ...)
        # 新方法: current_model.transcribe(audio_array, ...)
        logger.info(f"🧠 使用模型 '{current_model_name}' 進行轉錄...")
        result = current_model.transcribe(
            audio_array,
            language=target_language,
            fp16=(device == "cuda"),
            temperature=0.0,
            beam_size=5,
            best_of=5,
            condition_on_previous_text=False
        )
        
        processing_time = time.time() - start_time
        
        # 處理 segments (保持不變)
        segments_list = [{"start": seg.get("start", 0), "end": seg.get("end", 0), "text": seg.get("text", "")} for seg in result.get("segments", [])]
        transcription_text = result["text"].strip()
        detected_language = result.get("language", "unknown")

        language_names = {"zh": "中文", "en": "英文", "vi": "越南文"}
        logger.info(f"✅ 轉錄完成，耗時: {processing_time:.2f}秒")
        logger.info(f"🗣️ 檢測語言: {detected_language}")
        logger.info(f"📝 轉錄結果預覽: {transcription_text[:100]}...")

        return {
            "transcription": transcription_text,
            "language": language_names.get(detected_language, detected_language),
            "detected_language": detected_language,
            "segments": segments_list,
            "processing_time": processing_time,
            "model_name": f"OpenAI Whisper {current_model_name}",
            "device_used": device,
            "transcription_language": transcription_language
        }
        
    except Exception as e:
        logger.error(f"❌ 轉錄過程中發生錯誤: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"轉錄失敗: {str(e)}")

@app.post("/api/translate", response_model=TranslationResponse)
async def translate_text(request: TranslationRequest):
    """使用 Ollama Gemma3 模型翻譯文本 (保持不變)"""
    # ... 此函數的內部實現與您提供的版本完全相同，此處省略以保持簡潔 ...
    start_time = time.time()
    try:
        logger.info(f"🌐 开始翻译: {request.text[:50]}... -> {request.target_language}")
        language_prompts = {
            'english': '''You are a professional translator. Translate the following text to natural, fluent English.\nRules:\n- Only return the translation, no explanations\n- Maintain the original meaning and tone\n- Use natural English expressions\n- If the text is already in English, return it as is\n\nText to translate:''',
            'traditional_chinese': '''你是專業翻譯員。將以下文字翻譯成自然流暢的繁體中文。\n規則：\n- 只返回翻譯結果，不要任何解釋\n- 保持原文的意思和語調\n- 使用自然的中文表達\n- 如果文字已經是繁體中文，直接返回原文\n\n要翻譯的文字：''',
            'vietnamese': '''Bạn là một dịch giả chuyên nghiệp. Hãy dịch văn bản sau sang tiếng Việt tự nhiên và trôi chảy.\nQuy tắc:\n- Chỉ trả về bản dịch, không giải thích\n- Giữ nguyên ý nghĩa và giọng điệu gốc\n- Sử dụng cách diễn đạt tiếng Việt tự nhiên\n- Nếu văn bản đã là tiếng Việt, trả về nguyên văn\n\nVăn bản cần dịch:'''
        }
        if request.target_language not in language_prompts:
            raise HTTPException(status_code=400, detail=f"不支持的翻译语言: {request.target_language}")
        prompt = f"{language_prompts[request.target_language]}\n\n{request.text}"
        ollama_host = os.getenv('OLLAMA_HOST', 'localhost')
        ollama_port = os.getenv('OLLAMA_PORT', '11434')
        ollama_url = f'http://{ollama_host}:{ollama_port}/api/generate'
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                ollama_url,
                json={'model': 'gemma3:latest','prompt': prompt,'stream': False,'options': {'temperature': 0.1,'top_p': 0.8,'top_k': 20,'repeat_penalty': 1.1,'num_predict': 200,'num_ctx': 2048}}
            )
            if response.status_code != 200:
                raise HTTPException(status_code=500, detail=f"Ollama API 错误: {response.status_code}")
            data = response.json()
            translated_text = data.get('response', '').strip()
            if not translated_text:
                raise HTTPException(status_code=500, detail="翻译结果为空")
        processing_time = time.time() - start_time
        logger.info(f"✅ 翻译完成，耗时: {processing_time:.2f}秒")
        return TranslationResponse(original_text=request.text,translated_text=translated_text,target_language=request.target_language,processing_time=processing_time)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"翻译错误: {e}")
        raise HTTPException(status_code=500, detail=f"翻译失败: {str(e)}")


@app.on_event("startup")
async def startup_event():
    """應用啟動時初始化模型 (保持不變)"""
    logger.info("🚀 啟動 OpenAI Whisper GPU 服務器...")
    logger.info("📋 配置信息:")
    logger.info("   - 框架: FastAPI")
    logger.info("   - 模型: OpenAI Whisper")
    logger.info("   - 設備: GPU (CUDA) 優先，CPU 回退")
    
    if not initialize_whisper_model():
        logger.error("❌ 關鍵錯誤：初始模型加載失敗！伺服器可能無法處理請求。")
        # 這裡可以根據需要決定是否讓服務器直接退出
        # raise Exception("模型初始化失敗")
    
    logger.info("✅ 伺服器啟動完成!")

@app.get("/", response_model=dict, include_in_schema=False)
async def root():
    """根路徑 (保持不變)"""
    return {
        "message": "OpenAI Whisper GPU API 服務 (優化版)",
        "version": app.version,
        "docs": "/docs",
        "health": "/health",
        "demo_page": "/static/meeting_transcription.html"
    }

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """健康檢查端點 (保持不變)"""
    gpu_info = None
    if torch.cuda.is_available():
        gpu_info = f"{torch.cuda.get_device_name(0)} ({torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB)"

    current_model_instance = get_current_model()
    model_name_display = f"OpenAI Whisper {current_model_name}" if current_model_instance else "未加載或加載失敗"

    return HealthResponse(
        status="healthy" if current_model_instance else "degraded",
        model_loaded=current_model_instance is not None,
        timestamp=time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime()),
        device=device,
        model_name=model_name_display,
        gpu_info=gpu_info
    )

@app.get("/models", response_model=dict)
async def get_models():
    """
    【優化】獲取可用模型列表。
    補全了模型列表，並簡化了 `loaded_models` 的邏輯。
    """
    # 【優化】補全可用模型列表以包含所有官方版本
    available_models = ["tiny", "base", "small", "medium", "large", "large-v2", "large-v3"]
    # 【優化】由於我們現在只在內存中保留一個模型，所以 "loaded_models" 總是只包含當前模型（如果成功加載的話）
    loaded_models = [current_model_name] if current_model_name in whisper_models else []

    return {
        "available_models": available_models,
        "loaded_models": loaded_models,
        "current_model": current_model_name or "None",
        "device": device
    }

@app.post("/models/{model_name}", response_model=ModelSwitchResponse)
async def switch_model(model_name: str):
    """
    【優化】切換模型。
    此端點現在會觸發包含卸載和加載邏輯的 `load_model` 函數。
    """
    # 【優化】使用更新後的完整模型列表進行驗證
    available_models = ["tiny", "base", "small", "medium", "large", "large-v2", "large-v3"]

    if model_name not in available_models:
        raise HTTPException(
            status_code=400,
            detail=f"不支持的模型: '{model_name}'. 可用模型: {available_models}"
        )

    try:
        success = load_model(model_name)
        if success:
            return ModelSwitchResponse(
                success=True,
                message=f"成功切換到模型: {model_name}",
                current_model=current_model_name,
                available_models=available_models
            )
        else:
            # 【優化】如果切換失敗，返回更明確的錯誤信息
            raise HTTPException(
                status_code=500, 
                detail=f"切換到模型 '{model_name}' 失敗。請檢查伺服器日誌。"
            )
    except Exception as e:
        logger.error(f"模型切換時發生未知錯誤: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"模型切換失敗: {str(e)}")

@app.post("/api/transcribe", response_model=TranscriptionResponse)
async def transcribe_file(
    audio_file: UploadFile = File(...),
    transcription_language: str = "auto"
):
    """文件轉錄端點 (保持不變)"""
    try:
        valid_languages = ["auto", "chinese", "english", "vietnamese"]
        if transcription_language not in valid_languages:
            raise HTTPException(status_code=400, detail=f"不支持的轉錄語言: {transcription_language}. 支持的語言: {valid_languages}")

        audio_data = await audio_file.read()
        if len(audio_data) == 0:
            raise HTTPException(status_code=400, detail="上傳的文件為空")

        logger.info(f"收到音頻文件: {audio_file.filename}, 大小: {len(audio_data)} bytes, 轉錄語言: {transcription_language}")
        
        # 【優化】建議使用 asyncio.to_thread 在背景線程中運行 CPU 密集型或阻塞操作，以避免阻塞事件循環
        result = await asyncio.to_thread(transcribe_audio, audio_data, transcription_language)

        return TranscriptionResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件轉錄請求處理錯誤: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"轉錄失敗: {str(e)}")

# --- 異常處理與主程序 ---

@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """HTTP 異常處理器 (保持不變)"""
    return JSONResponse(status_code=exc.status_code, content={"error": exc.detail})

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """通用異常處理器 (保持不變)"""
    logger.error(f"發生未處理的異常: {exc}", exc_info=True)
    return JSONResponse(status_code=500, content={"error": "內部服務器錯誤", "detail": str(exc)})

def main():
    """啟動服務器 (保持不變)"""
    print("🚀 正在啟動伺服器...")
    try:
        uvicorn.run(
            "__main__:app",  # 【優化】建議使用字符串引用 `module_name:app_instance`，這對熱重載更友好
            host="0.0.0.0",
            port=9004,
            reload=False,  # 生產環境下應設為 False
            log_level="info"
        )
    except Exception as e:
        print(f"❌ 伺服器啟動失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()