#!/usr/bin/env python3
"""
FastAPI 语音转文字服务器
"""

import logging
import io
import numpy as np
import soundfile as sf
from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from faster_whisper import WhisperModel
import torch
from typing import Optional, Dict, Any
from pydantic import BaseModel
import uvicorn

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 创建 FastAPI 应用
app = FastAPI(
    title="语音转文字 API",
    description="基于 Faster-Whisper 的语音转文字服务",
    version="1.0.0"
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局变量
whisper_model: Optional[WhisperModel] = None

# 响应模型
class HealthResponse(BaseModel):
    status: str
    model_loaded: bool
    timestamp: str
    device: str
    model_name: str

class TranscriptionResponse(BaseModel):
    transcription: str
    language: str
    language_probability: float
    segments: list
    processing_time: float

class ErrorResponse(BaseModel):
    error: str
    detail: Optional[str] = None

def initialize_whisper_model() -> bool:
    """初始化 Whisper 模型"""
    global whisper_model
    
    device = "cuda"
    compute_type = "float16"
    model_name = "large-v3"
    
    logger.info("🔍 检查 GPU 可用性...")
    
    try:
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            logger.info(f"✅ 检测到 CUDA，GPU 数量: {gpu_count}")
            logger.info(f"🎮 GPU 名称: {gpu_name}")
            logger.info(f"💾 GPU 内存: {gpu_memory:.1f} GB")
        else:
            logger.warning("⚠️ CUDA 不可用，回退到 CPU")
            device = "cpu"
            compute_type = "int8"
    except ImportError:
        logger.warning("⚠️ PyTorch 未安装，回退到 CPU")
        device = "cpu"
        compute_type = "int8"
    
    try:
        logger.info(f"🔄 正在加载 Whisper 模型: {model_name} (设备: {device}, 计算类型: {compute_type})")
        whisper_model = WhisperModel(model_name, device=device, compute_type=compute_type)
        logger.info(f"✅ Whisper 模型加载成功 (设备: {device})")
        return True
    except Exception as e:
        logger.error(f"❌ 加载 {model_name} 失败: {e}")
        try:
            logger.info("🔄 尝试 base 模型...")
            whisper_model = WhisperModel("base", device=device, compute_type=compute_type)
            logger.info(f"✅ Whisper base 模型加载成功")
            return True
        except Exception as e2:
            logger.error(f"❌ 所有模型都失败: {e2}")
            return False

def transcribe_audio(audio_data: bytes) -> Dict[str, Any]:
    """使用 Faster Whisper 进行语音转文字"""
    global whisper_model
    
    if whisper_model is None:
        raise HTTPException(status_code=500, detail="Whisper 模型未初始化")
    
    import time
    start_time = time.time()
    
    try:
        # 音频预处理
        audio_io = io.BytesIO(audio_data)
        audio_array, sample_rate = sf.read(audio_io, dtype='float32')
        
        logger.info(f"音频信息: 采样率={sample_rate}, 形状={audio_array.shape}, 时长={len(audio_array)/sample_rate:.2f}秒")
        
        # 确保单声道
        if len(audio_array.shape) > 1:
            audio_array = np.mean(audio_array, axis=1)
        
        # 数据预处理
        audio_array = np.array(audio_array, dtype=np.float32)
        max_val = np.max(np.abs(audio_array))
        if max_val > 0:
            audio_array = audio_array / max_val
        audio_array = np.nan_to_num(audio_array, nan=0.0, posinf=1.0, neginf=-1.0)
        
        # 进行转录
        logger.info("🎯 开始转录...")
        
        segments, info = whisper_model.transcribe(
            audio_array,
            language="zh",  # 强制中文
            beam_size=5,
            best_of=5,
            temperature=0.0,
            condition_on_previous_text=False,
            vad_filter=False
        )
        
        logger.info(f"检测语言: {info.language} (概率: {info.language_probability:.3f})")
        
        # 收集转录结果
        transcription_text = ""
        segments_list = []
        
        for segment in segments:
            transcription_text += segment.text
            segments_list.append({
                "start": segment.start,
                "end": segment.end,
                "text": segment.text
            })
        
        processing_time = time.time() - start_time
        
        logger.info(f"转录完成，耗时: {processing_time:.2f}秒")
        logger.info(f"转录结果: {transcription_text[:100]}...")
        
        return {
            "transcription": transcription_text.strip(),
            "language": info.language,
            "language_probability": float(info.language_probability),
            "segments": segments_list,
            "processing_time": processing_time
        }
        
    except Exception as e:
        logger.error(f"转录错误: {e}")
        raise HTTPException(status_code=500, detail=f"转录失败: {str(e)}")

@app.on_event("startup")
async def startup_event():
    """应用启动时初始化模型"""
    logger.info("🚀 启动 FastAPI 语音转文字服务器...")
    logger.info("📋 配置信息:")
    logger.info("   - 框架: FastAPI")
    logger.info("   - 模型: Whisper large-v3")
    logger.info("   - 设备: GPU (CUDA) 优先")
    
    if not initialize_whisper_model():
        logger.error("❌ 模型初始化失败!")
        raise Exception("模型初始化失败")
    
    logger.info("✅ 服务器启动完成!")

@app.get("/", response_model=dict)
async def root():
    """根路径"""
    return {
        "message": "语音转文字 API 服务",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health"
    }

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查端点"""
    device_info = "unknown"
    model_name = "unknown"
    
    if whisper_model is not None:
        try:
            device_info = str(whisper_model.model.device)
            model_name = "large-v3" if hasattr(whisper_model, 'model') else "base"
        except:
            device_info = "cuda" if torch.cuda.is_available() else "cpu"
    
    return HealthResponse(
        status="healthy",
        model_loaded=whisper_model is not None,
        timestamp="2025-07-18T00:00:00Z",
        device=device_info,
        model_name=model_name
    )

@app.post("/api/transcribe", response_model=TranscriptionResponse)
async def transcribe_file(audio_file: UploadFile = File(...)):
    """文件转录端点"""
    try:
        # 验证文件类型
        if not audio_file.content_type or not audio_file.content_type.startswith('audio/'):
            # 也允许一些常见的音频文件扩展名
            allowed_extensions = ['.wav', '.mp3', '.m4a', '.flac', '.ogg', '.webm']
            if not any(audio_file.filename.lower().endswith(ext) for ext in allowed_extensions):
                raise HTTPException(
                    status_code=400, 
                    detail="不支持的文件类型。请上传音频文件 (wav, mp3, m4a, flac, ogg, webm)"
                )
        
        # 读取音频数据
        audio_data = await audio_file.read()
        
        if len(audio_data) == 0:
            raise HTTPException(status_code=400, detail="上传的文件为空")
        
        logger.info(f"收到音频文件: {audio_file.filename}, 大小: {len(audio_data)} bytes")
        
        # 进行转录
        result = transcribe_audio(audio_data)
        
        return TranscriptionResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件转录错误: {e}")
        raise HTTPException(status_code=500, detail=f"转录失败: {str(e)}")

@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """HTTP 异常处理器"""
    return JSONResponse(
        status_code=exc.status_code,
        content={"error": exc.detail}
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """通用异常处理器"""
    logger.error(f"未处理的异常: {exc}")
    return JSONResponse(
        status_code=500,
        content={"error": "内部服务器错误", "detail": str(exc)}
    )

def main():
    """启动服务器"""
    uvicorn.run(
        "fastapi_server:app",
        host="0.0.0.0",
        port=9004,
        reload=False,
        log_level="info"
    )

if __name__ == "__main__":
    main()
