#!/usr/bin/env python3
"""
生成测试音频的脚本
"""

import numpy as np
import soundfile as sf
from gtts import gTTS
import io
import tempfile
import os

def create_test_audio():
    """创建测试用的中文音频"""
    
    # 测试文本
    test_texts = [
        "你好，这是一个语音转文字的测试。",
        "今天天气很好，我们来测试一下中文语音识别的效果。",
        "人工智能技术正在快速发展，语音识别是其中重要的一部分。",
        "希望这个测试能够成功，谢谢大家。"
    ]
    
    print("🎤 生成测试音频文件...")
    
    for i, text in enumerate(test_texts, 1):
        try:
            # 使用 gTTS 生成语音
            tts = gTTS(text=text, lang='zh', slow=False)
            
            # 保存到临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix='.mp3') as tmp_file:
                tts.save(tmp_file.name)
                
                # 转换为 WAV 格式
                output_file = f"test_chinese_{i}.wav"
                os.system(f"ffmpeg -i {tmp_file.name} -ar 16000 -ac 1 {output_file} -y")
                
                print(f"✅ 生成: {output_file}")
                print(f"   文本: {text}")
                
                # 清理临时文件
                os.unlink(tmp_file.name)
                
        except Exception as e:
            print(f"❌ 生成第 {i} 个文件失败: {e}")

def analyze_audio_file(file_path):
    """分析音频文件的特征"""
    try:
        audio_data, sample_rate = sf.read(file_path)
        
        print(f"\n📊 音频分析: {file_path}")
        print(f"   采样率: {sample_rate} Hz")
        print(f"   时长: {len(audio_data) / sample_rate:.2f} 秒")
        print(f"   声道: {audio_data.shape}")
        print(f"   数据类型: {audio_data.dtype}")
        
        # 计算音频统计信息
        if len(audio_data.shape) > 1:
            audio_mono = np.mean(audio_data, axis=1)
        else:
            audio_mono = audio_data
            
        print(f"   最大值: {np.max(np.abs(audio_mono)):.4f}")
        print(f"   平均值: {np.mean(np.abs(audio_mono)):.4f}")
        print(f"   标准差: {np.std(audio_mono):.4f}")
        
        # 检测静音段
        silence_threshold = 0.01
        silence_ratio = np.sum(np.abs(audio_mono) < silence_threshold) / len(audio_mono)
        print(f"   静音比例: {silence_ratio:.2%}")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

if __name__ == "__main__":
    print("🔧 音频测试工具")
    print("=" * 50)
    
    # 分析现有的问题音频
    problem_files = [
        "/home/<USER>/下載/ponsunlun_6.wav",
        "/home/<USER>/下載/ponsunlun_7.wav", 
        "/home/<USER>/下載/ponsunlun_8.wav"
    ]
    
    print("\n📋 分析问题音频文件:")
    for file_path in problem_files:
        if os.path.exists(file_path):
            analyze_audio_file(file_path)
        else:
            print(f"❌ 文件不存在: {file_path}")
    
    print("\n" + "=" * 50)
    
    # 生成测试音频
    try:
        create_test_audio()
        print("\n✅ 测试音频生成完成！")
        print("请使用生成的 test_chinese_*.wav 文件进行测试")
    except Exception as e:
        print(f"\n❌ 生成测试音频失败: {e}")
        print("请手动录制一段清晰的中文语音进行测试")
