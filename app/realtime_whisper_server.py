#!/usr/bin/env python3
"""
实时系统音频转录 FastAPI 服务器
支持系统音频捕获和实时转录
"""

import logging
import io
import numpy as np
import soundfile as sf
from fastapi import FastAPI, File, UploadFile, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, HTMLResponse
import whisper  # OpenAI Whisper
import torch
from typing import Optional, Dict, Any, List
from pydantic import BaseModel
import uvicorn
import time
import tempfile
import os
import asyncio
import json
import base64
import pyaudio
import threading
import queue

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 创建 FastAPI 应用
app = FastAPI(
    title="实时系统音频转录 API",
    description="支持系统音频捕获和实时转录的 OpenAI Whisper 服务",
    version="4.0.0"
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局变量
whisper_models: Dict[str, Any] = {}
current_model_name: str = "base"
device: str = "cpu"
audio_queue = queue.Queue()
is_recording = False

# 响应模型
class HealthResponse(BaseModel):
    status: str
    model_loaded: bool
    timestamp: str
    device: str
    model_name: str
    gpu_info: Optional[str] = None
    audio_devices: List[dict] = []

class TranscriptionResponse(BaseModel):
    transcription: str
    language: str
    segments: List[dict]
    processing_time: float
    model_name: str
    device_used: str

class ModelSwitchResponse(BaseModel):
    success: bool
    message: str
    current_model: str
    available_models: List[str]

class RealtimeTranscriptionResponse(BaseModel):
    transcription: str
    timestamp: float
    confidence: float
    is_final: bool

def get_audio_devices():
    """获取可用的音频设备"""
    try:
        p = pyaudio.PyAudio()
        devices = []
        
        for i in range(p.get_device_count()):
            info = p.get_device_info_by_index(i)
            devices.append({
                "index": i,
                "name": info["name"],
                "channels": info["maxInputChannels"],
                "sample_rate": int(info["defaultSampleRate"]),
                "is_input": info["maxInputChannels"] > 0
            })
        
        p.terminate()
        return devices
    except Exception as e:
        logger.error(f"获取音频设备失败: {e}")
        return []

def initialize_whisper_model() -> bool:
    """初始化 OpenAI Whisper 模型"""
    global whisper_models, current_model_name, device
    
    logger.info("🔍 检查 GPU 可用性...")
    
    if torch.cuda.is_available():
        device = "cuda"
        gpu_count = torch.cuda.device_count()
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        logger.info(f"✅ 检测到 CUDA，GPU 数量: {gpu_count}")
        logger.info(f"🎮 GPU 名称: {gpu_name}")
        logger.info(f"💾 GPU 内存: {gpu_memory:.1f} GB")
    else:
        device = "cpu"
        logger.warning("⚠️ CUDA 不可用，使用 CPU")
    
    try:
        logger.info(f"🔄 正在预加载 OpenAI Whisper base (设备: {device})...")
        whisper_models["base"] = whisper.load_model("base", device=device)
        current_model_name = "base"
        logger.info(f"✅ OpenAI Whisper base 预加载成功 (设备: {device})")
        return True
    except Exception as e:
        logger.error(f"❌ OpenAI Whisper base 预加载失败: {e}")
        return False

def load_model(model_name: str) -> bool:
    """动态加载指定的模型"""
    global whisper_models, current_model_name, device
    
    if model_name in whisper_models:
        current_model_name = model_name
        logger.info(f"✅ 切换到已加载的模型: {model_name}")
        return True
    
    try:
        logger.info(f"🔄 正在加载 OpenAI Whisper {model_name} (设备: {device})...")
        whisper_models[model_name] = whisper.load_model(model_name, device=device)
        current_model_name = model_name
        logger.info(f"✅ OpenAI Whisper {model_name} 加载成功 (设备: {device})")
        return True
    except Exception as e:
        logger.error(f"❌ OpenAI Whisper {model_name} 加载失败: {e}")
        return False

def get_current_model():
    """获取当前模型"""
    global whisper_models, current_model_name
    return whisper_models.get(current_model_name)

def transcribe_audio_chunk(audio_data: np.ndarray, sample_rate: int = 16000) -> Dict[str, Any]:
    """转录音频片段"""
    current_model = get_current_model()
    if current_model is None:
        return {"transcription": "", "language": "zh", "confidence": 0.0}
    
    try:
        # 创建临时文件
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
            sf.write(temp_file.name, audio_data, sample_rate)
            
            # 转录
            result = current_model.transcribe(
                temp_file.name,
                language="zh",
                temperature=0.0,
                condition_on_previous_text=False,
                fp16=device == "cuda"
            )
            
            os.unlink(temp_file.name)
        
        return {
            "transcription": result["text"].strip(),
            "language": result.get("language", "zh"),
            "confidence": 1.0  # OpenAI Whisper 不提供置信度
        }
        
    except Exception as e:
        logger.error(f"转录错误: {e}")
        return {"transcription": "", "language": "zh", "confidence": 0.0}

class AudioCapture:
    """音频捕获类"""
    
    def __init__(self, device_index=None, sample_rate=16000, chunk_size=1024):
        self.device_index = device_index
        self.sample_rate = sample_rate
        self.chunk_size = chunk_size
        self.audio = None
        self.stream = None
        self.is_recording = False
        
    def start_capture(self):
        """开始音频捕获"""
        try:
            self.audio = pyaudio.PyAudio()
            
            # 如果没有指定设备，尝试找到系统音频设备
            if self.device_index is None:
                self.device_index = self._find_system_audio_device()
            
            self.stream = self.audio.open(
                format=pyaudio.paFloat32,
                channels=1,
                rate=self.sample_rate,
                input=True,
                input_device_index=self.device_index,
                frames_per_buffer=self.chunk_size
            )
            
            self.is_recording = True
            logger.info(f"开始音频捕获，设备: {self.device_index}")
            return True
            
        except Exception as e:
            logger.error(f"音频捕获启动失败: {e}")
            return False
    
    def _find_system_audio_device(self):
        """查找系统音频设备"""
        devices = get_audio_devices()
        
        # 优先查找包含 "monitor" 或 "loopback" 的设备
        for device in devices:
            name_lower = device["name"].lower()
            if ("monitor" in name_lower or "loopback" in name_lower) and device["is_input"]:
                logger.info(f"找到系统音频设备: {device['name']}")
                return device["index"]
        
        # 如果没找到，使用默认输入设备
        for device in devices:
            if device["is_input"]:
                logger.info(f"使用默认输入设备: {device['name']}")
                return device["index"]
        
        return None
    
    def read_audio(self):
        """读取音频数据"""
        if not self.is_recording or not self.stream:
            return None
            
        try:
            data = self.stream.read(self.chunk_size, exception_on_overflow=False)
            audio_array = np.frombuffer(data, dtype=np.float32)
            return audio_array
        except Exception as e:
            logger.error(f"读取音频数据失败: {e}")
            return None
    
    def stop_capture(self):
        """停止音频捕获"""
        self.is_recording = False
        
        if self.stream:
            self.stream.stop_stream()
            self.stream.close()
            
        if self.audio:
            self.audio.terminate()
            
        logger.info("音频捕获已停止")

# 全局音频捕获实例
audio_capture = AudioCapture()

@app.on_event("startup")
async def startup_event():
    """应用启动时初始化模型"""
    logger.info("🚀 启动实时系统音频转录服务器...")
    logger.info("📋 配置信息:")
    logger.info("   - 框架: FastAPI")
    logger.info("   - 模型: OpenAI Whisper")
    logger.info("   - 功能: 实时系统音频转录")
    
    if not initialize_whisper_model():
        logger.error("❌ 模型初始化失败!")
        raise Exception("模型初始化失败")
    
    logger.info("✅ 服务器启动完成!")

@app.get("/", response_model=dict)
async def root():
    """根路径"""
    return {
        "message": "实时系统音频转录 API 服务",
        "version": "4.0.0",
        "docs": "/docs",
        "health": "/health",
        "features": ["文件转录", "实时转录", "系统音频捕获", "模型切换"]
    }

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查端点"""
    gpu_info = None
    if torch.cuda.is_available():
        gpu_info = f"{torch.cuda.get_device_name(0)} ({torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB)"
    
    current_model = get_current_model()
    model_name = f"OpenAI Whisper {current_model_name}" if current_model else "未加载"
    
    audio_devices = get_audio_devices()
    
    return HealthResponse(
        status="healthy",
        model_loaded=current_model is not None,
        timestamp="2025-07-18T00:00:00Z",
        device=device,
        model_name=model_name,
        gpu_info=gpu_info,
        audio_devices=audio_devices
    )

@app.get("/models", response_model=dict)
async def get_models():
    """获取可用模型列表"""
    available_models = ["tiny", "base", "small", "medium", "large"]
    loaded_models = list(whisper_models.keys())
    
    return {
        "available_models": available_models,
        "loaded_models": loaded_models,
        "current_model": current_model_name,
        "device": device
    }

@app.post("/models/{model_name}", response_model=ModelSwitchResponse)
async def switch_model(model_name: str):
    """切换模型"""
    available_models = ["tiny", "base", "small", "medium", "large"]
    
    if model_name not in available_models:
        raise HTTPException(
            status_code=400, 
            detail=f"不支持的模型: {model_name}. 可用模型: {available_models}"
        )
    
    try:
        success = load_model(model_name)
        if success:
            return ModelSwitchResponse(
                success=True,
                message=f"成功切换到模型: {model_name}",
                current_model=current_model_name,
                available_models=available_models
            )
        else:
            return ModelSwitchResponse(
                success=False,
                message=f"切换到模型 {model_name} 失败",
                current_model=current_model_name,
                available_models=available_models
            )
    except Exception as e:
        logger.error(f"模型切换错误: {e}")
        raise HTTPException(status_code=500, detail=f"模型切换失败: {str(e)}")

@app.post("/api/transcribe", response_model=TranscriptionResponse)
async def transcribe_file(audio_file: UploadFile = File(...)):
    """文件转录端点"""
    try:
        if not audio_file.content_type or not audio_file.content_type.startswith('audio/'):
            allowed_extensions = ['.wav', '.mp3', '.m4a', '.flac', '.ogg', '.webm']
            if not any(audio_file.filename.lower().endswith(ext) for ext in allowed_extensions):
                raise HTTPException(
                    status_code=400, 
                    detail="不支持的文件类型。请上传音频文件 (wav, mp3, m4a, flac, ogg, webm)"
                )
        
        audio_data = await audio_file.read()
        if len(audio_data) == 0:
            raise HTTPException(status_code=400, detail="上传的文件为空")
        
        logger.info(f"收到音频文件: {audio_file.filename}, 大小: {len(audio_data)} bytes")
        
        # 转录音频
        current_model = get_current_model()
        if current_model is None:
            raise HTTPException(status_code=500, detail="Whisper 模型未初始化")
        
        start_time = time.time()
        
        audio_io = io.BytesIO(audio_data)
        audio_array, sample_rate = sf.read(audio_io, dtype='float32')
        
        if len(audio_array.shape) > 1:
            audio_array = np.mean(audio_array, axis=1)
        
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
            sf.write(temp_file.name, audio_array, sample_rate)
            
            result = current_model.transcribe(
                temp_file.name,
                language="zh",
                temperature=0.0,
                beam_size=5,
                best_of=5,
                condition_on_previous_text=False,
                fp16=device == "cuda"
            )
            
            os.unlink(temp_file.name)
        
        processing_time = time.time() - start_time
        
        segments_list = []
        if "segments" in result:
            for segment in result["segments"]:
                segments_list.append({
                    "start": segment.get("start", 0),
                    "end": segment.get("end", 0),
                    "text": segment.get("text", "")
                })
        
        return TranscriptionResponse(
            transcription=result["text"].strip(),
            language=result.get("language", "zh"),
            segments=segments_list,
            processing_time=processing_time,
            model_name=f"OpenAI Whisper {current_model_name}",
            device_used=device
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件转录错误: {e}")
        raise HTTPException(status_code=500, detail=f"转录失败: {str(e)}")

@app.websocket("/ws/realtime")
async def websocket_realtime_transcription(websocket: WebSocket):
    """WebSocket 实时转录端点"""
    await websocket.accept()
    logger.info("WebSocket 连接已建立")

    try:
        # 发送初始状态
        await websocket.send_json({
            "type": "status",
            "message": "连接成功",
            "current_model": current_model_name,
            "device": device
        })

        audio_buffer = np.array([], dtype=np.float32)
        buffer_duration = 3.0  # 3秒缓冲
        sample_rate = 16000
        buffer_size = int(buffer_duration * sample_rate)

        while True:
            try:
                # 接收音频数据
                data = await websocket.receive_text()
                message = json.loads(data)

                if message["type"] == "audio":
                    # 解码音频数据
                    audio_data = base64.b64decode(message["data"])
                    audio_array = np.frombuffer(audio_data, dtype=np.float32)

                    # 添加到缓冲区
                    audio_buffer = np.append(audio_buffer, audio_array)

                    # 如果缓冲区足够大，进行转录
                    if len(audio_buffer) >= buffer_size:
                        # 转录音频片段
                        result = transcribe_audio_chunk(audio_buffer, sample_rate)

                        if result["transcription"].strip():
                            # 发送转录结果
                            await websocket.send_json({
                                "type": "transcription",
                                "transcription": result["transcription"],
                                "language": result["language"],
                                "confidence": result["confidence"],
                                "timestamp": time.time(),
                                "model": current_model_name
                            })

                        # 保留一部分重叠以确保连续性
                        overlap_size = buffer_size // 4
                        audio_buffer = audio_buffer[-overlap_size:]

                elif message["type"] == "start_system_audio":
                    # 开始系统音频捕获
                    device_index = message.get("device_index")
                    if audio_capture.start_capture():
                        await websocket.send_json({
                            "type": "status",
                            "message": "系统音频捕获已开始",
                            "device_index": device_index
                        })

                        # 启动音频捕获线程
                        asyncio.create_task(system_audio_capture_task(websocket))
                    else:
                        await websocket.send_json({
                            "type": "error",
                            "message": "系统音频捕获启动失败"
                        })

                elif message["type"] == "stop_system_audio":
                    # 停止系统音频捕获
                    audio_capture.stop_capture()
                    await websocket.send_json({
                        "type": "status",
                        "message": "系统音频捕获已停止"
                    })

                elif message["type"] == "switch_model":
                    # 切换模型
                    model_name = message.get("model_name")
                    if load_model(model_name):
                        await websocket.send_json({
                            "type": "status",
                            "message": f"已切换到模型: {model_name}",
                            "current_model": current_model_name
                        })
                    else:
                        await websocket.send_json({
                            "type": "error",
                            "message": f"模型切换失败: {model_name}"
                        })

            except WebSocketDisconnect:
                break
            except Exception as e:
                logger.error(f"WebSocket 处理错误: {e}")
                await websocket.send_json({
                    "type": "error",
                    "message": f"处理错误: {str(e)}"
                })

    except WebSocketDisconnect:
        logger.info("WebSocket 连接已断开")
    finally:
        # 清理资源
        audio_capture.stop_capture()

async def system_audio_capture_task(websocket: WebSocket):
    """系统音频捕获任务"""
    logger.info("开始系统音频捕获任务")

    audio_buffer = np.array([], dtype=np.float32)
    buffer_duration = 2.0  # 2秒缓冲
    sample_rate = 16000
    buffer_size = int(buffer_duration * sample_rate)

    try:
        while audio_capture.is_recording:
            # 读取音频数据
            audio_chunk = audio_capture.read_audio()

            if audio_chunk is not None and len(audio_chunk) > 0:
                # 添加到缓冲区
                audio_buffer = np.append(audio_buffer, audio_chunk)

                # 如果缓冲区足够大，进行转录
                if len(audio_buffer) >= buffer_size:
                    # 转录音频片段
                    result = transcribe_audio_chunk(audio_buffer, sample_rate)

                    if result["transcription"].strip():
                        # 发送转录结果
                        await websocket.send_json({
                            "type": "system_transcription",
                            "transcription": result["transcription"],
                            "language": result["language"],
                            "confidence": result["confidence"],
                            "timestamp": time.time(),
                            "model": current_model_name,
                            "source": "system_audio"
                        })

                    # 保留一部分重叠
                    overlap_size = buffer_size // 4
                    audio_buffer = audio_buffer[-overlap_size:]

            # 短暂休眠避免过度占用 CPU
            await asyncio.sleep(0.1)

    except Exception as e:
        logger.error(f"系统音频捕获任务错误: {e}")
        await websocket.send_json({
            "type": "error",
            "message": f"系统音频捕获错误: {str(e)}"
        })

def main():
    """启动服务器"""
    uvicorn.run(
        "realtime_whisper_server:app",
        host="0.0.0.0",
        port=9004,
        reload=False,
        log_level="info"
    )

if __name__ == "__main__":
    main()
