<!DOCTYPE html>
<!-- saved from url=(0037)http://localhost:9004/static/234.html -->
<html lang="zh-TW"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  
  <title>自適應智能轉錄系統 (自動優化)</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      color: white;
    }
    
    .container {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 20px;
      padding: 30px;
      color: #333;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }
    
    h1 {
      text-align: center;
      color: #2c3e50;
      margin-bottom: 30px;
    }
    
    .section {
      background: #f8f9fa;
      border-radius: 15px;
      padding: 25px;
      margin: 20px 0;
      border: 2px solid #e9ecef;
    }
    
    button {
      margin: 5px 10px 5px 0;
      padding: 12px 24px;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
      border: none;
      border-radius: 8px;
      transition: all 0.3s ease;
    }
    
    button.primary {
      background: linear-gradient(45deg, #28a745, #20c997);
      color: white;
    }
    
    button.danger {
      background: linear-gradient(45deg, #dc3545, #fd7e14);
      color: white;
    }
    
    button.secondary {
      background: linear-gradient(45deg, #6c757d, #495057);
      color: white;
    }
    
    button:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    
    button:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
      opacity: 0.6;
    }
    
    button.recording {
      animation: pulse 1.5s infinite;
    }
    
    @keyframes pulse {
      0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
      70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
      100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
    }
    
    select {
      margin: 5px 10px 5px 0;
      padding: 8px 12px;
      font-size: 16px;
      border: 2px solid #dee2e6;
      border-radius: 8px;
      background: white;
    }
    
    .status {
      padding: 15px;
      margin: 10px 0;
      border-radius: 8px;
      font-weight: bold;
    }
    
    .status.success {
      background: #d4edda;
      color: #155724;
    }
    
    .status.error {
      background: #f8d7da;
      color: #721c24;
    }
    
    .status.info {
      background: #d1ecf1;
      color: #0c5460;
    }
    
    .status.warning {
      background: #fff3cd;
      color: #856404;
    }
    
    .transcription-display {
      background: white;
      border: 1px solid #dee2e6;
      border-radius: 10px;
      padding: 20px;
      margin: 20px 0;
      max-height: 500px;
      overflow-y: auto;
    }
    
    .transcription-item {
      background: #f8f9fa;
      margin: 10px 0;
      padding: 15px;
      border-radius: 8px;
      border-left: 4px solid #28a745;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      transition: all 0.3s ease;
    }
    
    .transcription-item.processing {
      border-left-color: #ffc107;
      background: #fff8e1;
    }
    
    .transcription-item.filtered {
      border-left-color: #dc3545;
      background: #ffe6e6;
      opacity: 0.7;
    }
    
    .timestamp {
      font-size: 12px;
      color: #6c757d;
      margin-bottom: 5px;
    }
    
    .text-content {
      font-size: 16px;
      line-height: 1.5;
      margin-top: 8px;
    }
    
    .success-box {
      background: #d4edda;
      border: 1px solid #c3e6cb;
      padding: 15px;
      border-radius: 10px;
      margin: 15px 0;
      color: #155724;
    }
    
    .adaptive-panel {
      background: #e3f2fd;
      border: 2px solid #2196f3;
      border-radius: 15px;
      padding: 20px;
      margin: 20px 0;
    }
    
    .adaptive-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 10px 0;
      padding: 10px;
      background: white;
      border-radius: 8px;
    }
    
    .audio-visualizer {
      width: 100%;
      height: 80px;
      background: #2c3e50;
      border-radius: 8px;
      margin: 10px 0;
      position: relative;
      overflow: hidden;
    }
    
    .volume-bar {
      height: 100%;
      background: linear-gradient(90deg, #28a745, #ffc107, #dc3545);
      width: 0%;
      transition: width 0.1s ease;
      border-radius: 8px;
    }
    
    .adaptive-info {
      position: absolute;
      top: 10px;
      left: 10px;
      color: white;
      font-size: 12px;
      font-weight: bold;
    }
    
    .silence-info {
      position: absolute;
      bottom: 10px;
      left: 10px;
      color: white;
      font-size: 12px;
    }
    
    .quality-indicator {
      position: absolute;
      top: 10px;
      right: 10px;
      color: white;
      font-size: 12px;
      font-weight: bold;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🤖 自適應智能轉錄系統</h1>
    
    <!-- 說明 -->
    <div class="success-box">
      <h3>🧠 全自動智能優化</h3>
      <p>此系統會自動學習和優化，無需手動調整參數：</p>
      <ul>
        <li><strong>自動環境檢測</strong>：分析音頻環境自動調整參數</li>
        <li><strong>智能內容過濾</strong>：自動過濾無意義的轉錄結果</li>
        <li><strong>動態閾值調整</strong>：根據音頻特性實時優化</li>
        <li><strong>質量評估</strong>：自動評估轉錄質量並改進</li>
      </ul>
    </div>
    
    <!-- 自適應狀態面板 -->
    <div class="adaptive-panel">
      <h3>🔧 自適應狀態監控</h3>
      <div class="adaptive-item">
        <span>環境類型：</span>
        <span id="environmentType">安靜環境</span>
      </div>
      <div class="adaptive-item">
        <span>動態閾值：</span>
        <span id="dynamicThreshold">0.0050</span>
      </div>
      <div class="adaptive-item">
        <span>分段策略：</span>
        <span id="segmentStrategy">3-30秒 / 800ms</span>
      </div>
      <div class="adaptive-item">
        <span>過濾效率：</span>
        <span id="filterEfficiency">0.0% (0/1)</span>
      </div>
    </div>
    
    <!-- 音頻監控 -->
    <div class="section">
      <h3>🔊 智能音頻監控</h3>
      <div class="audio-visualizer">
        <div class="volume-bar" id="volumeBar" style="width: 8.44018%;"></div>
        <div class="adaptive-info" id="adaptiveInfo">自適應：安靜環境</div>
        <div class="silence-info" id="silenceInfo">閾值：0.0050 | 音量：0.0084</div>
        <div class="quality-indicator" id="qualityIndicator">狀態：良好</div>
      </div>
    </div>
    
    <!-- 控制 -->
    <div class="section">
      <h3>🎯 自適應轉錄控制</h3>
      
      <label for="audioQuality">音頻品質：</label>
      <select id="audioQuality">
        <option value="192000,44100">192 kbps, 44.1 kHz (推薦)</option>
        <option value="128000,44100">128 kbps, 44.1 kHz</option>
        <option value="64000,22050">64 kbps, 22.05 kHz</option>
      </select>
      <br><br>
      
      <button id="startCapture" class="primary">🤖 開始自適應轉錄</button>
      <button id="stopCapture" class="danger" disabled="">⏹️ 停止轉錄</button>
      <button id="toggleFiltering" class="secondary">🔍 過濾模式：開啟</button>
      
      <div id="captureStatus" class="status info"><div class="status info">⏹️ 自適應轉錄已停止</div></div>
    </div>
    
    <!-- 轉錄結果 -->
    <div class="section">
      <h3>📝 智能轉錄結果</h3>
      <button onclick="clearTranscriptions()" class="secondary">🗑️ 清空</button>
      <button onclick="exportTranscriptions()" class="secondary">💾 匯出</button>
      <button onclick="copyValidText()" class="secondary">📋 複製有效內容</button>
      <button onclick="showFilteredItems()" class="secondary" id="showFilteredBtn">👁️ 顯示已過濾</button>
      
      <div class="transcription-display" id="transcriptionDisplay"></div>
    </div>
  </div>

  <script>
    // 全局變量
    let capturedStream, micStream, audioContext;
    let isRecording = false;
    let audioBuffer = [];
    let currentSegmentId = 0;
    let showFiltered = false;
    let enableFiltering = true;
    
    // 自適應參數
    let adaptiveParams = {
      silenceThreshold: 0.01,
      silenceDuration: 1000,
      minAudioLength: 3,
      maxAudioLength: 30,
      environmentType: 'unknown',
      avgVolume: 0,
      volumeHistory: [],
      segmentQuality: []
    };
    
    // 統計數據
    let stats = {
      totalSegments: 0,
      validSegments: 0,
      filteredSegments: 0,
      avgConfidence: 0
    };

    const startCaptureButton = document.getElementById('startCapture');
    const stopCaptureButton = document.getElementById('stopCapture');
    const audioQualitySelect = document.getElementById('audioQuality');

    // 切換過濾模式
    document.getElementById('toggleFiltering').addEventListener('click', () => {
      enableFiltering = !enableFiltering;
      const btn = document.getElementById('toggleFiltering');
      btn.textContent = enableFiltering ? '🔍 過濾模式：開啟' : '🔍 過濾模式：關閉';
      btn.className = enableFiltering ? 'secondary' : 'warning';
    });

    // 顯示/隱藏已過濾項目
    document.getElementById('showFilteredBtn').addEventListener('click', showFilteredItems);

    // 開始自適應轉錄
    startCaptureButton.addEventListener('click', async () => {
      try {
        const statusDiv = document.getElementById('captureStatus');
        statusDiv.innerHTML = '<div class="status info">🔄 啟動自適應轉錄系統...</div>';
        
        const [audioBitRate, audioSampleRate] = audioQualitySelect.value.split(',').map(Number);

        // 捕捉音頻
        capturedStream = await navigator.mediaDevices.getDisplayMedia({
          video: { width: { ideal: 1280 }, height: { ideal: 720 }, frameRate: { ideal: 30 } },
          audio: {
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false,
            sampleRate: audioSampleRate,
            channelCount: 2
          }
        });

        micStream = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false
          }
        });

        // 建立音頻混合
        audioContext = new AudioContext();
        const destination = audioContext.createMediaStreamDestination();

        if (capturedStream.getAudioTracks().length > 0) {
          const systemAudioSource = audioContext.createMediaStreamSource(capturedStream);
          systemAudioSource.connect(destination);
        }
        if (micStream.getAudioTracks().length > 0) {
          const micAudioSource = audioContext.createMediaStreamSource(micStream);
          micAudioSource.connect(destination);
        }

        // 設置自適應處理
        setupAdaptiveProcessing(destination.stream);

        startCaptureButton.disabled = true;
        stopCaptureButton.disabled = false;
        startCaptureButton.classList.add('recording');
        
        statusDiv.innerHTML = '<div class="status success">🤖 自適應轉錄中 (智能優化)</div>';
        isRecording = true;

        // 開始環境學習
        startEnvironmentLearning();

        capturedStream.getTracks().forEach(track => {
          track.onended = () => stopCapture();
        });

      } catch (err) {
        console.error('自適應轉錄啟動失敗:', err);
        document.getElementById('captureStatus').innerHTML = 
          `<div class="status error">❌ 啟動失敗：${err.message}</div>`;
      }
    });

    // 停止轉錄
    stopCaptureButton.addEventListener('click', stopCapture);

    function stopCapture() {
      isRecording = false;
      
      if (audioBuffer.length > 0) {
        processRemainingAudio();
      }
      
      [capturedStream, micStream].forEach(stream => {
        if (stream) {
          stream.getTracks().forEach(track => track.stop());
        }
      });
      
      if (audioContext) {
        audioContext.close();
        audioContext = null;
      }
      
      startCaptureButton.disabled = false;
      stopCaptureButton.disabled = true;
      startCaptureButton.classList.remove('recording');
      
      document.getElementById('captureStatus').innerHTML = 
        '<div class="status info">⏹️ 自適應轉錄已停止</div>';
      
      updateAdaptiveStatus();
    }

    // 設置自適應處理
    function setupAdaptiveProcessing(mixedStream) {
      const source = audioContext.createMediaStreamSource(mixedStream);
      const processor = audioContext.createScriptProcessor(4096, 1, 1);

      processor.onaudioprocess = function(event) {
        if (!isRecording) return;
        
        const audioData = event.inputBuffer.getChannelData(0);
        processAdaptiveAudio(audioData);
      };

      source.connect(processor);
      processor.connect(audioContext.destination);
    }

    // 自適應音頻處理
    function processAdaptiveAudio(audioData) {
      // 計算音量
      let sum = 0;
      for (let i = 0; i < audioData.length; i++) {
        sum += audioData[i] * audioData[i];
      }
      const rms = Math.sqrt(sum / audioData.length);
      
      // 更新音量歷史
      adaptiveParams.volumeHistory.push(rms);
      if (adaptiveParams.volumeHistory.length > 1000) {
        adaptiveParams.volumeHistory.shift();
      }
      
      // 動態調整閾值
      updateDynamicThreshold();
      
      // 更新視覺化
      updateAdaptiveVisualization(rms);
      
      // 添加到緩衝區
      audioBuffer.push(...audioData);
      
      const bufferDurationMs = (audioBuffer.length / 44100) * 1000;
      
      // 智能分段判斷
      if (shouldSegmentAudio(rms, bufferDurationMs)) {
        processAudioSegment();
      }
    }

    // 動態調整閾值
    function updateDynamicThreshold() {
      if (adaptiveParams.volumeHistory.length < 100) return;
      
      const recent = adaptiveParams.volumeHistory.slice(-100);
      const avg = recent.reduce((a, b) => a + b, 0) / recent.length;
      const std = Math.sqrt(recent.reduce((a, b) => a + (b - avg) ** 2, 0) / recent.length);
      
      // 動態調整閾值
      adaptiveParams.silenceThreshold = Math.max(0.005, avg - std * 1.5);
      adaptiveParams.avgVolume = avg;
      
      // 環境類型檢測
      if (avg < 0.01 && std < 0.005) {
        adaptiveParams.environmentType = '安靜環境';
        adaptiveParams.silenceDuration = 800;
      } else if (avg > 0.05 || std > 0.02) {
        adaptiveParams.environmentType = '嘈雜環境';
        adaptiveParams.silenceDuration = 1500;
      } else {
        adaptiveParams.environmentType = '一般環境';
        adaptiveParams.silenceDuration = 1000;
      }
    }

    // 智能分段判斷
    function shouldSegmentAudio(rms, bufferDurationMs) {
      const isQuiet = rms < adaptiveParams.silenceThreshold;
      const minDuration = adaptiveParams.minAudioLength * 1000;
      const maxDuration = adaptiveParams.maxAudioLength * 1000;
      
      // 強制分段（避免過長）
      if (bufferDurationMs >= maxDuration) {
        return true;
      }
      
      // 靜音分段
      if (isQuiet && bufferDurationMs >= minDuration) {
        return true;
      }
      
      return false;
    }

    // 處理音頻段
    function processAudioSegment() {
      if (audioBuffer.length === 0) return;
      
      const segmentId = ++currentSegmentId;
      const audioBlob = createWavBlob(new Float32Array(audioBuffer), 44100);
      const duration = (audioBuffer.length / 44100).toFixed(1);
      
      // 預評估音頻質量
      const quality = evaluateAudioQuality(audioBuffer);
      
      if (enableFiltering && quality.score < 0.3) {
        // 低質量音頻，直接過濾
        stats.filteredSegments++;
        addFilteredTranscription(segmentId, duration, '音頻質量過低，已自動過濾');
      } else {
        // 添加處理中項目
        addProcessingTranscription(segmentId, duration, quality.score);
        sendAudioForTranscription(audioBlob, segmentId, quality);
      }
      
      audioBuffer = [];
      stats.totalSegments++;
    }

    // 評估音頻質量
    function evaluateAudioQuality(buffer) {
      let energy = 0;
      let zeroCrossings = 0;
      
      for (let i = 0; i < buffer.length; i++) {
        energy += buffer[i] * buffer[i];
        if (i > 0 && buffer[i] * buffer[i-1] < 0) {
          zeroCrossings++;
        }
      }
      
      const rms = Math.sqrt(energy / buffer.length);
      const zcr = zeroCrossings / buffer.length;
      
      // 質量評分 (0-1)
      let score = 0;
      if (rms > 0.005) score += 0.4; // 有足夠音量
      if (rms < 0.1) score += 0.2;   // 不過載
      if (zcr > 0.01 && zcr < 0.1) score += 0.4; // 合理的頻率變化
      
      return {
        score: score,
        rms: rms,
        zcr: zcr,
        quality: score > 0.7 ? '高' : score > 0.4 ? '中' : '低'
      };
    }

    // 開始環境學習
    function startEnvironmentLearning() {
      setInterval(() => {
        if (isRecording) {
          updateAdaptiveStatus();
        }
      }, 2000);
    }

    // 更新自適應狀態
    function updateAdaptiveStatus() {
      document.getElementById('environmentType').textContent = adaptiveParams.environmentType;
      document.getElementById('dynamicThreshold').textContent = adaptiveParams.silenceThreshold.toFixed(4);
      document.getElementById('segmentStrategy').textContent = 
        `${adaptiveParams.minAudioLength}-${adaptiveParams.maxAudioLength}秒 / ${adaptiveParams.silenceDuration}ms`;
      
      const efficiency = stats.totalSegments > 0 ? 
        ((stats.filteredSegments / stats.totalSegments) * 100).toFixed(1) : 0;
      document.getElementById('filterEfficiency').textContent = 
        `${efficiency}% (${stats.filteredSegments}/${stats.totalSegments})`;
    }

    // 更新自適應視覺化
    function updateAdaptiveVisualization(rms) {
      const volumeBar = document.getElementById('volumeBar');
      const adaptiveInfo = document.getElementById('adaptiveInfo');
      const silenceInfo = document.getElementById('silenceInfo');
      const qualityIndicator = document.getElementById('qualityIndicator');
      
      const volumePercent = Math.min(rms * 1000, 100);
      volumeBar.style.width = volumePercent + '%';
      
      adaptiveInfo.textContent = `自適應：${adaptiveParams.environmentType}`;
      silenceInfo.textContent = `閾值：${adaptiveParams.silenceThreshold.toFixed(4)} | 音量：${rms.toFixed(4)}`;
      
      const quality = rms > adaptiveParams.silenceThreshold ? '良好' : '靜音';
      qualityIndicator.textContent = `狀態：${quality}`;
    }

    // 處理剩餘音頻
    function processRemainingAudio() {
      if (audioBuffer.length > 0) {
        const duration = (audioBuffer.length / 44100).toFixed(1);
        if (parseFloat(duration) >= 1.0) {
          processAudioSegment();
        }
      }
    }

    // 創建 WAV Blob
    function createWavBlob(audioData, sampleRate) {
      const length = audioData.length;
      const buffer = new ArrayBuffer(44 + length * 2);
      const view = new DataView(buffer);
      
      const writeString = (offset, string) => {
        for (let i = 0; i < string.length; i++) {
          view.setUint8(offset + i, string.charCodeAt(i));
        }
      };
      
      writeString(0, 'RIFF');
      view.setUint32(4, 36 + length * 2, true);
      writeString(8, 'WAVE');
      writeString(12, 'fmt ');
      view.setUint32(16, 16, true);
      view.setUint16(20, 1, true);
      view.setUint16(22, 1, true);
      view.setUint32(24, sampleRate, true);
      view.setUint32(28, sampleRate * 2, true);
      view.setUint16(32, 2, true);
      view.setUint16(34, 16, true);
      writeString(36, 'data');
      view.setUint32(40, length * 2, true);
      
      let offset = 44;
      for (let i = 0; i < length; i++) {
        const sample = Math.max(-1, Math.min(1, audioData[i]));
        view.setInt16(offset, sample * 0x7FFF, true);
        offset += 2;
      }
      
      return new Blob([buffer], { type: 'audio/wav' });
    }

    // 添加處理中轉錄
    function addProcessingTranscription(segmentId, duration, qualityScore) {
      const display = document.getElementById('transcriptionDisplay');
      const timestamp = new Date().toLocaleTimeString();

      const item = document.createElement('div');
      item.className = 'transcription-item processing';
      item.id = `segment-${segmentId}`;
      item.innerHTML = `
        <div class="timestamp">${timestamp} - 段落 ${segmentId} (${duration}秒) [質量: ${(qualityScore * 100).toFixed(0)}%]</div>
        <div class="text-content">🔄 智能轉錄中...</div>
      `;

      display.appendChild(item);
      display.scrollTop = display.scrollHeight;
    }

    // 添加已過濾轉錄
    function addFilteredTranscription(segmentId, duration, reason) {
      const display = document.getElementById('transcriptionDisplay');
      const timestamp = new Date().toLocaleTimeString();

      const item = document.createElement('div');
      item.className = 'transcription-item filtered';
      item.id = `segment-${segmentId}`;
      item.style.display = showFiltered ? 'block' : 'none';
      item.innerHTML = `
        <div class="timestamp">${timestamp} - 段落 ${segmentId} (${duration}秒) [已過濾]</div>
        <div class="text-content">🚫 ${reason}</div>
      `;

      display.appendChild(item);
      display.scrollTop = display.scrollHeight;
    }

    // 發送轉錄
    async function sendAudioForTranscription(audioBlob, segmentId, quality) {
      try {
        const formData = new FormData();
        formData.append('audio_file', audioBlob, `adaptive_${segmentId}.wav`);

        const response = await fetch('/api/transcribe', {
          method: 'POST',
          body: formData
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`);
        }

        const data = await response.json();
        
        // 智能內容過濾
        const filteredResult = intelligentContentFilter(data.transcription || '', quality);
        updateTranscriptionResult(segmentId, filteredResult.text, filteredResult.isValid);
        
      } catch (error) {
        console.error('轉錄錯誤:', error);
        updateTranscriptionResult(segmentId, `❌ 轉錄失敗: ${error.message}`, false);
      }
    }

    // 智能內容過濾
    function intelligentContentFilter(text, quality) {
      if (!text || text.trim().length === 0) {
        return { text: '🔇 無語音內容', isValid: false };
      }
      
      const cleanText = text.trim();
      
      // 過濾條件
      const filters = [
        { test: t => t.length < 3, reason: '內容過短' },
        { test: t => /^[^\w\u4e00-\u9fff]*$/.test(t), reason: '無有效文字' },
        { test: t => /^(呃|嗯|啊|哦|唔){2,}$/.test(t), reason: '僅含語助詞' },
        { test: t => t.split('').every(c => c === t[0]), reason: '重複字符' },
        { test: t => quality.score < 0.3, reason: '音頻質量過低' }
      ];
      
      for (const filter of filters) {
        if (enableFiltering && filter.test(cleanText)) {
          stats.filteredSegments++;
          return { text: `🚫 已過濾：${filter.reason}`, isValid: false };
        }
      }
      
      stats.validSegments++;
      return { text: cleanText, isValid: true };
    }

    // 更新轉錄結果
    function updateTranscriptionResult(segmentId, transcription, isValid) {
      const item = document.getElementById(`segment-${segmentId}`);
      if (item) {
        const textContent = item.querySelector('.text-content');
        textContent.textContent = transcription;
        item.classList.remove('processing');
        
        if (!isValid) {
          item.classList.add('filtered');
          item.style.display = showFiltered ? 'block' : 'none';
        }
      }
    }

    // 顯示/隱藏已過濾項目
    function showFilteredItems() {
      showFiltered = !showFiltered;
      const filteredItems = document.querySelectorAll('.transcription-item.filtered');
      const btn = document.getElementById('showFilteredBtn');
      
      filteredItems.forEach(item => {
        item.style.display = showFiltered ? 'block' : 'none';
      });
      
      btn.textContent = showFiltered ? '👁️ 隱藏已過濾' : '👁️ 顯示已過濾';
    }

    // 清空轉錄
    function clearTranscriptions() {
      document.getElementById('transcriptionDisplay').innerHTML = '';
      stats = { totalSegments: 0, validSegments: 0, filteredSegments: 0, avgConfidence: 0 };
      currentSegmentId = 0;
    }

    // 匯出轉錄
    function exportTranscriptions() {
      const display = document.getElementById('transcriptionDisplay');
      const validItems = display.querySelectorAll('.transcription-item:not(.filtered):not(.processing)');

      let exportText = '自適應智能轉錄記錄\n================\n\n';

      validItems.forEach(item => {
        const timestamp = item.querySelector('.timestamp').textContent;
        const textContent = item.querySelector('.text-content').textContent;
        if (!textContent.includes('轉錄失敗') && !textContent.includes('已過濾')) {
          exportText += `${timestamp}\n${textContent}\n\n`;
        }
      });

      const blob = new Blob([exportText], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `adaptive_transcription_${new Date().toISOString().slice(0, 19)}.txt`;
      a.click();
      URL.revokeObjectURL(url);
    }

    // 複製有效內容
    function copyValidText() {
      const display = document.getElementById('transcriptionDisplay');
      const validItems = display.querySelectorAll('.transcription-item:not(.filtered):not(.processing)');

      let copyText = '';
      validItems.forEach(item => {
        const textContent = item.querySelector('.text-content').textContent;
        if (!textContent.includes('轉錄失敗') && !textContent.includes('已過濾') && 
            !textContent.includes('轉錄中')) {
          copyText += textContent + '\n';
        }
      });

      navigator.clipboard.writeText(copyText).then(() => {
        alert(`已複製 ${copyText.split('\n').filter(line => line.trim()).length} 條有效轉錄內容`);
      });
    }
  </script>


</body></html>