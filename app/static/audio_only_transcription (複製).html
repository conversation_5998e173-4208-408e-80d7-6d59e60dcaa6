<!DOCTYPE html>
<html lang="zh-TW">
<head>
  <meta charset="UTF-8">
  <title>純音頻會議轉錄系統 (基於您的技術)</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      color: white;
    }
    
    .container {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 20px;
      padding: 30px;
      color: #333;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }
    
    h1 {
      text-align: center;
      color: #2c3e50;
      margin-bottom: 30px;
    }
    
    .section {
      background: #f8f9fa;
      border-radius: 15px;
      padding: 25px;
      margin: 20px 0;
      border: 2px solid #e9ecef;
    }
    
    button {
      margin: 5px 10px 5px 0;
      padding: 12px 24px;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
      border: none;
      border-radius: 8px;
      transition: all 0.3s ease;
    }
    
    button.primary {
      background: linear-gradient(45deg, #28a745, #20c997);
      color: white;
    }
    
    button.danger {
      background: linear-gradient(45deg, #dc3545, #fd7e14);
      color: white;
    }
    
    button.secondary {
      background: linear-gradient(45deg, #6c757d, #495057);
      color: white;
    }
    
    button:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    
    button:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
      opacity: 0.6;
    }
    
    button.recording {
      animation: pulse 1.5s infinite;
    }
    
    @keyframes pulse {
      0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
      70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
      100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
    }
    
    select {
      margin: 5px 10px 5px 0;
      padding: 8px 12px;
      font-size: 16px;
      border: 2px solid #dee2e6;
      border-radius: 8px;
      background: white;
    }
    
    .status {
      padding: 15px;
      margin: 10px 0;
      border-radius: 8px;
      font-weight: bold;
    }
    
    .status.success {
      background: #d4edda;
      color: #155724;
    }
    
    .status.error {
      background: #f8d7da;
      color: #721c24;
    }
    
    .status.info {
      background: #d1ecf1;
      color: #0c5460;
    }
    
    .transcription-display {
      background: white;
      border: 1px solid #dee2e6;
      border-radius: 10px;
      padding: 20px;
      margin: 20px 0;
      max-height: 500px;
      overflow-y: auto;
    }
    
    .transcription-item {
      background: #f8f9fa;
      margin: 10px 0;
      padding: 15px;
      border-radius: 8px;
      border-left: 4px solid #6f42c1;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .timestamp {
      font-size: 12px;
      color: #6c757d;
      margin-bottom: 5px;
    }
    
    .text-content {
      font-size: 16px;
      line-height: 1.5;
      margin-top: 8px;
    }
    
    .success-box {
      background: #d4edda;
      border: 1px solid #c3e6cb;
      padding: 15px;
      border-radius: 10px;
      margin: 15px 0;
      color: #155724;
    }
    
    .warning-box {
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      padding: 15px;
      border-radius: 10px;
      margin: 15px 0;
      color: #856404;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🎤 純音頻會議轉錄系統</h1>
    
    <!-- 說明 -->
    <div class="success-box">
      <h3>✅ 完全基於您的音頻捕獲技術</h3>
      <p>此版本完全使用您提供的可靠方法，但專注於音頻轉錄：</p>
      <ul>
        <li><strong>包含視頻軌道</strong>：避免 "video is required" 錯誤</li>
        <li><strong>只處理音頻</strong>：視頻軌道存在但不顯示，只用音頻轉錄</li>
        <li><strong>AudioContext 混合</strong>：完全使用您的混合技術</li>
        <li><strong>實時轉錄</strong>：結合 OpenAI Whisper</li>
      </ul>
    </div>
    
    <!-- 設置 -->
    <div class="section">
      <h3>🎛️ 音頻設置</h3>
      <label for="audioQuality">選擇音頻品質：</label>
      <select id="audioQuality">
        <option value="192000,44100">192 kbps, 44.1 kHz</option>
        <option value="128000,44100">128 kbps, 44.1 kHz</option>
        <option value="64000,22050">64 kbps, 22.05 kHz</option>
      </select>
    </div>
    
    <!-- 控制 -->
    <div class="section">
      <h3>🎯 會議轉錄控制</h3>
      <div class="warning-box">
        <h4>📋 使用步驟</h4>
        <ol>
          <li>點擊「開始會議轉錄」</li>
          <li>選擇要分享的螢幕或瀏覽器標籤</li>
          <li><strong>重要：勾選「分享音頻」</strong></li>
          <li>允許麥克風權限</li>
          <li>開始會議，系統會自動轉錄</li>
        </ol>
      </div>
      
      <button id="startCapture" class="primary">🎯 開始會議轉錄</button>
      <button id="stopCapture" class="danger" disabled>⏹️ 停止轉錄</button>
      
      <div id="captureStatus" class="status info">點擊開始會議轉錄</div>
    </div>
    
    <!-- 轉錄結果 -->
    <div class="section">
      <h3>📝 轉錄結果</h3>
      <button onclick="clearTranscriptions()" class="secondary">🗑️ 清空</button>
      <button onclick="exportTranscriptions()" class="secondary">💾 匯出</button>
      <button onclick="copyAllText()" class="secondary">📋 複製</button>
      
      <div class="transcription-display" id="transcriptionDisplay">
        <div class="transcription-item">
          <div class="timestamp">等待中...</div>
          <div class="text-content">轉錄結果將在此顯示</div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 全局變量 - 完全基於您的代碼
    let capturedStream; // 螢幕及系統聲音串流
    let micStream;      // 麥克風串流
    let audioContext;
    let isRecording = false;
    let audioBuffer = [];
    let lastProcessTime = 0;

    const startCaptureButton = document.getElementById('startCapture');
    const stopCaptureButton = document.getElementById('stopCapture');
    const audioQualitySelect = document.getElementById('audioQuality');

    // 開始捕獲 - 完全使用您的方法
    startCaptureButton.addEventListener('click', async () => {
      try {
        const statusDiv = document.getElementById('captureStatus');
        statusDiv.innerHTML = '<div class="status info">🔄 正在啟動...</div>';
        
        const [audioBitRate, audioSampleRate] = audioQualitySelect.value.split(',').map(Number);

        // 捕捉螢幕影音（含系統聲音）- 完全使用您的配置
        capturedStream = await navigator.mediaDevices.getDisplayMedia({
          video: {
            width: { ideal: 1280 },
            height: { ideal: 720 },
            frameRate: { ideal: 30 }
          },
          audio: {
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false,
            sampleRate: audioSampleRate,
            channelCount: 2
          }
        });

        // 捕捉本地麥克風音訊 - 完全使用您的配置
        micStream = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false
          }
        });

        // 建立 AudioContext 與混合目的地 - 完全使用您的方法
        audioContext = new AudioContext();
        const destination = audioContext.createMediaStreamDestination();

        if (capturedStream.getAudioTracks().length > 0) {
          const systemAudioSource = audioContext.createMediaStreamSource(capturedStream);
          systemAudioSource.connect(destination);
        }
        if (micStream.getAudioTracks().length > 0) {
          const micAudioSource = audioContext.createMediaStreamSource(micStream);
          micAudioSource.connect(destination);
        }

        // 設置實時轉錄處理
        setupTranscriptionProcessing(destination.stream);

        startCaptureButton.disabled = true;
        stopCaptureButton.disabled = false;
        startCaptureButton.classList.add('recording');
        
        statusDiv.innerHTML = '<div class="status success">🎵 會議轉錄中 (系統+麥克風)</div>';
        isRecording = true;

        // 監聽流結束
        capturedStream.getTracks().forEach(track => {
          track.onended = () => {
            stopCapture();
          };
        });

      } catch (err) {
        console.error('捕捉失敗:', err);
        document.getElementById('captureStatus').innerHTML = 
          `<div class="status error">❌ 捕獲失敗：${err.message}<br>
          <small>請確保：1. 選擇包含音頻的來源 2. 勾選"分享音頻" 3. 允許麥克風權限</small></div>`;
      }
    });

    // 停止捕獲
    stopCaptureButton.addEventListener('click', stopCapture);

    function stopCapture() {
      isRecording = false;
      
      if (capturedStream) {
        capturedStream.getTracks().forEach(track => track.stop());
        capturedStream = null;
      }
      if (micStream) {
        micStream.getTracks().forEach(track => track.stop());
        micStream = null;
      }
      if (audioContext) {
        audioContext.close();
        audioContext = null;
      }
      
      startCaptureButton.disabled = false;
      stopCaptureButton.disabled = true;
      startCaptureButton.classList.remove('recording');
      
      document.getElementById('captureStatus').innerHTML = 
        '<div class="status info">⏹️ 轉錄已停止</div>';
    }

    // 設置轉錄處理
    function setupTranscriptionProcessing(mixedStream) {
      const source = audioContext.createMediaStreamSource(mixedStream);
      const processor = audioContext.createScriptProcessor(4096, 1, 1);

      processor.onaudioprocess = function(event) {
        if (!isRecording) return;
        
        const audioData = event.inputBuffer.getChannelData(0);
        processAudioForTranscription(audioData);
      };

      source.connect(processor);
      processor.connect(audioContext.destination);
    }

    // 處理音頻進行轉錄
    function processAudioForTranscription(audioData) {
      audioBuffer.push(...audioData);
      
      const now = Date.now();
      if (now - lastProcessTime > 3000 && audioBuffer.length > 0) {
        lastProcessTime = now;
        
        const audioBlob = createWavBlob(new Float32Array(audioBuffer), 44100);
        sendAudioForTranscription(audioBlob);
        
        audioBuffer = audioBuffer.slice(-4096);
      }
    }

    // 創建 WAV Blob
    function createWavBlob(audioData, sampleRate) {
      const length = audioData.length;
      const buffer = new ArrayBuffer(44 + length * 2);
      const view = new DataView(buffer);
      
      const writeString = (offset, string) => {
        for (let i = 0; i < string.length; i++) {
          view.setUint8(offset + i, string.charCodeAt(i));
        }
      };
      
      writeString(0, 'RIFF');
      view.setUint32(4, 36 + length * 2, true);
      writeString(8, 'WAVE');
      writeString(12, 'fmt ');
      view.setUint32(16, 16, true);
      view.setUint16(20, 1, true);
      view.setUint16(22, 1, true);
      view.setUint32(24, sampleRate, true);
      view.setUint32(28, sampleRate * 2, true);
      view.setUint16(32, 2, true);
      view.setUint16(34, 16, true);
      writeString(36, 'data');
      view.setUint32(40, length * 2, true);
      
      let offset = 44;
      for (let i = 0; i < length; i++) {
        const sample = Math.max(-1, Math.min(1, audioData[i]));
        view.setInt16(offset, sample * 0x7FFF, true);
        offset += 2;
      }
      
      return new Blob([buffer], { type: 'audio/wav' });
    }

    // 發送音頻進行轉錄
    async function sendAudioForTranscription(audioBlob) {
      try {
        const formData = new FormData();
        formData.append('audio_file', audioBlob, 'meeting_audio.wav');

        const response = await fetch('/api/transcribe', {
          method: 'POST',
          body: formData
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`);
        }

        const data = await response.json();
        
        if (data.transcription && data.transcription.trim()) {
          addTranscription(data.transcription);
        }
      } catch (error) {
        console.error('轉錄錯誤:', error);
      }
    }

    // 添加轉錄結果
    function addTranscription(text) {
      const display = document.getElementById('transcriptionDisplay');
      const timestamp = new Date().toLocaleTimeString();

      const item = document.createElement('div');
      item.className = 'transcription-item';
      item.innerHTML = `
        <div class="timestamp">${timestamp} - 會議音頻</div>
        <div class="text-content">${text}</div>
      `;

      display.appendChild(item);
      display.scrollTop = display.scrollHeight;
    }

    // 清空轉錄
    function clearTranscriptions() {
      document.getElementById('transcriptionDisplay').innerHTML = '';
    }

    // 匯出轉錄
    function exportTranscriptions() {
      const display = document.getElementById('transcriptionDisplay');
      const items = display.querySelectorAll('.transcription-item');

      let exportText = '會議轉錄記錄\n================\n\n';

      items.forEach(item => {
        const timestamp = item.querySelector('.timestamp').textContent;
        const textContent = item.querySelector('.text-content').textContent;
        exportText += `${timestamp}\n${textContent}\n\n`;
      });

      const blob = new Blob([exportText], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `meeting_transcription_${new Date().toISOString().slice(0, 19)}.txt`;
      a.click();
      URL.revokeObjectURL(url);
    }

    // 複製全部文字
    function copyAllText() {
      const display = document.getElementById('transcriptionDisplay');
      const items = display.querySelectorAll('.transcription-item');

      let copyText = '';
      items.forEach(item => {
        const textContent = item.querySelector('.text-content').textContent;
        if (textContent) {
          copyText += textContent + '\n';
        }
      });

      navigator.clipboard.writeText(copyText).then(() => {
        alert('轉錄內容已複製到剪貼板');
      });
    }
  </script>
</body>
</html>
