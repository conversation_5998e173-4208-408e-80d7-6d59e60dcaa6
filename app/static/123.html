
<!DOCTYPE html>
<html lang="zh-TW">
<head>
  <meta charset="UTF-8">
  <title>系統影音及 STT 即時串流 (含系統聲音與麥克風音訊)</title>
  <!-- 引入 FFmpeg.wasm -->
  <script src="https://unpkg.com/@ffmpeg/ffmpeg@0.11.6/dist/ffmpeg.min.js"></script>
  <style>
    body {
      background-color: pink;
      font-family: Arial, sans-serif;
      margin: 40px;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    
    button {
      margin: 5px 10px 5px 0;
      padding: 10px 20px;
      font-size: 16px;
      cursor: pointer;
      background-color: #33d413;
      color: rgb(48, 16, 224);
      border: none;
      border-radius: 4px;
    }

    button:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
    }

    select {
      margin: 5px 10px 5px 0;
      padding: 5px;
      font-size: 16px;
    }

    video {
      display: block;
      margin: 20px 0;
      max-width: 100%;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    textarea {
      width: 100%;
      height: 150px;
      margin-top: 10px;
      font-size: 16px;
      padding: 10px;
      box-sizing: border-box;
    }

    #recordingsList, #sttSection {
      margin-top: 20px;
    }

    .recording-item {
      margin-bottom: 30px;
      padding: 20px;
      background-color: #f8f9fa;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
  </style>
</head>
<body>
  <h1>電腦影音錄影(含系統聲音與麥克風音訊)</h1>
  
  <!-- 【一】 系統影音及麥克風捕捉 (錄製影片) -->
  <label for="videoQuality">選擇影像品質：</label>
  <select id="videoQuality">
    <option value="1920,1080,60">1080p 60fps</option>
    <option value="1280,720,30">720p 30fps</option>
    <option value="640,480,15">480p 15fps</option>
  </select>

  <label for="audioQuality">選擇音頻品質：</label>
  <select id="audioQuality">
    <option value="192000,44101">192 kbps, 44.1 kHz</option>
    <option value="128000,44101">128 kbps, 44.1 kHz</option>
    <option value="64000,22050">64 kbps, 22.05 kHz</option>
  </select>

  <button id="startCapture">開始捕捉 (錄製影片)</button>
  <button id="stopCapture" disabled>停止捕捉 (錄製影片)</button>
  
  <h2>錄製結果</h2>
  <div id="recordingsList"></div>

  <hr>
  <!-- 【二】 STT 即時串流功能 (混合系統音與麥克風音) -->
  <h2></h2>
  <p></p>

	<a href="http://tw100025521:8080/" target="_blank">
	<button id="startSTT1">語音轉文字</button>
	</a>
  <script>
    // ========== FFmpeg 初始化 ==========
    const { createFFmpeg, fetchFile } = FFmpeg;
    const ffmpeg = createFFmpeg({ log: true });
    let ffmpegLoaded = false;
    async function loadFFmpeg() {
      if (!ffmpegLoaded) {
        await ffmpeg.load();
        ffmpegLoaded = true;
      }
    }

    // ========== 【一】 系統影音及麥克風捕捉 (錄製影片) ==========
    const startCaptureButton = document.getElementById('startCapture');
    const stopCaptureButton = document.getElementById('stopCapture');
    const videoQualitySelect = document.getElementById('videoQuality');
    const audioQualitySelect = document.getElementById('audioQuality');
    const recordingsList = document.getElementById('recordingsList');

    let mediaRecorder;
    let recordedChunks = [];
    let capturedStream; // 螢幕及系統聲音串流
    let micStream;      // 麥克風串流
    let audioContext;

    startCaptureButton.addEventListener('click', async () => {
      try {
        const [videoWidth, videoHeight, frameRate] = videoQualitySelect.value.split(',').map(Number);
        const [audioBitRate, audioSampleRate] = audioQualitySelect.value.split(',').map(Number);

        // 捕捉螢幕影音（含系統聲音）
        capturedStream = await navigator.mediaDevices.getDisplayMedia({
          video: {
            width: { ideal: videoWidth },
            height: { ideal: videoHeight },
            frameRate: { ideal: frameRate }
          },
          audio: {
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false,
            sampleRate: audioSampleRate,
            channelCount: 2
          }
        });

        // 捕捉本地麥克風音訊
        micStream = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false
          }
        });

        // 建立 AudioContext 與混合目的地
        audioContext = new AudioContext();
        const destination = audioContext.createMediaStreamDestination();

        if (capturedStream.getAudioTracks().length > 0) {
          const systemAudioSource = audioContext.createMediaStreamSource(capturedStream);
          systemAudioSource.connect(destination);
        }
        if (micStream.getAudioTracks().length > 0) {
          const micAudioSource = audioContext.createMediaStreamSource(micStream);
          micAudioSource.connect(destination);
        }

        // 最終混合串流包含螢幕視訊軌與混合後的音訊軌
        const finalStream = new MediaStream([
          ...capturedStream.getVideoTracks(),
          ...destination.stream.getAudioTracks()
        ]);

        mediaRecorder = new MediaRecorder(finalStream, {
          mimeType: 'video/webm;codecs=vp8,opus',
          videoBitsPerSecond: videoWidth * videoHeight * frameRate * 0.07,
          audioBitsPerSecond: audioBitRate
        });

        mediaRecorder.ondataavailable = event => {
          if (event.data.size > 0) {
            recordedChunks.push(event.data);
          }
        };

        mediaRecorder.onstop = () => {
          const videoBlob = new Blob(recordedChunks, { type: 'video/webm' });
          const videoUrl = URL.createObjectURL(videoBlob);
          const listItem = document.createElement('div');
          listItem.className = 'recording-item';

          const video = document.createElement('video');
          video.controls = true;
          video.src = videoUrl;
          listItem.appendChild(video);

          const downloadButton = document.createElement('button');
          downloadButton.textContent = '下載錄影';
          downloadButton.onclick = () => {
            const a = document.createElement('a');
            a.href = videoUrl;
            a.download = `recording_${Date.now()}.webm`;
            a.click();
          };
          listItem.appendChild(downloadButton);

          recordingsList.appendChild(listItem);
          recordedChunks = [];
        };

        mediaRecorder.start();
        startCaptureButton.disabled = true;
        stopCaptureButton.disabled = false;
      } catch (err) {
        console.error('捕捉失敗:', err);
        alert(`無法捕捉影音。錯誤訊息: ${err.message}`);
      }
    });

    stopCaptureButton.addEventListener('click', () => {
      if (mediaRecorder && mediaRecorder.state !== 'inactive') {
        mediaRecorder.stop();
        startCaptureButton.disabled = false;
        stopCaptureButton.disabled = true;
      }
      if (capturedStream) {
        capturedStream.getTracks().forEach(track => track.stop());
      }
      if (micStream) {
        micStream.getTracks().forEach(track => track.stop());
      }
      if (audioContext) {
        audioContext.close();
      }
    });


  </script>
  <script>
  document.getElementById("startSTT").addEventListener("click", function() {
    window.location.href = "file://\\Auo\gfs\AESH00\CFT\DT_Team\給俊宏\templates\web_record004.html";
  });
</script>
</body>
</html>




