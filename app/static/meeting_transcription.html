<!DOCTYPE html>
<html lang="zh-TW">
<head>
  <meta charset="UTF-8">
  <title>會議轉錄系統 - 本地服務器版本</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      color: white;
    }
    
    .container {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 20px;
      padding: 30px;
      color: #333;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }
    
    h1 {
      text-align: center;
      color: #2c3e50;
      margin-bottom: 30px;
      font-size: 2.2em;
    }
    
    .section {
      background: #f8f9fa;
      border-radius: 15px;
      padding: 25px;
      margin: 20px 0;
      border: 2px solid #e9ecef;
    }
    
    .browser-check {
      background: #e3f2fd;
      border: 2px solid #2196f3;
      border-radius: 15px;
      padding: 20px;
      margin: 20px 0;
    }
    
    .browser-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 10px 0;
      padding: 10px;
      background: white;
      border-radius: 8px;
    }
    
    button {
      padding: 12px 24px;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
      border: none;
      border-radius: 8px;
      margin: 5px;
      transition: all 0.3s ease;
    }
    
    button.primary {
      background: linear-gradient(45deg, #28a745, #20c997);
      color: white;
    }
    
    button.danger {
      background: linear-gradient(45deg, #dc3545, #fd7e14);
      color: white;
    }
    
    button.secondary {
      background: linear-gradient(45deg, #6c757d, #495057);
      color: white;
    }
    
    button:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    
    button:disabled {
      background: #6c757d;
      cursor: not-allowed;
      opacity: 0.6;
    }
    
    button.recording {
      animation: pulse 1.5s infinite;
    }
    
    @keyframes pulse {
      0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
      70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
      100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
    }
    
    .status {
      padding: 15px;
      margin: 10px 0;
      border-radius: 8px;
      font-weight: bold;
    }
    
    .status.success {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    
    .status.error {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    
    .status.info {
      background: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }
    
    .status.warning {
      background: #fff3cd;
      color: #856404;
      border: 1px solid #ffeaa7;
    }
    
    .transcription-display {
      background: white;
      border: 1px solid #dee2e6;
      border-radius: 10px;
      padding: 20px;
      margin: 20px 0;
      max-height: 500px;
      overflow-y: auto;
    }
    
    .transcription-item {
      background: #f8f9fa;
      margin: 10px 0;
      padding: 15px;
      border-radius: 8px;
      border-left: 4px solid #007bff;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .transcription-item.microphone {
      border-left-color: #ffc107;
    }
    
    .transcription-item.system {
      border-left-color: #28a745;
    }
    
    .transcription-item.file {
      border-left-color: #6f42c1;
    }
    
    .timestamp {
      font-size: 12px;
      color: #6c757d;
      margin-bottom: 5px;
    }
    
    .source-tag {
      display: inline-block;
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: bold;
      margin-right: 10px;
    }
    
    .source-tag.microphone {
      background: #ffc107;
      color: #212529;
    }
    
    .source-tag.system {
      background: #28a745;
      color: white;
    }
    
    .source-tag.file {
      background: #6f42c1;
      color: white;
    }
    
    .text-content {
      font-size: 16px;
      line-height: 1.5;
      margin-top: 8px;
    }
    
    input[type="file"] {
      margin: 10px 0;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      background: white;
      width: 100%;
      max-width: 400px;
    }
    
    .warning-box {
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      padding: 15px;
      border-radius: 10px;
      margin: 15px 0;
      color: #856404;
    }
    
    .info-box {
      background: #d1ecf1;
      border: 1px solid #bee5eb;
      padding: 15px;
      border-radius: 10px;
      margin: 15px 0;
      color: #0c5460;
    }
    
    .success-box {
      background: #d4edda;
      border: 1px solid #c3e6cb;
      padding: 15px;
      border-radius: 10px;
      margin: 15px 0;
      color: #155724;
    }
    
    .feature-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;
      margin: 20px 0;
    }
    
    .feature-card {
      background: white;
      border: 2px solid #e9ecef;
      border-radius: 10px;
      padding: 20px;
      text-align: center;
    }
    
    .feature-card.recommended {
      border-color: #28a745;
      background: #f8fff9;
    }
    
    .feature-card h4 {
      margin-top: 0;
      color: #2c3e50;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🎤 會議轉錄系統</h1>
    
    <!-- 瀏覽器兼容性檢查 -->
    <div class="browser-check">
      <h3>🌐 瀏覽器兼容性檢查</h3>
      <div class="browser-item">
        <span>當前瀏覽器：</span>
        <span id="browserInfo">檢測中...</span>
      </div>
      <div class="browser-item">
        <span>getUserMedia 支援：</span>
        <span id="getUserMediaSupport">檢測中...</span>
      </div>
      <div class="browser-item">
        <span>getDisplayMedia 支援：</span>
        <span id="getDisplayMediaSupport">檢測中...</span>
      </div>
      <div class="browser-item">
        <span>協議類型：</span>
        <span id="protocolInfo">檢測中...</span>
      </div>
    </div>
    
    <!-- 功能選擇 -->
    <div class="feature-grid">
      <div class="feature-card recommended">
        <h4>📁 文件上傳轉錄</h4>
        <p><strong>推薦方式</strong><br>兼容性最好，支援所有瀏覽器</p>
        <button onclick="showFileUpload()" class="primary">選擇此方式</button>
      </div>
      
      <div class="feature-card">
        <h4>🎙️ 麥克風轉錄</h4>
        <p>即時語音輸入轉錄<br>適合個人發言記錄</p>
        <button onclick="showMicrophone()" class="secondary">選擇此方式</button>
      </div>
      
      <div class="feature-card">
        <h4>🖥️ 系統音頻轉錄</h4>
        <p>捕獲系統聲音<br>需要瀏覽器支援</p>
        <button onclick="showSystemAudio()" class="secondary">選擇此方式</button>
      </div>
    </div>
    
    <!-- 文件上傳區域 -->
    <div id="fileUploadSection" class="section" style="display: none;">
      <h3>📁 文件上傳轉錄</h3>
      <div class="success-box">
        <h4>✅ 推薦使用此方式</h4>
        <p>文件上傳轉錄具有最好的兼容性和準確性：</p>
        <ul>
          <li>支援所有瀏覽器和作業系統</li>
          <li>可以處理長時間的會議錄音</li>
          <li>轉錄質量最高</li>
          <li>支援多種音頻格式</li>
        </ul>
      </div>
      
      <input type="file" id="audioFile" accept="audio/*">
      <br>
      <button onclick="uploadFile()" class="primary">📤 上傳並轉錄</button>
      <button onclick="hideAllSections()" class="secondary">返回選擇</button>
      <div id="uploadStatus" class="status info">選擇音頻文件後點擊上傳</div>
    </div>
    
    <!-- 麥克風區域 -->
    <div id="microphoneSection" class="section" style="display: none;">
      <h3>🎙️ 麥克風轉錄</h3>
      <div class="info-box">
        <p><strong>使用說明：</strong></p>
        <p>點擊開始後對著麥克風說話，系統會即時轉錄您的語音。適合個人發言記錄。</p>
      </div>
      
      <button id="startMic" onclick="startMicrophone()" class="primary">🎙️ 開始麥克風</button>
      <button id="stopMic" onclick="stopMicrophone()" class="danger" disabled>⏹️ 停止麥克風</button>
      <button onclick="hideAllSections()" class="secondary">返回選擇</button>
      <div id="micStatus" class="status info">點擊開始麥克風錄音</div>
    </div>
    
    <!-- 系統音頻區域 -->
    <div id="systemAudioSection" class="section" style="display: none;">
      <h3>🖥️ 系統音頻轉錄</h3>
      <div class="warning-box">
        <h4>⚠️ 系統音頻捕獲要求</h4>
        <p><strong>如果遇到 "Not supported" 錯誤，請檢查：</strong></p>
        <ol>
          <li><strong>瀏覽器：</strong>使用 Chrome 88+ 或 Edge 88+</li>
          <li><strong>協議：</strong>確保使用 http://localhost 而非 file://</li>
          <li><strong>權限：</strong>允許瀏覽器訪問螢幕分享</li>
          <li><strong>音頻：</strong>在分享對話框中勾選"分享音頻"</li>
        </ol>
      </div>
      
      <button id="startSystem" onclick="startSystemAudio()" class="primary">🖥️ 開始系統音頻</button>
      <button id="stopSystem" onclick="stopSystemAudio()" class="danger" disabled>⏹️ 停止系統音頻</button>
      <button onclick="hideAllSections()" class="secondary">返回選擇</button>
      <div id="systemStatus" class="status info">點擊開始系統音頻捕獲</div>
    </div>
    
    <!-- 轉錄結果 -->
    <div class="section">
      <h3>📝 轉錄結果</h3>
      <button onclick="clearTranscriptions()">🗑️ 清空</button>
      <button onclick="exportTranscriptions()">💾 匯出</button>
      <button onclick="copyAllText()">📋 複製全部</button>
      <div class="transcription-display" id="transcriptionDisplay">
        <div class="transcription-item">
          <div class="timestamp">等待中...</div>
          <span class="source-tag file">系統</span>
          <div class="text-content">轉錄結果將在此顯示</div>
        </div>
      </div>
    </div>
  </div>

  <script>
    let microphoneStream = null;
    let systemAudioStream = null;
    let audioContext = null;
    let isRecording = false;
    let audioBuffer = [];
    let lastProcessTime = 0;

    // 頁面載入時檢查瀏覽器兼容性
    window.onload = function() {
      checkBrowserCompatibility();
    };

    // 檢查瀏覽器兼容性
    function checkBrowserCompatibility() {
      // 檢測瀏覽器
      const userAgent = navigator.userAgent;
      let browserName = "未知瀏覽器";
      
      if (userAgent.includes("Chrome")) {
        browserName = "Chrome";
      } else if (userAgent.includes("Firefox")) {
        browserName = "Firefox";
      } else if (userAgent.includes("Safari")) {
        browserName = "Safari";
      } else if (userAgent.includes("Edge")) {
        browserName = "Edge";
      }
      
      document.getElementById('browserInfo').textContent = browserName;
      
      // 檢測 getUserMedia 支援
      const getUserMediaSupported = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
      document.getElementById('getUserMediaSupport').innerHTML = 
        getUserMediaSupported ? '<span style="color: #28a745;">✅ 支援</span>' : '<span style="color: #dc3545;">❌ 不支援</span>';
      
      // 檢測 getDisplayMedia 支援
      const getDisplayMediaSupported = !!(navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia);
      document.getElementById('getDisplayMediaSupport').innerHTML = 
        getDisplayMediaSupported ? '<span style="color: #28a745;">✅ 支援</span>' : '<span style="color: #dc3545;">❌ 不支援</span>';
      
      // 檢測協議
      const protocol = window.location.protocol;
      document.getElementById('protocolInfo').innerHTML = 
        protocol === 'file:' ? '<span style="color: #dc3545;">❌ file:// (建議使用 http://)</span>' : 
        '<span style="color: #28a745;">✅ ' + protocol + '</span>';
    }

    // 顯示文件上傳
    function showFileUpload() {
      hideAllSections();
      document.getElementById('fileUploadSection').style.display = 'block';
    }

    // 顯示麥克風
    function showMicrophone() {
      hideAllSections();
      document.getElementById('microphoneSection').style.display = 'block';
    }

    // 顯示系統音頻
    function showSystemAudio() {
      hideAllSections();
      document.getElementById('systemAudioSection').style.display = 'block';
    }

    // 隱藏所有區域
    function hideAllSections() {
      document.getElementById('fileUploadSection').style.display = 'none';
      document.getElementById('microphoneSection').style.display = 'none';
      document.getElementById('systemAudioSection').style.display = 'none';
      
      // 停止所有錄音
      stopMicrophone();
      stopSystemAudio();
    }

    // 文件上傳轉錄
    async function uploadFile() {
      const fileInput = document.getElementById('audioFile');
      const statusDiv = document.getElementById('uploadStatus');
      
      if (!fileInput.files[0]) {
        statusDiv.innerHTML = '<div class="status error">❌ 請選擇音頻文件</div>';
        return;
      }

      const file = fileInput.files[0];
      statusDiv.innerHTML = '<div class="status info">🔄 上傳轉錄中...</div>';

      try {
        const formData = new FormData();
        formData.append('audio_file', file);

        const response = await fetch('/api/transcribe', {
          method: 'POST',
          body: formData
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`);
        }

        const data = await response.json();
        
        statusDiv.innerHTML = '<div class="status success">✅ 轉錄完成</div>';
        addTranscription(data.transcription, 'file');
        
      } catch (error) {
        statusDiv.innerHTML = `<div class="status error">❌ 轉錄失敗：${error.message}</div>`;
      }
    }

    // 開始麥克風
    async function startMicrophone() {
      try {
        microphoneStream = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: true,
            noiseSuppression: true,
            sampleRate: 44100
          }
        });

        setupAudioProcessing(microphoneStream, 'microphone');
        
        document.getElementById('startMic').disabled = true;
        document.getElementById('stopMic').disabled = false;
        document.getElementById('startMic').classList.add('recording');
        
        document.getElementById('micStatus').innerHTML = 
          '<div class="status success">🎙️ 麥克風錄音中...</div>';
        
        isRecording = true;

      } catch (error) {
        document.getElementById('micStatus').innerHTML = 
          `<div class="status error">❌ 麥克風啟動失敗：${error.message}</div>`;
      }
    }

    // 停止麥克風
    function stopMicrophone() {
      if (microphoneStream) {
        microphoneStream.getTracks().forEach(track => track.stop());
        microphoneStream = null;
      }
      
      isRecording = false;
      
      document.getElementById('startMic').disabled = false;
      document.getElementById('stopMic').disabled = true;
      document.getElementById('startMic').classList.remove('recording');
      
      document.getElementById('micStatus').innerHTML = 
        '<div class="status info">⏹️ 麥克風已停止</div>';
    }

    // 開始系統音頻
    async function startSystemAudio() {
      try {
        // 詳細的兼容性檢查
        if (!navigator.mediaDevices) {
          throw new Error('瀏覽器不支援 MediaDevices API');
        }
        
        if (!navigator.mediaDevices.getDisplayMedia) {
          throw new Error('瀏覽器不支援 getDisplayMedia API，請使用 Chrome 88+ 或 Edge 88+');
        }

        systemAudioStream = await navigator.mediaDevices.getDisplayMedia({
          video: false,
          audio: {
            echoCancellation: false,
            noiseSuppression: false,
            sampleRate: 44100,
            channelCount: 2
          }
        });

        // 檢查音頻軌道
        const audioTracks = systemAudioStream.getAudioTracks();
        if (audioTracks.length === 0) {
          throw new Error('未捕獲到音頻軌道。請確保：1. 選擇了包含音頻的來源 2. 勾選了"分享音頻"選項');
        }

        setupAudioProcessing(systemAudioStream, 'system');
        
        document.getElementById('startSystem').disabled = true;
        document.getElementById('stopSystem').disabled = false;
        document.getElementById('startSystem').classList.add('recording');
        
        document.getElementById('systemStatus').innerHTML = 
          '<div class="status success">🖥️ 系統音頻捕獲中...</div>';
        
        isRecording = true;

        // 監聽流結束事件
        systemAudioStream.getTracks().forEach(track => {
          track.onended = () => {
            stopSystemAudio();
          };
        });

      } catch (error) {
        let errorMessage = error.message;
        let suggestions = '';
        
        if (error.name === 'NotAllowedError') {
          suggestions = '<br><small>建議：用戶拒絕了權限，請重新嘗試並允許螢幕分享</small>';
        } else if (error.name === 'NotSupportedError') {
          suggestions = '<br><small>建議：瀏覽器不支援，請使用 Chrome 88+ 或 Edge 88+</small>';
        } else if (error.message.includes('getDisplayMedia')) {
          suggestions = '<br><small>建議：1. 使用 Chrome/Edge 瀏覽器 2. 確保不是 file:// 協議 3. 嘗試文件上傳功能</small>';
        }
        
        document.getElementById('systemStatus').innerHTML = 
          `<div class="status error">❌ 系統音頻失敗：${errorMessage}${suggestions}</div>`;
      }
    }

    // 停止系統音頻
    function stopSystemAudio() {
      if (systemAudioStream) {
        systemAudioStream.getTracks().forEach(track => track.stop());
        systemAudioStream = null;
      }
      
      isRecording = false;
      
      document.getElementById('startSystem').disabled = false;
      document.getElementById('stopSystem').disabled = true;
      document.getElementById('startSystem').classList.remove('recording');
      
      document.getElementById('systemStatus').innerHTML = 
        '<div class="status info">⏹️ 系統音頻已停止</div>';
    }

    // 設置音頻處理
    function setupAudioProcessing(stream, sourceType) {
      audioContext = new AudioContext();
      const source = audioContext.createMediaStreamSource(stream);
      const processor = audioContext.createScriptProcessor(4096, 1, 1);

      processor.onaudioprocess = function(event) {
        if (!isRecording) return;
        
        const audioData = event.inputBuffer.getChannelData(0);
        processAudioChunk(audioData, sourceType);
      };

      source.connect(processor);
      processor.connect(audioContext.destination);
    }

    // 處理音頻片段
    function processAudioChunk(audioData, sourceType) {
      audioBuffer.push(...audioData);
      
      const now = Date.now();
      if (now - lastProcessTime > 3000 && audioBuffer.length > 0) {
        lastProcessTime = now;
        
        const audioBlob = createWavBlob(new Float32Array(audioBuffer), 44100);
        sendAudioForTranscription(audioBlob, sourceType);
        
        audioBuffer = audioBuffer.slice(-4096);
      }
    }

    // 創建 WAV Blob
    function createWavBlob(audioData, sampleRate) {
      const length = audioData.length;
      const buffer = new ArrayBuffer(44 + length * 2);
      const view = new DataView(buffer);
      
      const writeString = (offset, string) => {
        for (let i = 0; i < string.length; i++) {
          view.setUint8(offset + i, string.charCodeAt(i));
        }
      };
      
      writeString(0, 'RIFF');
      view.setUint32(4, 36 + length * 2, true);
      writeString(8, 'WAVE');
      writeString(12, 'fmt ');
      view.setUint32(16, 16, true);
      view.setUint16(20, 1, true);
      view.setUint16(22, 1, true);
      view.setUint32(24, sampleRate, true);
      view.setUint32(28, sampleRate * 2, true);
      view.setUint16(32, 2, true);
      view.setUint16(34, 16, true);
      writeString(36, 'data');
      view.setUint32(40, length * 2, true);
      
      let offset = 44;
      for (let i = 0; i < length; i++) {
        const sample = Math.max(-1, Math.min(1, audioData[i]));
        view.setInt16(offset, sample * 0x7FFF, true);
        offset += 2;
      }
      
      return new Blob([buffer], { type: 'audio/wav' });
    }

    // 發送音頻進行轉錄
    async function sendAudioForTranscription(audioBlob, sourceType) {
      try {
        const formData = new FormData();
        formData.append('audio_file', audioBlob, 'audio.wav');

        const response = await fetch('/api/transcribe', {
          method: 'POST',
          body: formData
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`);
        }

        const data = await response.json();
        
        if (data.transcription && data.transcription.trim()) {
          addTranscription(data.transcription, sourceType);
        }
      } catch (error) {
        console.error('轉錄錯誤:', error);
      }
    }

    // 添加轉錄結果
    function addTranscription(text, sourceType) {
      const display = document.getElementById('transcriptionDisplay');
      const timestamp = new Date().toLocaleTimeString();
      
      const sourceNames = {
        'microphone': '🎙️ 麥克風',
        'system': '🖥️ 系統音頻',
        'file': '📁 文件'
      };

      const item = document.createElement('div');
      item.className = `transcription-item ${sourceType}`;
      item.innerHTML = `
        <div class="timestamp">${timestamp}</div>
        <span class="source-tag ${sourceType}">${sourceNames[sourceType]}</span>
        <div class="text-content">${text}</div>
      `;

      display.appendChild(item);
      display.scrollTop = display.scrollHeight;
    }

    // 清空轉錄
    function clearTranscriptions() {
      document.getElementById('transcriptionDisplay').innerHTML = '';
    }

    // 匯出轉錄
    function exportTranscriptions() {
      const display = document.getElementById('transcriptionDisplay');
      const items = display.querySelectorAll('.transcription-item');

      let exportText = '會議轉錄記錄\n================\n\n';

      items.forEach(item => {
        const timestamp = item.querySelector('.timestamp').textContent;
        const source = item.querySelector('.source-tag').textContent;
        const textContent = item.querySelector('.text-content').textContent;
        exportText += `${timestamp} [${source}]\n${textContent}\n\n`;
      });

      const blob = new Blob([exportText], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `meeting_transcription_${new Date().toISOString().slice(0, 19)}.txt`;
      a.click();
      URL.revokeObjectURL(url);
    }

    // 複製全部文字
    function copyAllText() {
      const display = document.getElementById('transcriptionDisplay');
      const items = display.querySelectorAll('.transcription-item');

      let copyText = '';
      items.forEach(item => {
        const textContent = item.querySelector('.text-content').textContent;
        if (textContent) {
          copyText += textContent + '\n';
        }
      });

      navigator.clipboard.writeText(copyText).then(() => {
        alert('轉錄內容已複製到剪貼板');
      });
    }
  </script>
</body>
</html>
