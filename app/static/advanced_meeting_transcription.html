<!DOCTYPE html>
<html lang="zh-TW">
<head>
  <meta charset="UTF-8">
  <title>高級會議轉錄系統 (基於您的音頻捕獲技術)</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      max-width: 1400px;
      margin: 0 auto;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      color: white;
    }
    
    .container {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 20px;
      padding: 30px;
      color: #333;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }
    
    h1 {
      text-align: center;
      color: #2c3e50;
      margin-bottom: 30px;
      font-size: 2.5em;
    }
    
    .section {
      background: #f8f9fa;
      border-radius: 15px;
      padding: 25px;
      margin: 20px 0;
      border: 2px solid #e9ecef;
    }
    
    button {
      margin: 5px 10px 5px 0;
      padding: 12px 24px;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
      border: none;
      border-radius: 8px;
      transition: all 0.3s ease;
    }
    
    button.primary {
      background: linear-gradient(45deg, #28a745, #20c997);
      color: white;
    }
    
    button.danger {
      background: linear-gradient(45deg, #dc3545, #fd7e14);
      color: white;
    }
    
    button.secondary {
      background: linear-gradient(45deg, #6c757d, #495057);
      color: white;
    }
    
    button:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    
    button:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
      opacity: 0.6;
    }
    
    button.recording {
      animation: pulse 1.5s infinite;
    }
    
    @keyframes pulse {
      0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
      70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
      100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
    }
    
    select {
      margin: 5px 10px 5px 0;
      padding: 8px 12px;
      font-size: 16px;
      border: 2px solid #dee2e6;
      border-radius: 8px;
      background: white;
    }
    
    .status {
      padding: 15px;
      margin: 10px 0;
      border-radius: 8px;
      font-weight: bold;
    }
    
    .status.success {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    
    .status.error {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    
    .status.info {
      background: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }
    
    .status.warning {
      background: #fff3cd;
      color: #856404;
      border: 1px solid #ffeaa7;
    }
    
    .transcription-display {
      background: white;
      border: 1px solid #dee2e6;
      border-radius: 10px;
      padding: 20px;
      margin: 20px 0;
      max-height: 500px;
      overflow-y: auto;
    }
    
    .transcription-item {
      background: #f8f9fa;
      margin: 10px 0;
      padding: 15px;
      border-radius: 8px;
      border-left: 4px solid #007bff;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .transcription-item.mixed {
      border-left-color: #6f42c1;
    }
    
    .transcription-item.system {
      border-left-color: #28a745;
    }
    
    .transcription-item.microphone {
      border-left-color: #ffc107;
    }
    
    .timestamp {
      font-size: 12px;
      color: #6c757d;
      margin-bottom: 5px;
    }
    
    .source-tag {
      display: inline-block;
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: bold;
      margin-right: 10px;
    }
    
    .source-tag.mixed {
      background: #6f42c1;
      color: white;
    }
    
    .source-tag.system {
      background: #28a745;
      color: white;
    }
    
    .source-tag.microphone {
      background: #ffc107;
      color: #212529;
    }
    
    .text-content {
      font-size: 16px;
      line-height: 1.5;
      margin-top: 8px;
    }
    
    .warning-box {
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      padding: 15px;
      border-radius: 10px;
      margin: 15px 0;
      color: #856404;
    }
    
    .success-box {
      background: #d4edda;
      border: 1px solid #c3e6cb;
      padding: 15px;
      border-radius: 10px;
      margin: 15px 0;
      color: #155724;
    }
    
    .control-row {
      display: flex;
      align-items: center;
      margin: 15px 0;
      flex-wrap: wrap;
      gap: 15px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🎤 高級會議轉錄系統</h1>
    
    <!-- 使用說明 -->
    <div class="success-box">
      <h3>✅ 基於您的可靠音頻捕獲技術</h3>
      <p>此版本使用您提供的音頻捕獲方法，具有最佳的兼容性：</p>
      <ul>
        <li><strong>系統音頻 + 麥克風混合</strong>：同時捕獲會議聲音和您的發言</li>
        <li><strong>AudioContext 混合</strong>：使用您的音頻混合技術</li>
        <li><strong>實時轉錄</strong>：結合 OpenAI Whisper 高質量轉錄</li>
        <li><strong>支援所有會議平台</strong>：WebEx、Teams、Zoom 等</li>
      </ul>
    </div>
    
    <!-- 音頻品質設置 -->
    <div class="section">
      <h3>🎛️ 音頻設置</h3>
      <div class="control-row">
        <label>音頻品質：</label>
        <select id="audioQuality">
          <option value="192000,44100">192 kbps, 44.1 kHz (高品質)</option>
          <option value="128000,44100">128 kbps, 44.1 kHz (標準)</option>
          <option value="64000,22050">64 kbps, 22.05 kHz (節省頻寬)</option>
        </select>
      </div>
      
      <div class="control-row">
        <label>轉錄間隔：</label>
        <select id="transcriptionInterval">
          <option value="3000">3 秒 (推薦)</option>
          <option value="5000">5 秒</option>
          <option value="10000">10 秒</option>
        </select>
      </div>
    </div>
    
    <!-- 控制面板 -->
    <div class="section">
      <h3>🎯 會議音頻捕獲控制</h3>
      <div class="warning-box">
        <h4>📋 操作步驟</h4>
        <ol>
          <li>點擊「開始會議轉錄」</li>
          <li>在彈出對話框中選擇要分享的內容（螢幕或瀏覽器標籤）</li>
          <li><strong>重要：勾選「分享音頻」選項</strong></li>
          <li>允許麥克風權限（用於捕獲您的發言）</li>
          <li>開始會議，系統會自動轉錄所有音頻</li>
        </ol>
      </div>
      
      <div class="control-row">
        <button id="startMeetingCapture" class="primary">🎯 開始會議轉錄 (系統+麥克風)</button>
        <button id="stopMeetingCapture" class="danger" disabled>⏹️ 停止會議轉錄</button>
      </div>
      
      <div id="captureStatus" class="status info">點擊開始會議轉錄</div>
    </div>
    
    <!-- 轉錄結果 -->
    <div class="section">
      <h3>📝 實時轉錄結果</h3>
      <div class="control-row">
        <button onclick="clearTranscriptions()" class="secondary">🗑️ 清空記錄</button>
        <button onclick="exportTranscriptions()" class="secondary">💾 匯出記錄</button>
        <button onclick="copyAllText()" class="secondary">📋 複製全部</button>
        <button onclick="pauseTranscription()" id="pauseBtn" class="secondary">⏸️ 暫停轉錄</button>
      </div>
      
      <div class="transcription-display" id="transcriptionDisplay">
        <div class="transcription-item">
          <div class="timestamp">等待中...</div>
          <span class="source-tag mixed">會議音頻</span>
          <div class="text-content">轉錄結果將在此顯示，請開始會議音頻捕獲</div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 全局變量
    let mediaRecorder;
    let capturedStream; // 系統音頻流
    let micStream;      // 麥克風流
    let audioContext;
    let isRecording = false;
    let isPaused = false;
    let audioBuffer = [];
    let lastProcessTime = 0;
    let transcriptionInterval = 3000; // 默認 3 秒

    // 開始會議轉錄 (基於您的音頻捕獲技術)
    document.getElementById('startMeetingCapture').addEventListener('click', async () => {
      try {
        const statusDiv = document.getElementById('captureStatus');
        statusDiv.innerHTML = '<div class="status info">🔄 正在啟動音頻捕獲...</div>';
        
        // 獲取音頻設置
        const [audioBitRate, audioSampleRate] = document.getElementById('audioQuality').value.split(',').map(Number);
        transcriptionInterval = parseInt(document.getElementById('transcriptionInterval').value);

        // 1. 捕獲系統音頻（螢幕分享）- 使用您的方法
        capturedStream = await navigator.mediaDevices.getDisplayMedia({
          video: false, // 只要音頻
          audio: {
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false,
            sampleRate: audioSampleRate,
            channelCount: 2
          }
        });

        // 檢查是否成功捕獲音頻
        if (capturedStream.getAudioTracks().length === 0) {
          throw new Error('未捕獲到系統音頻軌道，請確保選擇了包含音頻的來源並勾選"分享音頻"');
        }

        // 2. 捕獲麥克風音頻 - 使用您的方法
        micStream = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false
          }
        });

        // 3. 建立 AudioContext 與混合 - 完全使用您的方法
        audioContext = new AudioContext();
        const destination = audioContext.createMediaStreamDestination();

        // 連接系統音頻
        if (capturedStream.getAudioTracks().length > 0) {
          const systemAudioSource = audioContext.createMediaStreamSource(capturedStream);
          systemAudioSource.connect(destination);
        }

        // 連接麥克風音頻
        if (micStream.getAudioTracks().length > 0) {
          const micAudioSource = audioContext.createMediaStreamSource(micStream);
          micAudioSource.connect(destination);
        }

        // 4. 設置實時音頻處理用於轉錄
        setupRealtimeTranscription(destination.stream);

        // 5. 更新 UI
        document.getElementById('startMeetingCapture').disabled = true;
        document.getElementById('stopMeetingCapture').disabled = false;
        document.getElementById('startMeetingCapture').classList.add('recording');
        
        statusDiv.innerHTML = '<div class="status success">🎵 會議音頻捕獲中 (系統+麥克風)</div>';
        
        isRecording = true;

        // 監聽流結束事件
        capturedStream.getTracks().forEach(track => {
          track.onended = () => {
            stopMeetingCapture();
          };
        });

      } catch (error) {
        console.error('會議音頻捕獲失敗:', error);
        document.getElementById('captureStatus').innerHTML = 
          `<div class="status error">❌ 捕獲失敗：${error.message}<br>
          <small>請確保：1. 選擇包含音頻的來源 2. 勾選"分享音頻" 3. 允許麥克風權限</small></div>`;
      }
    });

    // 停止會議轉錄
    document.getElementById('stopMeetingCapture').addEventListener('click', stopMeetingCapture);

    function stopMeetingCapture() {
      isRecording = false;
      
      // 停止所有音頻流
      if (capturedStream) {
        capturedStream.getTracks().forEach(track => track.stop());
        capturedStream = null;
      }
      
      if (micStream) {
        micStream.getTracks().forEach(track => track.stop());
        micStream = null;
      }
      
      if (audioContext) {
        audioContext.close();
        audioContext = null;
      }
      
      // 重置 UI
      document.getElementById('startMeetingCapture').disabled = false;
      document.getElementById('stopMeetingCapture').disabled = true;
      document.getElementById('startMeetingCapture').classList.remove('recording');
      
      document.getElementById('captureStatus').innerHTML = 
        '<div class="status info">⏹️ 會議轉錄已停止</div>';
    }

    // 設置實時轉錄
    function setupRealtimeTranscription(mixedStream) {
      const source = audioContext.createMediaStreamSource(mixedStream);
      const processor = audioContext.createScriptProcessor(4096, 1, 1);

      processor.onaudioprocess = function(event) {
        if (!isRecording || isPaused) return;
        
        const audioData = event.inputBuffer.getChannelData(0);
        processAudioForTranscription(audioData);
      };

      source.connect(processor);
      processor.connect(audioContext.destination);
    }

    // 處理音頻進行轉錄
    function processAudioForTranscription(audioData) {
      // 累積音頻數據
      audioBuffer.push(...audioData);
      
      // 根據設定的間隔進行轉錄
      const now = Date.now();
      if (now - lastProcessTime > transcriptionInterval && audioBuffer.length > 0) {
        lastProcessTime = now;
        
        // 轉換為 WAV 格式並發送轉錄
        const audioBlob = createWavBlob(new Float32Array(audioBuffer), 44100);
        sendAudioForTranscription(audioBlob, 'mixed');
        
        // 保留一些重疊以確保連續性
        const overlapSize = Math.floor(audioBuffer.length * 0.25);
        audioBuffer = audioBuffer.slice(-overlapSize);
      }
    }

    // 創建 WAV Blob
    function createWavBlob(audioData, sampleRate) {
      const length = audioData.length;
      const buffer = new ArrayBuffer(44 + length * 2);
      const view = new DataView(buffer);
      
      // WAV 文件頭
      const writeString = (offset, string) => {
        for (let i = 0; i < string.length; i++) {
          view.setUint8(offset + i, string.charCodeAt(i));
        }
      };
      
      writeString(0, 'RIFF');
      view.setUint32(4, 36 + length * 2, true);
      writeString(8, 'WAVE');
      writeString(12, 'fmt ');
      view.setUint32(16, 16, true);
      view.setUint16(20, 1, true);
      view.setUint16(22, 1, true);
      view.setUint32(24, sampleRate, true);
      view.setUint32(28, sampleRate * 2, true);
      view.setUint16(32, 2, true);
      view.setUint16(34, 16, true);
      writeString(36, 'data');
      view.setUint32(40, length * 2, true);
      
      // 音頻數據
      let offset = 44;
      for (let i = 0; i < length; i++) {
        const sample = Math.max(-1, Math.min(1, audioData[i]));
        view.setInt16(offset, sample * 0x7FFF, true);
        offset += 2;
      }
      
      return new Blob([buffer], { type: 'audio/wav' });
    }

    // 發送音頻進行轉錄
    async function sendAudioForTranscription(audioBlob, sourceType) {
      try {
        const formData = new FormData();
        formData.append('audio_file', audioBlob, 'meeting_audio.wav');

        const response = await fetch('/api/transcribe', {
          method: 'POST',
          body: formData
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`);
        }

        const data = await response.json();
        
        if (data.transcription && data.transcription.trim()) {
          addTranscription(data.transcription, sourceType);
        }
      } catch (error) {
        console.error('轉錄錯誤:', error);
      }
    }

    // 添加轉錄結果
    function addTranscription(text, sourceType) {
      const display = document.getElementById('transcriptionDisplay');
      const timestamp = new Date().toLocaleTimeString();
      
      const sourceNames = {
        'mixed': '🎯 會議音頻',
        'system': '🖥️ 系統音頻',
        'microphone': '🎙️ 麥克風'
      };

      const item = document.createElement('div');
      item.className = `transcription-item ${sourceType}`;
      item.innerHTML = `
        <div class="timestamp">${timestamp}</div>
        <span class="source-tag ${sourceType}">${sourceNames[sourceType]}</span>
        <div class="text-content">${text}</div>
      `;

      display.appendChild(item);
      display.scrollTop = display.scrollHeight;
    }

    // 暫停/恢復轉錄
    function pauseTranscription() {
      const pauseBtn = document.getElementById('pauseBtn');
      isPaused = !isPaused;
      
      if (isPaused) {
        pauseBtn.textContent = '▶️ 恢復轉錄';
        pauseBtn.classList.add('warning');
      } else {
        pauseBtn.textContent = '⏸️ 暫停轉錄';
        pauseBtn.classList.remove('warning');
      }
    }

    // 清空轉錄記錄
    function clearTranscriptions() {
      document.getElementById('transcriptionDisplay').innerHTML = '';
    }

    // 匯出轉錄記錄
    function exportTranscriptions() {
      const display = document.getElementById('transcriptionDisplay');
      const items = display.querySelectorAll('.transcription-item');

      let exportText = '會議轉錄記錄\n';
      exportText += '================\n';
      exportText += `匯出時間: ${new Date().toLocaleString()}\n\n`;

      items.forEach(item => {
        const timestamp = item.querySelector('.timestamp').textContent;
        const source = item.querySelector('.source-tag').textContent;
        const textContent = item.querySelector('.text-content').textContent;
        exportText += `${timestamp} [${source}]\n${textContent}\n\n`;
      });

      const blob = new Blob([exportText], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `meeting_transcription_${new Date().toISOString().slice(0, 19)}.txt`;
      a.click();
      URL.revokeObjectURL(url);
    }

    // 複製全部文字
    function copyAllText() {
      const display = document.getElementById('transcriptionDisplay');
      const items = display.querySelectorAll('.transcription-item');

      let copyText = '';
      items.forEach(item => {
        const textContent = item.querySelector('.text-content').textContent;
        if (textContent) {
          copyText += textContent + '\n';
        }
      });

      navigator.clipboard.writeText(copyText).then(() => {
        alert('轉錄內容已複製到剪貼板');
      });
    }
  </script>
</body>
</html>
