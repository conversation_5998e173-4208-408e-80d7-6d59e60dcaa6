<!DOCTYPE html>
<html lang="zh-TW">
<head>
  <meta charset="UTF-8">
  <title>智能語音分段轉錄系統 (靜音檢測)</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      color: white;
    }
    
    .container {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 20px;
      padding: 30px;
      color: #333;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }
    
    h1 {
      text-align: center;
      color: #2c3e50;
      margin-bottom: 30px;
    }
    
    .section {
      background: #f8f9fa;
      border-radius: 15px;
      padding: 25px;
      margin: 20px 0;
      border: 2px solid #e9ecef;
    }
    
    button {
      margin: 5px 10px 5px 0;
      padding: 12px 24px;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
      border: none;
      border-radius: 8px;
      transition: all 0.3s ease;
    }
    
    button.primary {
      background: linear-gradient(45deg, #28a745, #20c997);
      color: white;
    }
    
    button.danger {
      background: linear-gradient(45deg, #dc3545, #fd7e14);
      color: white;
    }
    
    button.secondary {
      background: linear-gradient(45deg, #6c757d, #495057);
      color: white;
    }
    
    button:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    
    button:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
      opacity: 0.6;
    }
    
    button.recording {
      animation: pulse 1.5s infinite;
    }
    
    @keyframes pulse {
      0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
      70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
      100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
    }
    
    select, input[type="range"] {
      margin: 5px 10px 5px 0;
      padding: 8px 12px;
      font-size: 16px;
      border: 2px solid #dee2e6;
      border-radius: 8px;
      background: white;
    }
    
    .status {
      padding: 15px;
      margin: 10px 0;
      border-radius: 8px;
      font-weight: bold;
    }
    
    .status.success {
      background: #d4edda;
      color: #155724;
    }
    
    .status.error {
      background: #f8d7da;
      color: #721c24;
    }
    
    .status.info {
      background: #d1ecf1;
      color: #0c5460;
    }
    
    .transcription-display {
      background: white;
      border: 1px solid #dee2e6;
      border-radius: 10px;
      padding: 20px;
      margin: 20px 0;
      max-height: 500px;
      overflow-y: auto;
    }
    
    .transcription-item {
      background: #f8f9fa;
      margin: 10px 0;
      padding: 15px;
      border-radius: 8px;
      border-left: 4px solid #6f42c1;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      transition: all 0.3s ease;
    }
    
    .transcription-item.processing {
      border-left-color: #ffc107;
      background: #fff8e1;
    }
    
    .transcription-item.complete {
      border-left-color: #28a745;
    }
    
    .timestamp {
      font-size: 12px;
      color: #6c757d;
      margin-bottom: 5px;
    }
    
    .text-content {
      font-size: 16px;
      line-height: 1.5;
      margin-top: 8px;
    }
    
    .success-box {
      background: #d4edda;
      border: 1px solid #c3e6cb;
      padding: 15px;
      border-radius: 10px;
      margin: 15px 0;
      color: #155724;
    }
    
    .control-row {
      display: flex;
      align-items: center;
      margin: 15px 0;
      flex-wrap: wrap;
      gap: 15px;
    }
    
    .audio-visualizer {
      width: 100%;
      height: 60px;
      background: #2c3e50;
      border-radius: 8px;
      margin: 10px 0;
      position: relative;
      overflow: hidden;
    }
    
    .volume-bar {
      height: 100%;
      background: linear-gradient(90deg, #28a745, #ffc107, #dc3545);
      width: 0%;
      transition: width 0.1s ease;
      border-radius: 8px;
    }
    
    .silence-indicator {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: white;
      font-size: 12px;
      font-weight: bold;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🎤 智能語音分段轉錄系統</h1>
    
    <!-- 說明 -->
    <div class="success-box">
      <h3>🧠 智能靜音檢測轉錄</h3>
      <p>此版本使用音頻靜音檢測來智能分段，避免句子截斷：</p>
      <ul>
        <li><strong>靜音檢測</strong>：自動檢測語音停頓進行分段</li>
        <li><strong>完整句子</strong>：避免固定時間截斷造成的語意不順</li>
        <li><strong>可調參數</strong>：靜音閾值和持續時間可調整</li>
        <li><strong>實時視覺化</strong>：顯示音量和靜音狀態</li>
      </ul>
    </div>
    
    <!-- 設置 -->
    <div class="section">
      <h3>🎛️ 智能分段設置</h3>
      
      <div class="control-row">
        <label>音頻品質：</label>
        <select id="audioQuality">
          <option value="192000,44100">192 kbps, 44.1 kHz</option>
          <option value="128000,44100">128 kbps, 44.1 kHz</option>
          <option value="64000,22050">64 kbps, 22.05 kHz</option>
        </select>
      </div>
      
      <div class="control-row">
        <label>靜音閾值：</label>
        <input type="range" id="silenceThreshold" min="0.001" max="0.1" step="0.001" value="0.01">
        <span id="thresholdValue">0.01</span>
      </div>
      
      <div class="control-row">
        <label>靜音持續時間：</label>
        <input type="range" id="silenceDuration" min="500" max="3000" step="100" value="1000">
        <span id="durationValue">1000ms</span>
      </div>
      
      <div class="control-row">
        <label>最小音頻長度：</label>
        <input type="range" id="minAudioLength" min="2" max="10" step="1" value="3">
        <span id="lengthValue">3秒</span>
      </div>
      
      <div class="control-row">
        <label>最大音頻長度：</label>
        <input type="range" id="maxAudioLength" min="10" max="60" step="5" value="30">
        <span id="maxLengthValue">30秒</span>
      </div>
    </div>
    
    <!-- 音頻視覺化 -->
    <div class="section">
      <h3>🔊 音頻監控</h3>
      <div class="audio-visualizer">
        <div class="volume-bar" id="volumeBar"></div>
        <div class="silence-indicator" id="silenceIndicator">等待音頻...</div>
      </div>
      <div id="audioStats" class="status info">音頻統計：等待開始</div>
    </div>
    
    <!-- 控制 -->
    <div class="section">
      <h3>🎯 智能轉錄控制</h3>
      
      <button id="startCapture" class="primary">🎯 開始智能轉錄</button>
      <button id="stopCapture" class="danger" disabled>⏹️ 停止轉錄</button>
      
      <div id="captureStatus" class="status info">點擊開始智能轉錄</div>
    </div>
    
    <!-- 轉錄結果 -->
    <div class="section">
      <h3>📝 轉錄結果</h3>
      <button onclick="clearTranscriptions()" class="secondary">🗑️ 清空</button>
      <button onclick="exportTranscriptions()" class="secondary">💾 匯出</button>
      <button onclick="copyAllText()" class="secondary">📋 複製</button>
      
      <div class="transcription-display" id="transcriptionDisplay">
        <div class="transcription-item">
          <div class="timestamp">等待中...</div>
          <div class="text-content">智能分段轉錄結果將在此顯示</div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 全局變量
    let capturedStream;
    let micStream;
    let audioContext;
    let isRecording = false;
    let audioBuffer = [];
    let silenceStartTime = null;
    let lastSoundTime = Date.now();
    let currentSegmentId = 0;
    
    // 智能分段參數
    let silenceThreshold = 0.01;
    let silenceDuration = 1000; // ms
    let minAudioLength = 3; // seconds
    let maxAudioLength = 30; // seconds
    
    // 音頻統計
    let audioStats = {
      totalSegments: 0,
      avgSegmentLength: 0,
      silenceDetections: 0
    };

    const startCaptureButton = document.getElementById('startCapture');
    const stopCaptureButton = document.getElementById('stopCapture');
    const audioQualitySelect = document.getElementById('audioQuality');

    // 參數控制
    document.getElementById('silenceThreshold').addEventListener('input', (e) => {
      silenceThreshold = parseFloat(e.target.value);
      document.getElementById('thresholdValue').textContent = silenceThreshold.toFixed(3);
    });

    document.getElementById('silenceDuration').addEventListener('input', (e) => {
      silenceDuration = parseInt(e.target.value);
      document.getElementById('durationValue').textContent = silenceDuration + 'ms';
    });

    document.getElementById('minAudioLength').addEventListener('input', (e) => {
      minAudioLength = parseInt(e.target.value);
      document.getElementById('lengthValue').textContent = minAudioLength + '秒';
    });

    document.getElementById('maxAudioLength').addEventListener('input', (e) => {
      maxAudioLength = parseInt(e.target.value);
      document.getElementById('maxLengthValue').textContent = maxAudioLength + '秒';
    });

    // 開始智能轉錄
    startCaptureButton.addEventListener('click', async () => {
      try {
        const statusDiv = document.getElementById('captureStatus');
        statusDiv.innerHTML = '<div class="status info">🔄 正在啟動智能轉錄...</div>';
        
        const [audioBitRate, audioSampleRate] = audioQualitySelect.value.split(',').map(Number);

        // 捕捉螢幕影音（含系統聲音）
        capturedStream = await navigator.mediaDevices.getDisplayMedia({
          video: {
            width: { ideal: 1280 },
            height: { ideal: 720 },
            frameRate: { ideal: 30 }
          },
          audio: {
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false,
            sampleRate: audioSampleRate,
            channelCount: 2
          }
        });

        // 捕捉本地麥克風音訊
        micStream = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false
          }
        });

        // 建立 AudioContext 與混合目的地
        audioContext = new AudioContext();
        const destination = audioContext.createMediaStreamDestination();

        if (capturedStream.getAudioTracks().length > 0) {
          const systemAudioSource = audioContext.createMediaStreamSource(capturedStream);
          systemAudioSource.connect(destination);
        }
        if (micStream.getAudioTracks().length > 0) {
          const micAudioSource = audioContext.createMediaStreamSource(micStream);
          micAudioSource.connect(destination);
        }

        // 設置智能音頻處理
        setupSmartAudioProcessing(destination.stream);

        startCaptureButton.disabled = true;
        stopCaptureButton.disabled = false;
        startCaptureButton.classList.add('recording');
        
        statusDiv.innerHTML = '<div class="status success">🧠 智能轉錄中 (靜音檢測)</div>';
        isRecording = true;

        // 監聽流結束
        capturedStream.getTracks().forEach(track => {
          track.onended = () => {
            stopCapture();
          };
        });

      } catch (err) {
        console.error('智能轉錄啟動失敗:', err);
        document.getElementById('captureStatus').innerHTML = 
          `<div class="status error">❌ 啟動失敗：${err.message}</div>`;
      }
    });

    // 停止轉錄
    stopCaptureButton.addEventListener('click', stopCapture);

    function stopCapture() {
      isRecording = false;
      
      // 處理剩餘音頻
      if (audioBuffer.length > 0) {
        processRemainingAudio();
      }
      
      if (capturedStream) {
        capturedStream.getTracks().forEach(track => track.stop());
        capturedStream = null;
      }
      if (micStream) {
        micStream.getTracks().forEach(track => track.stop());
        micStream = null;
      }
      if (audioContext) {
        audioContext.close();
        audioContext = null;
      }
      
      startCaptureButton.disabled = false;
      stopCaptureButton.disabled = true;
      startCaptureButton.classList.remove('recording');
      
      document.getElementById('captureStatus').innerHTML = 
        '<div class="status info">⏹️ 智能轉錄已停止</div>';
      
      updateAudioStats();
    }

    // 設置智能音頻處理
    function setupSmartAudioProcessing(mixedStream) {
      const source = audioContext.createMediaStreamSource(mixedStream);
      const processor = audioContext.createScriptProcessor(4096, 1, 1);

      processor.onaudioprocess = function(event) {
        if (!isRecording) return;
        
        const audioData = event.inputBuffer.getChannelData(0);
        processSmartAudioSegmentation(audioData);
      };

      source.connect(processor);
      processor.connect(audioContext.destination);
    }

    // 智能音頻分段處理
    function processSmartAudioSegmentation(audioData) {
      // 計算音量 (RMS)
      let sum = 0;
      for (let i = 0; i < audioData.length; i++) {
        sum += audioData[i] * audioData[i];
      }
      const rms = Math.sqrt(sum / audioData.length);
      
      // 更新視覺化
      updateAudioVisualization(rms);
      
      // 添加到緩衝區
      audioBuffer.push(...audioData);
      
      const now = Date.now();
      const bufferDurationMs = (audioBuffer.length / 44100) * 1000;
      
      // 檢測靜音
      if (rms < silenceThreshold) {
        if (silenceStartTime === null) {
          silenceStartTime = now;
        }
        
        const silenceDurationMs = now - silenceStartTime;
        
        // 如果靜音持續足夠長時間且音頻長度合適
        if (silenceDurationMs >= silenceDuration && 
            bufferDurationMs >= minAudioLength * 1000) {
          
          processAudioSegment();
          audioStats.silenceDetections++;
        }
      } else {
        silenceStartTime = null;
        lastSoundTime = now;
      }
      
      // 強制分段（避免過長）
      if (bufferDurationMs >= maxAudioLength * 1000) {
        processAudioSegment();
      }
    }

    // 處理音頻段
    function processAudioSegment() {
      if (audioBuffer.length === 0) return;
      
      const segmentId = ++currentSegmentId;
      const audioBlob = createWavBlob(new Float32Array(audioBuffer), 44100);
      const duration = (audioBuffer.length / 44100).toFixed(1);
      
      // 添加處理中的轉錄項目
      addProcessingTranscription(segmentId, duration);
      
      // 發送轉錄
      sendAudioForTranscription(audioBlob, segmentId);
      
      // 清空緩衝區
      audioBuffer = [];
      audioStats.totalSegments++;
      
      console.log(`處理音頻段 ${segmentId}，時長: ${duration}秒`);
    }

    // 處理剩餘音頻
    function processRemainingAudio() {
      if (audioBuffer.length > 0) {
        const duration = (audioBuffer.length / 44100).toFixed(1);
        if (parseFloat(duration) >= 1.0) { // 至少1秒
          processAudioSegment();
        }
      }
    }

    // 更新音頻視覺化
    function updateAudioVisualization(rms) {
      const volumeBar = document.getElementById('volumeBar');
      const silenceIndicator = document.getElementById('silenceIndicator');
      
      const volumePercent = Math.min(rms * 1000, 100);
      volumeBar.style.width = volumePercent + '%';
      
      if (rms < silenceThreshold) {
        silenceIndicator.textContent = '🔇 靜音檢測中...';
        silenceIndicator.style.color = '#ffc107';
      } else {
        silenceIndicator.textContent = '🔊 音頻活躍';
        silenceIndicator.style.color = '#28a745';
      }
    }

    // 更新音頻統計
    function updateAudioStats() {
      const statsDiv = document.getElementById('audioStats');
      statsDiv.innerHTML = `
        <div class="status info">
          📊 統計：總段數 ${audioStats.totalSegments} | 靜音檢測 ${audioStats.silenceDetections} 次
        </div>
      `;
    }

    // 創建 WAV Blob
    function createWavBlob(audioData, sampleRate) {
      const length = audioData.length;
      const buffer = new ArrayBuffer(44 + length * 2);
      const view = new DataView(buffer);
      
      const writeString = (offset, string) => {
        for (let i = 0; i < string.length; i++) {
          view.setUint8(offset + i, string.charCodeAt(i));
        }
      };
      
      writeString(0, 'RIFF');
      view.setUint32(4, 36 + length * 2, true);
      writeString(8, 'WAVE');
      writeString(12, 'fmt ');
      view.setUint32(16, 16, true);
      view.setUint16(20, 1, true);
      view.setUint16(22, 1, true);
      view.setUint32(24, sampleRate, true);
      view.setUint32(28, sampleRate * 2, true);
      view.setUint16(32, 2, true);
      view.setUint16(34, 16, true);
      writeString(36, 'data');
      view.setUint32(40, length * 2, true);
      
      let offset = 44;
      for (let i = 0; i < length; i++) {
        const sample = Math.max(-1, Math.min(1, audioData[i]));
        view.setInt16(offset, sample * 0x7FFF, true);
        offset += 2;
      }
      
      return new Blob([buffer], { type: 'audio/wav' });
    }

    // 添加處理中的轉錄項目
    function addProcessingTranscription(segmentId, duration) {
      const display = document.getElementById('transcriptionDisplay');
      const timestamp = new Date().toLocaleTimeString();

      const item = document.createElement('div');
      item.className = 'transcription-item processing';
      item.id = `segment-${segmentId}`;
      item.innerHTML = `
        <div class="timestamp">${timestamp} - 段落 ${segmentId} (${duration}秒)</div>
        <div class="text-content">🔄 正在轉錄中...</div>
      `;

      display.appendChild(item);
      display.scrollTop = display.scrollHeight;
    }

    // 發送音頻進行轉錄
    async function sendAudioForTranscription(audioBlob, segmentId) {
      try {
        const formData = new FormData();
        formData.append('audio_file', audioBlob, `segment_${segmentId}.wav`);

        const response = await fetch('/api/transcribe', {
          method: 'POST',
          body: formData
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`);
        }

        const data = await response.json();
        
        // 更新轉錄結果
        updateTranscriptionResult(segmentId, data.transcription || '(無語音內容)');
        
      } catch (error) {
        console.error('轉錄錯誤:', error);
        updateTranscriptionResult(segmentId, `❌ 轉錄失敗: ${error.message}`);
      }
    }

    // 更新轉錄結果
    function updateTranscriptionResult(segmentId, transcription) {
      const item = document.getElementById(`segment-${segmentId}`);
      if (item) {
        const textContent = item.querySelector('.text-content');
        textContent.textContent = transcription;
        item.classList.remove('processing');
        item.classList.add('complete');
      }
    }

    // 清空轉錄
    function clearTranscriptions() {
      document.getElementById('transcriptionDisplay').innerHTML = '';
      audioStats = { totalSegments: 0, avgSegmentLength: 0, silenceDetections: 0 };
      currentSegmentId = 0;
    }

    // 匯出轉錄
    function exportTranscriptions() {
      const display = document.getElementById('transcriptionDisplay');
      const items = display.querySelectorAll('.transcription-item.complete');

      let exportText = '智能分段轉錄記錄\n================\n\n';

      items.forEach(item => {
        const timestamp = item.querySelector('.timestamp').textContent;
        const textContent = item.querySelector('.text-content').textContent;
        exportText += `${timestamp}\n${textContent}\n\n`;
      });

      const blob = new Blob([exportText], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `smart_transcription_${new Date().toISOString().slice(0, 19)}.txt`;
      a.click();
      URL.revokeObjectURL(url);
    }

    // 複製全部文字
    function copyAllText() {
      const display = document.getElementById('transcriptionDisplay');
      const items = display.querySelectorAll('.transcription-item.complete');

      let copyText = '';
      items.forEach(item => {
        const textContent = item.querySelector('.text-content').textContent;
        if (textContent && !textContent.includes('轉錄失敗')) {
          copyText += textContent + '\n';
        }
      });

      navigator.clipboard.writeText(copyText).then(() => {
        alert('轉錄內容已複製到剪貼板');
      });
    }
  </script>
</body>
</html>
