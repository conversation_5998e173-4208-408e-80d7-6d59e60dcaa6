<!DOCTYPE html>
<html lang="zh-TW">
<head>
  <meta charset="UTF-8">
  <title>綜合智能轉錄系統 (自適應+可調整)</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      max-width: 1400px;
      margin: 0 auto;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      color: white;
    }
    
    .container {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 20px;
      padding: 30px;
      color: #333;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }
    
    h1 {
      text-align: center;
      color: #2c3e50;
      margin-bottom: 30px;
      font-size: 2.2em;
    }
    
    .section {
      background: #f8f9fa;
      border-radius: 15px;
      padding: 25px;
      margin: 20px 0;
      border: 2px solid #e9ecef;
    }
    
    button {
      margin: 5px 10px 5px 0;
      padding: 12px 24px;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
      border: none;
      border-radius: 8px;
      transition: all 0.3s ease;
    }
    
    button.primary {
      background: linear-gradient(45deg, #28a745, #20c997);
      color: white;
    }
    
    button.danger {
      background: linear-gradient(45deg, #dc3545, #fd7e14);
      color: white;
    }
    
    button.secondary {
      background: linear-gradient(45deg, #6c757d, #495057);
      color: white;
    }
    
    button.preset {
      background: linear-gradient(45deg, #007bff, #6610f2);
      color: white;
      margin: 5px;
      padding: 8px 16px;
      font-size: 14px;
    }
    
    button:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    
    button:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
      opacity: 0.6;
    }
    
    button.recording {
      animation: pulse 1.5s infinite;
    }
    
    @keyframes pulse {
      0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
      70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
      100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
    }
    
    select, input[type="range"] {
      margin: 5px 10px 5px 0;
      padding: 8px 12px;
      font-size: 16px;
      border: 2px solid #dee2e6;
      border-radius: 8px;
      background: white;
    }
    
    input[type="range"] {
      width: 200px;
    }
    
    .status {
      padding: 15px;
      margin: 10px 0;
      border-radius: 8px;
      font-weight: bold;
    }
    
    .status.success {
      background: #d4edda;
      color: #155724;
    }
    
    .status.error {
      background: #f8d7da;
      color: #721c24;
    }
    
    .status.info {
      background: #d1ecf1;
      color: #0c5460;
    }
    
    .status.warning {
      background: #fff3cd;
      color: #856404;
    }
    
    .transcription-display {
      background: white;
      border: 1px solid #dee2e6;
      border-radius: 10px;
      padding: 20px;
      margin: 20px 0;
      max-height: 500px;
      overflow-y: auto;
    }
    
    .transcription-item {
      background: #f8f9fa;
      margin: 10px 0;
      padding: 15px;
      border-radius: 8px;
      border-left: 4px solid #28a745;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      transition: all 0.3s ease;
    }
    
    .transcription-item.processing {
      border-left-color: #ffc107;
      background: #fff8e1;
    }
    
    .transcription-item.filtered {
      border-left-color: #dc3545;
      background: #ffe6e6;
      opacity: 0.7;
    }
    
    .timestamp {
      font-size: 12px;
      color: #6c757d;
      margin-bottom: 5px;
    }
    
    .text-content {
      font-size: 16px;
      line-height: 1.5;
      margin-top: 8px;
    }
    
    .success-box {
      background: #d4edda;
      border: 1px solid #c3e6cb;
      padding: 15px;
      border-radius: 10px;
      margin: 15px 0;
      color: #155724;
    }
    
    .adaptive-panel {
      background: #e3f2fd;
      border: 2px solid #2196f3;
      border-radius: 15px;
      padding: 20px;
      margin: 20px 0;
    }
    
    .adjustment-panel {
      background: #fff3cd;
      border: 2px solid #ffc107;
      border-radius: 15px;
      padding: 20px;
      margin: 20px 0;
    }
    
    .panel-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 10px 0;
      padding: 10px;
      background: white;
      border-radius: 8px;
    }
    
    .adjustment-row {
      display: flex;
      align-items: center;
      margin: 15px 0;
      flex-wrap: wrap;
      gap: 15px;
    }
    
    .adjustment-row label {
      min-width: 120px;
      font-weight: bold;
    }
    
    .value-display {
      background: white;
      padding: 5px 10px;
      border-radius: 5px;
      border: 1px solid #ddd;
      min-width: 80px;
      text-align: center;
      font-weight: bold;
    }
    
    .preset-buttons {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin: 15px 0;
    }
    
    .audio-visualizer {
      width: 100%;
      height: 120px;
      background: #2c3e50;
      border-radius: 8px;
      margin: 10px 0;
      position: relative;
      overflow: hidden;
    }
    
    .volume-bar {
      height: 40%;
      background: linear-gradient(90deg, #28a745, #ffc107, #dc3545);
      width: 0%;
      transition: width 0.1s ease;
      border-radius: 8px;
      margin-top: 30%;
    }
    
    .threshold-indicators {
      position: absolute;
      top: 0;
      bottom: 0;
      width: 100%;
    }
    
    .threshold-line {
      position: absolute;
      top: 0;
      bottom: 0;
      width: 2px;
      background: #ffc107;
    }
    
    .threshold-label {
      position: absolute;
      top: 5px;
      font-size: 10px;
      color: white;
      background: rgba(0,0,0,0.5);
      padding: 2px 4px;
      border-radius: 3px;
    }
    
    .adaptive-info {
      position: absolute;
      top: 10px;
      left: 10px;
      color: white;
      font-size: 12px;
      font-weight: bold;
    }
    
    .stats-info {
      position: absolute;
      bottom: 10px;
      left: 10px;
      color: white;
      font-size: 12px;
    }
    
    .quality-info {
      position: absolute;
      top: 10px;
      right: 10px;
      color: white;
      font-size: 12px;
      font-weight: bold;
    }
    
    .mode-indicator {
      position: absolute;
      bottom: 10px;
      right: 10px;
      color: white;
      font-size: 12px;
      font-weight: bold;
      background: rgba(0,0,0,0.5);
      padding: 2px 6px;
      border-radius: 3px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🧠🎛️ 綜合智能轉錄系統</h1>
    
    <!-- 說明 -->
    <div class="success-box">
      <h3>🚀 自適應 + 可調整雙重智能</h3>
      <p>結合兩大系統優點，提供最佳轉錄體驗：</p>
      <ul>
        <li><strong>自適應學習</strong>：自動分析環境並優化參數</li>
        <li><strong>手動微調</strong>：提供完整的參數調整控制</li>
        <li><strong>智能過濾</strong>：多重條件過濾無效轉錄</li>
        <li><strong>實時監控</strong>：視覺化顯示所有檢測狀態</li>
      </ul>
    </div>
    
    <!-- 自適應狀態面板 -->
    <div class="adaptive-panel">
      <h3>🧠 自適應學習狀態</h3>
      <div class="panel-row">
        <span>環境類型：</span>
        <span id="environmentType">檢測中...</span>
      </div>
      <div class="panel-row">
        <span>學習狀態：</span>
        <span id="learningStatus">初始化中...</span>
      </div>
      <div class="panel-row">
        <span>動態優化：</span>
        <span id="dynamicOptimization">準備中...</span>
      </div>
      <div class="panel-row">
        <span>過濾效率：</span>
        <span id="filterEfficiency">0% (0/0)</span>
      </div>
    </div>
    
    <!-- 手動調整面板 -->
    <div class="adjustment-panel">
      <h3>🎚️ 手動參數調整</h3>
      
      <!-- 預設模式 -->
      <div class="adjustment-row">
        <label>快速設定：</label>
        <div class="preset-buttons">
          <button class="preset" onclick="applyPreset('auto')">🤖 自動模式</button>
          <button class="preset" onclick="applyPreset('relaxed')">🟢 寬鬆模式</button>
          <button class="preset" onclick="applyPreset('balanced')">🟡 平衡模式</button>
          <button class="preset" onclick="applyPreset('strict')">🔴 嚴格模式</button>
        </div>
      </div>
      
      <!-- 音量閾值 -->
      <div class="adjustment-row">
        <label>音量閾值：</label>
        <input type="range" id="volumeThreshold" min="0.001" max="0.05" step="0.001" value="0.008">
        <div class="value-display" id="volumeValue">0.008</div>
        <small>越小越敏感</small>
      </div>
      
      <!-- 最小語音時長 -->
      <div class="adjustment-row">
        <label>最小語音時長：</label>
        <input type="range" id="minVoiceDuration" min="300" max="2000" step="100" value="800">
        <div class="value-display" id="durationValue">800ms</div>
        <small>最短語音長度</small>
      </div>
      
      <!-- 靜音緩衝時間 -->
      <div class="adjustment-row">
        <label>靜音緩衝時間：</label>
        <input type="range" id="silenceBuffer" min="500" max="3000" step="100" value="1200">
        <div class="value-display" id="bufferValue">1200ms</div>
        <small>語音結束等待時間</small>
      </div>
      
      <!-- 質量閾值 -->
      <div class="adjustment-row">
        <label>質量閾值：</label>
        <input type="range" id="qualityThreshold" min="0.1" max="0.8" step="0.05" value="0.25">
        <div class="value-display" id="qualityValue">0.25</div>
        <small>越小越寬鬆</small>
      </div>
      
      <!-- 自適應強度 -->
      <div class="adjustment-row">
        <label>自適應強度：</label>
        <input type="range" id="adaptiveStrength" min="0" max="1" step="0.1" value="0.7">
        <div class="value-display" id="adaptiveValue">0.7</div>
        <small>自動調整程度</small>
      </div>
    </div>
    
    <!-- 實時監控 -->
    <div class="section">
      <h3>📊 綜合監控面板</h3>
      <div class="audio-visualizer">
        <div class="threshold-indicators" id="thresholdIndicators"></div>
        <div class="volume-bar" id="volumeBar"></div>
        <div class="adaptive-info" id="adaptiveInfo">自適應：待機</div>
        <div class="stats-info" id="statsInfo">統計：0 接受 / 0 過濾</div>
        <div class="quality-info" id="qualityInfo">質量：--</div>
        <div class="mode-indicator" id="modeIndicator">模式：平衡</div>
      </div>
      
      <div id="monitorStatus" class="status info">系統準備就緒，可開始轉錄</div>
    </div>
    
    <!-- 控制 -->
    <div class="section">
      <h3>🎯 綜合轉錄控制</h3>
      
      <label for="audioQuality">音頻品質：</label>
      <select id="audioQuality">
        <option value="192000,44100">192 kbps, 44.1 kHz (推薦)</option>
        <option value="128000,44100">128 kbps, 44.1 kHz</option>
        <option value="64000,22050">64 kbps, 22.05 kHz</option>
      </select>
      <br><br>
      
      <button id="startCapture" class="primary">🧠🎛️ 開始綜合智能轉錄</button>
      <button id="stopCapture" class="danger" disabled>⏹️ 停止轉錄</button>
      <button id="toggleFiltering" class="secondary">🔍 切換過濾模式</button>
      <button id="resetSettings" class="secondary" onclick="resetToDefaults()">🔄 重置設定</button>
      
      <div id="captureStatus" class="status info">點擊開始綜合智能轉錄</div>
    </div>
    
    <!-- 轉錄結果 -->
    <div class="section">
      <h3>📝 智能轉錄結果</h3>
      <button onclick="clearTranscriptions()" class="secondary">🗑️ 清空</button>
      <button onclick="exportTranscriptions()" class="secondary">💾 匯出</button>
      <button onclick="copyValidText()" class="secondary">📋 複製有效內容</button>
      <button onclick="toggleFiltered()" class="secondary" id="toggleFilteredBtn">👁️ 顯示已過濾</button>
      <button onclick="showStats()" class="secondary">📊 顯示統計</button>
      
      <div class="transcription-display" id="transcriptionDisplay">
        <div class="transcription-item">
          <div class="timestamp">等待中...</div>
          <div class="text-content">綜合智能轉錄系統已就緒，結合自適應學習和手動調整功能</div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 全局變量
    let capturedStream, micStream, audioContext;
    let isRecording = false;
    let audioBuffer = [];
    let currentSegmentId = 0;
    let showFiltered = false;
    let enableFiltering = true;
    let currentMode = 'balanced';
    
    // 綜合檢測參數 (結合自適應和可調整)
    let comprehensiveParams = {
      // 基礎參數
      volumeThreshold: 0.008,
      minVoiceDuration: 800,
      silenceBuffer: 1200,
      qualityThreshold: 0.25,
      adaptiveStrength: 0.7,
      
      // 自適應參數
      environmentType: 'unknown',
      learningPhase: 'initializing',
      volumeHistory: [],
      adaptiveAdjustments: 0,
      
      // 語音檢測狀態
      voiceStartTime: null,
      lastVoiceTime: null,
      isVoiceActive: false
    };
    
    // 統計數據
    let stats = {
      totalSegments: 0,
      validSegments: 0,
      filteredSegments: 0,
      adaptiveAdjustments: 0,
      manualAdjustments: 0
    };

    // 預設模式配置
    const presets = {
      auto: {
        volumeThreshold: 0.01,
        minVoiceDuration: 800,
        silenceBuffer: 1200,
        qualityThreshold: 0.3,
        adaptiveStrength: 1.0
      },
      relaxed: {
        volumeThreshold: 0.005,
        minVoiceDuration: 500,
        silenceBuffer: 1000,
        qualityThreshold: 0.15,
        adaptiveStrength: 0.5
      },
      balanced: {
        volumeThreshold: 0.008,
        minVoiceDuration: 800,
        silenceBuffer: 1200,
        qualityThreshold: 0.25,
        adaptiveStrength: 0.7
      },
      strict: {
        volumeThreshold: 0.015,
        minVoiceDuration: 1200,
        silenceBuffer: 1800,
        qualityThreshold: 0.4,
        adaptiveStrength: 0.3
      }
    };

    // 初始化
    window.onload = function() {
      setupControllers();
      applyPreset('balanced');
      updateThresholdIndicators();
      startAdaptiveLearning();
    };

    // 設置控制器
    function setupControllers() {
      document.getElementById('volumeThreshold').addEventListener('input', updateParams);
      document.getElementById('minVoiceDuration').addEventListener('input', updateParams);
      document.getElementById('silenceBuffer').addEventListener('input', updateParams);
      document.getElementById('qualityThreshold').addEventListener('input', updateParams);
      document.getElementById('adaptiveStrength').addEventListener('input', updateParams);
      
      // 切換過濾模式
      document.getElementById('toggleFiltering').addEventListener('click', () => {
        enableFiltering = !enableFiltering;
        const btn = document.getElementById('toggleFiltering');
        btn.textContent = enableFiltering ? '🔍 過濾模式：開啟' : '🔍 過濾模式：關閉';
        btn.className = enableFiltering ? 'secondary' : 'warning';
      });
    }

    // 更新參數
    function updateParams() {
      comprehensiveParams.volumeThreshold = parseFloat(document.getElementById('volumeThreshold').value);
      comprehensiveParams.minVoiceDuration = parseInt(document.getElementById('minVoiceDuration').value);
      comprehensiveParams.silenceBuffer = parseInt(document.getElementById('silenceBuffer').value);
      comprehensiveParams.qualityThreshold = parseFloat(document.getElementById('qualityThreshold').value);
      comprehensiveParams.adaptiveStrength = parseFloat(document.getElementById('adaptiveStrength').value);
      
      stats.manualAdjustments++;
      updateValueDisplays();
      updateThresholdIndicators();
    }

    // 更新數值顯示
    function updateValueDisplays() {
      document.getElementById('volumeValue').textContent = comprehensiveParams.volumeThreshold.toFixed(3);
      document.getElementById('durationValue').textContent = comprehensiveParams.minVoiceDuration + 'ms';
      document.getElementById('bufferValue').textContent = comprehensiveParams.silenceBuffer + 'ms';
      document.getElementById('qualityValue').textContent = comprehensiveParams.qualityThreshold.toFixed(2);
      document.getElementById('adaptiveValue').textContent = comprehensiveParams.adaptiveStrength.toFixed(1);
    }

    // 應用預設模式
    function applyPreset(presetName) {
      const preset = presets[presetName];
      currentMode = presetName;
      
      document.getElementById('volumeThreshold').value = preset.volumeThreshold;
      document.getElementById('minVoiceDuration').value = preset.minVoiceDuration;
      document.getElementById('silenceBuffer').value = preset.silenceBuffer;
      document.getElementById('qualityThreshold').value = preset.qualityThreshold;
      document.getElementById('adaptiveStrength').value = preset.adaptiveStrength;
      
      updateParams();
      
      const modeNames = {
        auto: '自動模式 - 完全自適應',
        relaxed: '寬鬆模式 - 捕獲更多語音',
        balanced: '平衡模式 - 推薦設定',
        strict: '嚴格模式 - 高質量過濾'
      };
      
      document.getElementById('monitorStatus').innerHTML = 
        `<div class="status info">✅ 已套用${modeNames[presetName]}</div>`;
      
      document.getElementById('modeIndicator').textContent = `模式：${presetName}`;
    }

    // 重置到默認設定
    function resetToDefaults() {
      applyPreset('balanced');
      stats.manualAdjustments = 0;
    }

    // 更新閾值指示器
    function updateThresholdIndicators() {
      const indicators = document.getElementById('thresholdIndicators');
      const volumePercent = Math.min(comprehensiveParams.volumeThreshold * 2000, 100);
      
      indicators.innerHTML = `
        <div class="threshold-line" style="left: ${volumePercent}%"></div>
        <div class="threshold-label" style="left: ${volumePercent}%">閾值</div>
      `;
    }

    // 開始自適應學習
    function startAdaptiveLearning() {
      setInterval(() => {
        if (isRecording) {
          performAdaptiveLearning();
          updateAdaptiveStatus();
        }
      }, 2000);
    }

    // 執行自適應學習
    function performAdaptiveLearning() {
      if (comprehensiveParams.volumeHistory.length < 50) return;
      
      const recent = comprehensiveParams.volumeHistory.slice(-50);
      const avg = recent.reduce((a, b) => a + b, 0) / recent.length;
      const std = Math.sqrt(recent.reduce((a, b) => a + (b - avg) ** 2, 0) / recent.length);
      
      // 環境類型檢測
      if (avg < 0.01 && std < 0.005) {
        comprehensiveParams.environmentType = '安靜環境';
      } else if (avg > 0.05 || std > 0.02) {
        comprehensiveParams.environmentType = '嘈雜環境';
      } else {
        comprehensiveParams.environmentType = '一般環境';
      }
      
      // 自適應調整 (根據adaptiveStrength強度)
      if (comprehensiveParams.adaptiveStrength > 0.5) {
        const adaptiveThreshold = Math.max(0.005, avg + std * 1.5);
        const currentThreshold = parseFloat(document.getElementById('volumeThreshold').value);
        const newThreshold = currentThreshold * (1 - comprehensiveParams.adaptiveStrength * 0.1) + 
                           adaptiveThreshold * (comprehensiveParams.adaptiveStrength * 0.1);
        
        if (Math.abs(newThreshold - currentThreshold) > 0.001) {
          document.getElementById('volumeThreshold').value = newThreshold.toFixed(3);
          updateParams();
          stats.adaptiveAdjustments++;
        }
      }
      
      comprehensiveParams.learningPhase = 'optimizing';
    }

    // 更新自適應狀態
    function updateAdaptiveStatus() {
      document.getElementById('environmentType').textContent = comprehensiveParams.environmentType;
      document.getElementById('learningStatus').textContent = comprehensiveParams.learningPhase;
      
      const optimization = stats.adaptiveAdjustments > 0 ? 
        `已優化 ${stats.adaptiveAdjustments} 次` : '監控中';
      document.getElementById('dynamicOptimization').textContent = optimization;
      
      const efficiency = stats.totalSegments > 0 ? 
        ((stats.filteredSegments / stats.totalSegments) * 100).toFixed(1) : 0;
      document.getElementById('filterEfficiency').textContent = 
        `${efficiency}% (${stats.filteredSegments}/${stats.totalSegments})`;
    }

    const startCaptureButton = document.getElementById('startCapture');
    const stopCaptureButton = document.getElementById('stopCapture');
    const audioQualitySelect = document.getElementById('audioQuality');

    // 開始綜合智能轉錄
    startCaptureButton.addEventListener('click', async () => {
      try {
        const statusDiv = document.getElementById('captureStatus');
        statusDiv.innerHTML = '<div class="status info">🔄 啟動綜合智能轉錄...</div>';
        
        const [audioBitRate, audioSampleRate] = audioQualitySelect.value.split(',').map(Number);

        // 捕捉音頻
        capturedStream = await navigator.mediaDevices.getDisplayMedia({
          video: { width: { ideal: 1280 }, height: { ideal: 720 }, frameRate: { ideal: 30 } },
          audio: {
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false,
            sampleRate: audioSampleRate,
            channelCount: 2
          }
        });

        micStream = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false
          }
        });

        // 建立音頻混合
        audioContext = new AudioContext();
        const destination = audioContext.createMediaStreamDestination();

        if (capturedStream.getAudioTracks().length > 0) {
          const systemAudioSource = audioContext.createMediaStreamSource(capturedStream);
          systemAudioSource.connect(destination);
        }
        if (micStream.getAudioTracks().length > 0) {
          const micAudioSource = audioContext.createMediaStreamSource(micStream);
          micAudioSource.connect(destination);
        }

        // 設置綜合檢測處理
        setupComprehensiveProcessing(destination.stream);

        startCaptureButton.disabled = true;
        stopCaptureButton.disabled = false;
        startCaptureButton.classList.add('recording');
        
        statusDiv.innerHTML = '<div class="status success">🧠🎛️ 綜合智能轉錄中</div>';
        isRecording = true;

        // 開始狀態更新
        startStatusUpdate();

        capturedStream.getTracks().forEach(track => {
          track.onended = () => stopCapture();
        });

      } catch (err) {
        console.error('綜合智能轉錄啟動失敗:', err);
        document.getElementById('captureStatus').innerHTML = 
          `<div class="status error">❌ 啟動失敗：${err.message}</div>`;
      }
    });

    // 停止轉錄
    stopCaptureButton.addEventListener('click', stopCapture);

    function stopCapture() {
      isRecording = false;
      
      // 處理剩餘音頻
      if (audioBuffer.length > 0 && comprehensiveParams.isVoiceActive) {
        processVoiceSegment();
      }
      
      [capturedStream, micStream].forEach(stream => {
        if (stream) {
          stream.getTracks().forEach(track => track.stop());
        }
      });
      
      if (audioContext) {
        audioContext.close();
        audioContext = null;
      }
      
      startCaptureButton.disabled = false;
      stopCaptureButton.disabled = true;
      startCaptureButton.classList.remove('recording');
      
      document.getElementById('captureStatus').innerHTML = 
        '<div class="status info">⏹️ 綜合智能轉錄已停止</div>';
    }

    // 設置綜合處理
    function setupComprehensiveProcessing(mixedStream) {
      const source = audioContext.createMediaStreamSource(mixedStream);
      const processor = audioContext.createScriptProcessor(4096, 1, 1);

      processor.onaudioprocess = function(event) {
        if (!isRecording) return;
        
        const audioData = event.inputBuffer.getChannelData(0);
        processComprehensiveDetection(audioData);
      };

      source.connect(processor);
      processor.connect(audioContext.destination);
    }

    // 綜合檢測處理
    function processComprehensiveDetection(audioData) {
      const now = Date.now();
      
      // 音頻分析
      const analysis = analyzeAudioSegment(audioData);
      
      // 更新音量歷史 (用於自適應學習)
      comprehensiveParams.volumeHistory.push(analysis.rms);
      if (comprehensiveParams.volumeHistory.length > 100) {
        comprehensiveParams.volumeHistory.shift();
      }
      
      // 更新視覺化
      updateComprehensiveVisualization(analysis);
      
      // 綜合語音活動檢測
      const isVoiceDetected = detectComprehensiveVoiceActivity(analysis);
      
      if (isVoiceDetected) {
        if (!comprehensiveParams.isVoiceActive) {
          comprehensiveParams.voiceStartTime = now;
          comprehensiveParams.isVoiceActive = true;
        }
        comprehensiveParams.lastVoiceTime = now;
        audioBuffer.push(...audioData);
        
      } else if (comprehensiveParams.isVoiceActive) {
        const silenceDuration = now - comprehensiveParams.lastVoiceTime;
        
        if (silenceDuration >= comprehensiveParams.silenceBuffer) {
          const voiceDuration = now - comprehensiveParams.voiceStartTime;
          
          if (voiceDuration >= comprehensiveParams.minVoiceDuration && audioBuffer.length > 0) {
            processVoiceSegment();
          } else {
            stats.filteredSegments++;
            addFilteredTranscription(++currentSegmentId, (voiceDuration/1000).toFixed(1), '語音時長過短');
          }
          
          comprehensiveParams.isVoiceActive = false;
          comprehensiveParams.voiceStartTime = null;
          audioBuffer = [];
        } else {
          audioBuffer.push(...audioData);
        }
      }
    }

    // 分析音頻片段
    function analyzeAudioSegment(audioData) {
      let sum = 0;
      let energy = 0;
      let zeroCrossings = 0;
      
      for (let i = 0; i < audioData.length; i++) {
        const sample = audioData[i];
        sum += sample * sample;
        energy += Math.abs(sample);
        
        if (i > 0 && audioData[i] * audioData[i-1] < 0) {
          zeroCrossings++;
        }
      }
      
      const rms = Math.sqrt(sum / audioData.length);
      const avgEnergy = energy / audioData.length;
      const zcr = zeroCrossings / audioData.length;
      
      return { rms, energy: avgEnergy, zcr, length: audioData.length };
    }

    // 綜合語音活動檢測
    function detectComprehensiveVoiceActivity(analysis) {
      // 多重檢測條件
      const conditions = [
        analysis.rms > comprehensiveParams.volumeThreshold,
        analysis.energy > comprehensiveParams.volumeThreshold * 0.5,
        analysis.zcr > 0.01 && analysis.zcr < 0.2,
        analysis.rms > 0.002
      ];
      
      const passedConditions = conditions.filter(c => c).length;
      return passedConditions >= 2; // 至少滿足2個條件
    }

    // 處理語音段
    function processVoiceSegment() {
      if (audioBuffer.length === 0) return;
      
      const segmentId = ++currentSegmentId;
      const duration = (audioBuffer.length / 44100).toFixed(1);
      
      // 質量檢查
      const finalAnalysis = analyzeAudioSegment(new Float32Array(audioBuffer));
      const qualityScore = calculateQualityScore(finalAnalysis);
      
      // 智能內容過濾
      if (enableFiltering && qualityScore < comprehensiveParams.qualityThreshold) {
        stats.filteredSegments++;
        addFilteredTranscription(segmentId, duration, `質量過低 (${(qualityScore*100).toFixed(0)}%)`);
      } else {
        const audioBlob = createWavBlob(new Float32Array(audioBuffer), 44100);
        addProcessingTranscription(segmentId, duration, qualityScore);
        sendAudioForTranscription(audioBlob, segmentId);
        stats.validSegments++;
      }
      
      stats.totalSegments++;
      audioBuffer = [];
    }

    // 計算質量評分
    function calculateQualityScore(analysis) {
      let score = 0;
      if (analysis.rms > 0.005) score += 0.4;
      if (analysis.energy > 0.002) score += 0.3;
      if (analysis.zcr > 0.01 && analysis.zcr < 0.15) score += 0.3;
      return Math.min(score, 1.0);
    }

    // 開始狀態更新
    function startStatusUpdate() {
      setInterval(() => {
        if (isRecording) {
          updateComprehensiveStatus();
        }
      }, 1000);
    }

    // 更新綜合狀態
    function updateComprehensiveStatus() {
      const acceptanceRate = stats.totalSegments > 0 ? 
        ((stats.validSegments / stats.totalSegments) * 100).toFixed(1) : 0;
      
      document.getElementById('statsInfo').textContent = 
        `統計：${stats.validSegments} 接受 / ${stats.filteredSegments} 過濾 (${acceptanceRate}%)`;
    }

    // 更新綜合視覺化
    function updateComprehensiveVisualization(analysis) {
      const volumeBar = document.getElementById('volumeBar');
      const adaptiveInfo = document.getElementById('adaptiveInfo');
      const qualityInfo = document.getElementById('qualityInfo');
      
      const volumePercent = Math.min(analysis.rms * 2000, 100);
      volumeBar.style.width = volumePercent + '%';
      
      const isActive = comprehensiveParams.isVoiceActive;
      adaptiveInfo.textContent = `自適應：${isActive ? '語音活躍' : comprehensiveParams.environmentType}`;
      adaptiveInfo.style.color = isActive ? '#28a745' : '#ffc107';
      
      const quality = calculateQualityScore(analysis);
      qualityInfo.textContent = `質量：${(quality * 100).toFixed(0)}%`;
      qualityInfo.style.color = quality > comprehensiveParams.qualityThreshold ? '#28a745' : '#dc3545';
    }

    // 創建 WAV Blob
    function createWavBlob(audioData, sampleRate) {
      const length = audioData.length;
      const buffer = new ArrayBuffer(44 + length * 2);
      const view = new DataView(buffer);
      
      const writeString = (offset, string) => {
        for (let i = 0; i < string.length; i++) {
          view.setUint8(offset + i, string.charCodeAt(i));
        }
      };
      
      writeString(0, 'RIFF');
      view.setUint32(4, 36 + length * 2, true);
      writeString(8, 'WAVE');
      writeString(12, 'fmt ');
      view.setUint32(16, 16, true);
      view.setUint16(20, 1, true);
      view.setUint16(22, 1, true);
      view.setUint32(24, sampleRate, true);
      view.setUint32(28, sampleRate * 2, true);
      view.setUint16(32, 2, true);
      view.setUint16(34, 16, true);
      writeString(36, 'data');
      view.setUint32(40, length * 2, true);
      
      let offset = 44;
      for (let i = 0; i < length; i++) {
        const sample = Math.max(-1, Math.min(1, audioData[i]));
        view.setInt16(offset, sample * 0x7FFF, true);
        offset += 2;
      }
      
      return new Blob([buffer], { type: 'audio/wav' });
    }

    // 添加處理中轉錄
    function addProcessingTranscription(segmentId, duration, qualityScore) {
      const display = document.getElementById('transcriptionDisplay');
      const timestamp = new Date().toLocaleTimeString();

      const item = document.createElement('div');
      item.className = 'transcription-item processing';
      item.id = `segment-${segmentId}`;
      item.innerHTML = `
        <div class="timestamp">${timestamp} - 語音段 ${segmentId} (${duration}秒) [質量: ${(qualityScore * 100).toFixed(0)}%]</div>
        <div class="text-content">🔄 智能轉錄中...</div>
      `;

      display.appendChild(item);
      display.scrollTop = display.scrollHeight;
    }

    // 添加已過濾轉錄
    function addFilteredTranscription(segmentId, duration, reason) {
      const display = document.getElementById('transcriptionDisplay');
      const timestamp = new Date().toLocaleTimeString();

      const item = document.createElement('div');
      item.className = 'transcription-item filtered';
      item.id = `segment-${segmentId}`;
      item.style.display = showFiltered ? 'block' : 'none';
      item.innerHTML = `
        <div class="timestamp">${timestamp} - 過濾段 ${segmentId} (${duration}秒)</div>
        <div class="text-content">🚫 已過濾：${reason}</div>
      `;

      display.appendChild(item);
      display.scrollTop = display.scrollHeight;
    }

    // 發送轉錄
    async function sendAudioForTranscription(audioBlob, segmentId) {
      try {
        const formData = new FormData();
        formData.append('audio_file', audioBlob, `comprehensive_${segmentId}.wav`);

        const response = await fetch('/api/transcribe', {
          method: 'POST',
          body: formData
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`);
        }

        const data = await response.json();
        
        // 智能內容過濾
        const filteredResult = intelligentContentFilter(data.transcription || '');
        updateTranscriptionResult(segmentId, filteredResult.text, filteredResult.isValid);
        
      } catch (error) {
        console.error('轉錄錯誤:', error);
        updateTranscriptionResult(segmentId, `❌ 轉錄失敗: ${error.message}`, false);
      }
    }

    // 智能內容過濾
    function intelligentContentFilter(text) {
      if (!text || text.trim().length === 0) {
        return { text: '🔇 無語音內容', isValid: false };
      }
      
      const cleanText = text.trim();
      
      // 過濾條件
      const filters = [
        { test: t => t.length < 3, reason: '內容過短' },
        { test: t => /^[^\w\u4e00-\u9fff]*$/.test(t), reason: '無有效文字' },
        { test: t => /^(呃|嗯|啊|哦|唔){2,}$/.test(t), reason: '僅含語助詞' },
        { test: t => t.split('').every(c => c === t[0]), reason: '重複字符' },
        { test: t => /^(.+?)\1{3,}/.test(t), reason: '重複內容' }
      ];
      
      for (const filter of filters) {
        if (enableFiltering && filter.test(cleanText)) {
          stats.filteredSegments++;
          return { text: `🚫 已過濾：${filter.reason}`, isValid: false };
        }
      }
      
      return { text: cleanText, isValid: true };
    }

    // 更新轉錄結果
    function updateTranscriptionResult(segmentId, transcription, isValid) {
      const item = document.getElementById(`segment-${segmentId}`);
      if (item) {
        const textContent = item.querySelector('.text-content');
        textContent.textContent = transcription;
        item.classList.remove('processing');
        
        if (!isValid) {
          item.classList.add('filtered');
          item.style.display = showFiltered ? 'block' : 'none';
        }
      }
    }

    // 切換顯示已過濾項目
    function toggleFiltered() {
      showFiltered = !showFiltered;
      const filteredItems = document.querySelectorAll('.transcription-item.filtered');
      const btn = document.getElementById('toggleFilteredBtn');
      
      filteredItems.forEach(item => {
        item.style.display = showFiltered ? 'block' : 'none';
      });
      
      btn.textContent = showFiltered ? '👁️ 隱藏已過濾' : '👁️ 顯示已過濾';
    }

    // 顯示統計
    function showStats() {
      const totalTime = stats.totalSegments * 3; // 估算
      const efficiency = stats.totalSegments > 0 ? 
        ((stats.validSegments / stats.totalSegments) * 100).toFixed(1) : 0;
      
      alert(`📊 轉錄統計報告
      
總處理段數：${stats.totalSegments}
有效轉錄：${stats.validSegments}
過濾段數：${stats.filteredSegments}
轉錄效率：${efficiency}%

自適應調整：${stats.adaptiveAdjustments} 次
手動調整：${stats.manualAdjustments} 次
環境類型：${comprehensiveParams.environmentType}
當前模式：${currentMode}`);
    }

    // 清空轉錄
    function clearTranscriptions() {
      document.getElementById('transcriptionDisplay').innerHTML = '';
      stats = { totalSegments: 0, validSegments: 0, filteredSegments: 0, adaptiveAdjustments: 0, manualAdjustments: 0 };
      currentSegmentId = 0;
    }

    // 匯出轉錄
    function exportTranscriptions() {
      const display = document.getElementById('transcriptionDisplay');
      const validItems = display.querySelectorAll('.transcription-item:not(.filtered):not(.processing)');

      let exportText = '綜合智能轉錄記錄\n================\n\n';
      exportText += `環境類型：${comprehensiveParams.environmentType}\n`;
      exportText += `轉錄模式：${currentMode}\n`;
      exportText += `轉錄效率：${stats.totalSegments > 0 ? ((stats.validSegments / stats.totalSegments) * 100).toFixed(1) : 0}%\n\n`;

      validItems.forEach(item => {
        const timestamp = item.querySelector('.timestamp').textContent;
        const textContent = item.querySelector('.text-content').textContent;
        if (!textContent.includes('轉錄失敗') && !textContent.includes('已過濾')) {
          exportText += `${timestamp}\n${textContent}\n\n`;
        }
      });

      const blob = new Blob([exportText], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `comprehensive_transcription_${new Date().toISOString().slice(0, 19)}.txt`;
      a.click();
      URL.revokeObjectURL(url);
    }

    // 複製有效內容
    function copyValidText() {
      const display = document.getElementById('transcriptionDisplay');
      const validItems = display.querySelectorAll('.transcription-item:not(.filtered):not(.processing)');

      let copyText = '';
      validItems.forEach(item => {
        const textContent = item.querySelector('.text-content').textContent;
        if (!textContent.includes('轉錄失敗') && !textContent.includes('已過濾') && 
            !textContent.includes('轉錄中')) {
          copyText += textContent + '\n';
        }
      });

      navigator.clipboard.writeText(copyText).then(() => {
        alert(`已複製 ${copyText.split('\n').filter(line => line.trim()).length} 條有效轉錄內容`);
      });
    }
  </script>
</body>
</html>
