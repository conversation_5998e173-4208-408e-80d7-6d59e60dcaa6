<!DOCTYPE html>
<html lang="zh-TW">
<head>
  <meta charset="UTF-8">
  <title>靜音感知轉錄系統 (嚴格靜音檢測)</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      color: white;
    }
    
    .container {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 20px;
      padding: 30px;
      color: #333;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }
    
    h1 {
      text-align: center;
      color: #2c3e50;
      margin-bottom: 30px;
    }
    
    .section {
      background: #f8f9fa;
      border-radius: 15px;
      padding: 25px;
      margin: 20px 0;
      border: 2px solid #e9ecef;
    }
    
    button {
      margin: 5px 10px 5px 0;
      padding: 12px 24px;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
      border: none;
      border-radius: 8px;
      transition: all 0.3s ease;
    }
    
    button.primary {
      background: linear-gradient(45deg, #28a745, #20c997);
      color: white;
    }
    
    button.danger {
      background: linear-gradient(45deg, #dc3545, #fd7e14);
      color: white;
    }
    
    button.secondary {
      background: linear-gradient(45deg, #6c757d, #495057);
      color: white;
    }
    
    button:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    
    button:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
      opacity: 0.6;
    }
    
    button.recording {
      animation: pulse 1.5s infinite;
    }
    
    @keyframes pulse {
      0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
      70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
      100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
    }
    
    select {
      margin: 5px 10px 5px 0;
      padding: 8px 12px;
      font-size: 16px;
      border: 2px solid #dee2e6;
      border-radius: 8px;
      background: white;
    }
    
    .status {
      padding: 15px;
      margin: 10px 0;
      border-radius: 8px;
      font-weight: bold;
    }
    
    .status.success {
      background: #d4edda;
      color: #155724;
    }
    
    .status.error {
      background: #f8d7da;
      color: #721c24;
    }
    
    .status.info {
      background: #d1ecf1;
      color: #0c5460;
    }
    
    .status.warning {
      background: #fff3cd;
      color: #856404;
    }
    
    .transcription-display {
      background: white;
      border: 1px solid #dee2e6;
      border-radius: 10px;
      padding: 20px;
      margin: 20px 0;
      max-height: 500px;
      overflow-y: auto;
    }
    
    .transcription-item {
      background: #f8f9fa;
      margin: 10px 0;
      padding: 15px;
      border-radius: 8px;
      border-left: 4px solid #28a745;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      transition: all 0.3s ease;
    }
    
    .transcription-item.processing {
      border-left-color: #ffc107;
      background: #fff8e1;
    }
    
    .transcription-item.rejected {
      border-left-color: #dc3545;
      background: #ffe6e6;
      opacity: 0.7;
    }
    
    .timestamp {
      font-size: 12px;
      color: #6c757d;
      margin-bottom: 5px;
    }
    
    .text-content {
      font-size: 16px;
      line-height: 1.5;
      margin-top: 8px;
    }
    
    .success-box {
      background: #d4edda;
      border: 1px solid #c3e6cb;
      padding: 15px;
      border-radius: 10px;
      margin: 15px 0;
      color: #155724;
    }
    
    .detection-panel {
      background: #e8f5e8;
      border: 2px solid #28a745;
      border-radius: 15px;
      padding: 20px;
      margin: 20px 0;
    }
    
    .detection-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 10px 0;
      padding: 10px;
      background: white;
      border-radius: 8px;
    }
    
    .audio-visualizer {
      width: 100%;
      height: 100px;
      background: #2c3e50;
      border-radius: 8px;
      margin: 10px 0;
      position: relative;
      overflow: hidden;
    }
    
    .volume-bar {
      height: 50%;
      background: linear-gradient(90deg, #28a745, #ffc107, #dc3545);
      width: 0%;
      transition: width 0.1s ease;
      border-radius: 8px;
      margin-top: 25%;
    }
    
    .detection-info {
      position: absolute;
      top: 10px;
      left: 10px;
      color: white;
      font-size: 12px;
      font-weight: bold;
    }
    
    .silence-status {
      position: absolute;
      bottom: 10px;
      left: 10px;
      color: white;
      font-size: 12px;
    }
    
    .quality-score {
      position: absolute;
      top: 10px;
      right: 10px;
      color: white;
      font-size: 12px;
      font-weight: bold;
    }
    
    .threshold-line {
      position: absolute;
      top: 0;
      bottom: 0;
      width: 2px;
      background: #ffc107;
      left: 5%;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🔇 靜音感知轉錄系統</h1>
    
    <!-- 說明 -->
    <div class="success-box">
      <h3>🎯 嚴格靜音檢測</h3>
      <p>此系統使用多重檢測機制，確保只在有真實語音時才進行轉錄：</p>
      <ul>
        <li><strong>多重音頻分析</strong>：音量、頻譜、能量密度綜合評估</li>
        <li><strong>嚴格靜音檢測</strong>：多層過濾避免無聲音頻轉錄</li>
        <li><strong>語音活動檢測</strong>：VAD (Voice Activity Detection) 算法</li>
        <li><strong>智能拒絕機制</strong>：自動拒絕低質量音頻片段</li>
      </ul>
    </div>
    
    <!-- 檢測狀態面板 -->
    <div class="detection-panel">
      <h3>🔍 語音檢測狀態</h3>
      <div class="detection-item">
        <span>當前狀態：</span>
        <span id="voiceStatus">待機中</span>
      </div>
      <div class="detection-item">
        <span>音量閾值：</span>
        <span id="volumeThreshold">自動調整中</span>
      </div>
      <div class="detection-item">
        <span>語音活動：</span>
        <span id="voiceActivity">無活動</span>
      </div>
      <div class="detection-item">
        <span>拒絕統計：</span>
        <span id="rejectionStats">0 拒絕 / 0 總計</span>
      </div>
    </div>
    
    <!-- 音頻監控 -->
    <div class="section">
      <h3>🔊 實時語音監控</h3>
      <div class="audio-visualizer">
        <div class="threshold-line" id="thresholdLine"></div>
        <div class="volume-bar" id="volumeBar"></div>
        <div class="detection-info" id="detectionInfo">語音檢測：待機</div>
        <div class="silence-status" id="silenceStatus">靜音狀態：準備中</div>
        <div class="quality-score" id="qualityScore">質量：--</div>
      </div>
    </div>
    
    <!-- 控制 -->
    <div class="section">
      <h3>🎯 靜音感知轉錄控制</h3>
      
      <label for="audioQuality">音頻品質：</label>
      <select id="audioQuality">
        <option value="192000,44100">192 kbps, 44.1 kHz (推薦)</option>
        <option value="128000,44100">128 kbps, 44.1 kHz</option>
        <option value="64000,22050">64 kbps, 22.05 kHz</option>
      </select>
      <br><br>
      
      <button id="startCapture" class="primary">🔇 開始靜音感知轉錄</button>
      <button id="stopCapture" class="danger" disabled>⏹️ 停止轉錄</button>
      
      <div id="captureStatus" class="status info">點擊開始靜音感知轉錄</div>
    </div>
    
    <!-- 轉錄結果 -->
    <div class="section">
      <h3>📝 語音轉錄結果</h3>
      <button onclick="clearTranscriptions()" class="secondary">🗑️ 清空</button>
      <button onclick="exportTranscriptions()" class="secondary">💾 匯出</button>
      <button onclick="copyValidText()" class="secondary">📋 複製有效內容</button>
      <button onclick="toggleRejected()" class="secondary" id="toggleRejectedBtn">👁️ 顯示拒絕項目</button>
      
      <div class="transcription-display" id="transcriptionDisplay">
        <div class="transcription-item">
          <div class="timestamp">等待中...</div>
          <div class="text-content">只有檢測到真實語音時才會顯示轉錄結果</div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 全局變量
    let capturedStream, micStream, audioContext;
    let isRecording = false;
    let audioBuffer = [];
    let currentSegmentId = 0;
    let showRejected = false;
    
    // 語音檢測參數
    let voiceDetection = {
      volumeThreshold: 0.02,        // 音量閾值
      energyThreshold: 0.001,       // 能量閾值
      spectralThreshold: 0.01,      // 頻譜閾值
      minVoiceDuration: 1000,       // 最小語音持續時間 (ms)
      silenceBuffer: 2000,          // 靜音緩衝時間 (ms)
      voiceStartTime: null,
      lastVoiceTime: null,
      volumeHistory: [],
      isVoiceActive: false
    };
    
    // 統計數據
    let stats = {
      totalSegments: 0,
      validSegments: 0,
      rejectedSegments: 0,
      silenceRejections: 0,
      qualityRejections: 0
    };

    const startCaptureButton = document.getElementById('startCapture');
    const stopCaptureButton = document.getElementById('stopCapture');
    const audioQualitySelect = document.getElementById('audioQuality');

    // 開始靜音感知轉錄
    startCaptureButton.addEventListener('click', async () => {
      try {
        const statusDiv = document.getElementById('captureStatus');
        statusDiv.innerHTML = '<div class="status info">🔄 啟動靜音感知轉錄...</div>';
        
        const [audioBitRate, audioSampleRate] = audioQualitySelect.value.split(',').map(Number);

        // 捕捉音頻
        capturedStream = await navigator.mediaDevices.getDisplayMedia({
          video: { width: { ideal: 1280 }, height: { ideal: 720 }, frameRate: { ideal: 30 } },
          audio: {
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false,
            sampleRate: audioSampleRate,
            channelCount: 2
          }
        });

        micStream = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false
          }
        });

        // 建立音頻混合
        audioContext = new AudioContext();
        const destination = audioContext.createMediaStreamDestination();

        if (capturedStream.getAudioTracks().length > 0) {
          const systemAudioSource = audioContext.createMediaStreamSource(capturedStream);
          systemAudioSource.connect(destination);
        }
        if (micStream.getAudioTracks().length > 0) {
          const micAudioSource = audioContext.createMediaStreamSource(micStream);
          micAudioSource.connect(destination);
        }

        // 設置語音檢測處理
        setupVoiceDetectionProcessing(destination.stream);

        startCaptureButton.disabled = true;
        stopCaptureButton.disabled = false;
        startCaptureButton.classList.add('recording');
        
        statusDiv.innerHTML = '<div class="status success">🔇 靜音感知轉錄中 (嚴格檢測)</div>';
        isRecording = true;

        // 開始檢測狀態更新
        startDetectionStatusUpdate();

        capturedStream.getTracks().forEach(track => {
          track.onended = () => stopCapture();
        });

      } catch (err) {
        console.error('靜音感知轉錄啟動失敗:', err);
        document.getElementById('captureStatus').innerHTML = 
          `<div class="status error">❌ 啟動失敗：${err.message}</div>`;
      }
    });

    // 停止轉錄
    stopCaptureButton.addEventListener('click', stopCapture);

    function stopCapture() {
      isRecording = false;
      
      // 處理剩餘音頻
      if (audioBuffer.length > 0 && voiceDetection.isVoiceActive) {
        processVoiceSegment();
      }
      
      [capturedStream, micStream].forEach(stream => {
        if (stream) {
          stream.getTracks().forEach(track => track.stop());
        }
      });
      
      if (audioContext) {
        audioContext.close();
        audioContext = null;
      }
      
      startCaptureButton.disabled = false;
      stopCaptureButton.disabled = true;
      startCaptureButton.classList.remove('recording');
      
      document.getElementById('captureStatus').innerHTML = 
        '<div class="status info">⏹️ 靜音感知轉錄已停止</div>';
      
      updateDetectionStatus();
    }

    // 設置語音檢測處理
    function setupVoiceDetectionProcessing(mixedStream) {
      const source = audioContext.createMediaStreamSource(mixedStream);
      const processor = audioContext.createScriptProcessor(4096, 1, 1);

      processor.onaudioprocess = function(event) {
        if (!isRecording) return;
        
        const audioData = event.inputBuffer.getChannelData(0);
        processVoiceDetection(audioData);
      };

      source.connect(processor);
      processor.connect(audioContext.destination);
    }

    // 語音檢測處理
    function processVoiceDetection(audioData) {
      const now = Date.now();
      
      // 多重音頻分析
      const analysis = analyzeAudioSegment(audioData);
      
      // 更新音量歷史
      voiceDetection.volumeHistory.push(analysis.rms);
      if (voiceDetection.volumeHistory.length > 100) {
        voiceDetection.volumeHistory.shift();
      }
      
      // 動態調整閾值
      updateDynamicThresholds();
      
      // 更新視覺化
      updateVoiceVisualization(analysis);
      
      // 語音活動檢測
      const isVoiceDetected = detectVoiceActivity(analysis);
      
      if (isVoiceDetected) {
        if (!voiceDetection.isVoiceActive) {
          // 語音開始
          voiceDetection.voiceStartTime = now;
          voiceDetection.isVoiceActive = true;
          console.log('🎤 語音活動開始');
        }
        voiceDetection.lastVoiceTime = now;
        
        // 添加到音頻緩衝區
        audioBuffer.push(...audioData);
        
      } else if (voiceDetection.isVoiceActive) {
        // 檢查是否應該結束語音段
        const silenceDuration = now - voiceDetection.lastVoiceTime;
        
        if (silenceDuration >= voiceDetection.silenceBuffer) {
          // 語音結束，處理音頻段
          const voiceDuration = now - voiceDetection.voiceStartTime;
          
          if (voiceDuration >= voiceDetection.minVoiceDuration && audioBuffer.length > 0) {
            processVoiceSegment();
            console.log(`🎤 語音段結束，時長: ${(voiceDuration/1000).toFixed(1)}秒`);
          } else {
            // 語音太短，拒絕
            stats.rejectedSegments++;
            stats.silenceRejections++;
            addRejectedTranscription(++currentSegmentId, (voiceDuration/1000).toFixed(1), '語音時長過短');
            console.log(`🚫 拒絕短語音段: ${(voiceDuration/1000).toFixed(1)}秒`);
          }
          
          // 重置狀態
          voiceDetection.isVoiceActive = false;
          voiceDetection.voiceStartTime = null;
          audioBuffer = [];
        } else {
          // 仍在靜音緩衝期內，繼續添加音頻
          audioBuffer.push(...audioData);
        }
      }
    }

    // 分析音頻片段
    function analyzeAudioSegment(audioData) {
      let sum = 0;
      let energy = 0;
      let zeroCrossings = 0;
      
      for (let i = 0; i < audioData.length; i++) {
        const sample = audioData[i];
        sum += sample * sample;
        energy += Math.abs(sample);
        
        if (i > 0 && audioData[i] * audioData[i-1] < 0) {
          zeroCrossings++;
        }
      }
      
      const rms = Math.sqrt(sum / audioData.length);
      const avgEnergy = energy / audioData.length;
      const zcr = zeroCrossings / audioData.length;
      
      // 頻譜能量估算
      const spectralEnergy = calculateSpectralEnergy(audioData);
      
      return {
        rms: rms,
        energy: avgEnergy,
        zcr: zcr,
        spectralEnergy: spectralEnergy,
        length: audioData.length
      };
    }

    // 計算頻譜能量
    function calculateSpectralEnergy(audioData) {
      // 簡化的頻譜能量計算
      let highFreqEnergy = 0;
      let midFreqEnergy = 0;
      
      for (let i = 1; i < audioData.length; i++) {
        const diff = audioData[i] - audioData[i-1];
        highFreqEnergy += diff * diff;
        midFreqEnergy += audioData[i] * audioData[i];
      }
      
      return {
        high: highFreqEnergy / audioData.length,
        mid: midFreqEnergy / audioData.length
      };
    }

    // 語音活動檢測
    function detectVoiceActivity(analysis) {
      // 多重條件檢測
      const volumeCheck = analysis.rms > voiceDetection.volumeThreshold;
      const energyCheck = analysis.energy > voiceDetection.energyThreshold;
      const spectralCheck = analysis.spectralEnergy.high > voiceDetection.spectralThreshold;
      const zcrCheck = analysis.zcr > 0.01 && analysis.zcr < 0.15; // 合理的過零率範圍
      
      // 至少滿足3個條件
      const conditions = [volumeCheck, energyCheck, spectralCheck, zcrCheck];
      const passedConditions = conditions.filter(c => c).length;
      
      return passedConditions >= 3;
    }

    // 動態調整閾值
    function updateDynamicThresholds() {
      if (voiceDetection.volumeHistory.length < 50) return;
      
      const recent = voiceDetection.volumeHistory.slice(-50);
      const avg = recent.reduce((a, b) => a + b, 0) / recent.length;
      const std = Math.sqrt(recent.reduce((a, b) => a + (b - avg) ** 2, 0) / recent.length);
      
      // 動態調整音量閾值
      voiceDetection.volumeThreshold = Math.max(0.005, avg + std * 2);
      voiceDetection.energyThreshold = Math.max(0.001, avg * 0.5);
    }

    // 處理語音段
    function processVoiceSegment() {
      if (audioBuffer.length === 0) return;
      
      const segmentId = ++currentSegmentId;
      const duration = (audioBuffer.length / 44100).toFixed(1);
      
      // 最終質量檢查
      const finalAnalysis = analyzeAudioSegment(new Float32Array(audioBuffer));
      const qualityScore = calculateQualityScore(finalAnalysis);
      
      if (qualityScore < 0.4) {
        // 質量過低，拒絕
        stats.rejectedSegments++;
        stats.qualityRejections++;
        addRejectedTranscription(segmentId, duration, `質量過低 (${(qualityScore*100).toFixed(0)}%)`);
        console.log(`🚫 拒絕低質量音頻: ${(qualityScore*100).toFixed(0)}%`);
      } else {
        // 質量合格，進行轉錄
        const audioBlob = createWavBlob(new Float32Array(audioBuffer), 44100);
        addProcessingTranscription(segmentId, duration, qualityScore);
        sendAudioForTranscription(audioBlob, segmentId);
        stats.validSegments++;
        console.log(`✅ 處理語音段: ${duration}秒, 質量: ${(qualityScore*100).toFixed(0)}%`);
      }
      
      stats.totalSegments++;
      audioBuffer = [];
    }

    // 計算質量評分
    function calculateQualityScore(analysis) {
      let score = 0;
      
      // 音量評分 (0-0.3)
      if (analysis.rms > 0.01) score += 0.3;
      else if (analysis.rms > 0.005) score += 0.2;
      else if (analysis.rms > 0.002) score += 0.1;
      
      // 能量評分 (0-0.3)
      if (analysis.energy > 0.005) score += 0.3;
      else if (analysis.energy > 0.002) score += 0.2;
      else if (analysis.energy > 0.001) score += 0.1;
      
      // 頻譜評分 (0-0.2)
      if (analysis.spectralEnergy.high > 0.01) score += 0.2;
      else if (analysis.spectralEnergy.high > 0.005) score += 0.1;
      
      // 過零率評分 (0-0.2)
      if (analysis.zcr > 0.02 && analysis.zcr < 0.1) score += 0.2;
      else if (analysis.zcr > 0.01 && analysis.zcr < 0.15) score += 0.1;
      
      return Math.min(score, 1.0);
    }

    // 開始檢測狀態更新
    function startDetectionStatusUpdate() {
      setInterval(() => {
        if (isRecording) {
          updateDetectionStatus();
        }
      }, 1000);
    }

    // 更新檢測狀態
    function updateDetectionStatus() {
      const voiceStatus = voiceDetection.isVoiceActive ? '🎤 語音活躍' : '🔇 靜音中';
      document.getElementById('voiceStatus').textContent = voiceStatus;
      
      document.getElementById('volumeThreshold').textContent = voiceDetection.volumeThreshold.toFixed(4);
      
      const activity = voiceDetection.isVoiceActive ? '檢測到語音' : '無語音活動';
      document.getElementById('voiceActivity').textContent = activity;
      
      const rejectionRate = stats.totalSegments > 0 ? 
        ((stats.rejectedSegments / stats.totalSegments) * 100).toFixed(1) : 0;
      document.getElementById('rejectionStats').textContent = 
        `${stats.rejectedSegments} 拒絕 / ${stats.totalSegments} 總計 (${rejectionRate}%)`;
    }

    // 更新語音視覺化
    function updateVoiceVisualization(analysis) {
      const volumeBar = document.getElementById('volumeBar');
      const detectionInfo = document.getElementById('detectionInfo');
      const silenceStatus = document.getElementById('silenceStatus');
      const qualityScore = document.getElementById('qualityScore');
      const thresholdLine = document.getElementById('thresholdLine');
      
      const volumePercent = Math.min(analysis.rms * 2000, 100);
      volumeBar.style.width = volumePercent + '%';
      
      const thresholdPercent = Math.min(voiceDetection.volumeThreshold * 2000, 100);
      thresholdLine.style.left = thresholdPercent + '%';
      
      const isActive = voiceDetection.isVoiceActive;
      detectionInfo.textContent = `語音檢測：${isActive ? '活躍' : '待機'}`;
      detectionInfo.style.color = isActive ? '#28a745' : '#ffc107';
      
      silenceStatus.textContent = `音量：${analysis.rms.toFixed(4)} | 閾值：${voiceDetection.volumeThreshold.toFixed(4)}`;
      
      const quality = calculateQualityScore(analysis);
      qualityScore.textContent = `質量：${(quality * 100).toFixed(0)}%`;
      qualityScore.style.color = quality > 0.6 ? '#28a745' : quality > 0.3 ? '#ffc107' : '#dc3545';
    }

    // 創建 WAV Blob
    function createWavBlob(audioData, sampleRate) {
      const length = audioData.length;
      const buffer = new ArrayBuffer(44 + length * 2);
      const view = new DataView(buffer);
      
      const writeString = (offset, string) => {
        for (let i = 0; i < string.length; i++) {
          view.setUint8(offset + i, string.charCodeAt(i));
        }
      };
      
      writeString(0, 'RIFF');
      view.setUint32(4, 36 + length * 2, true);
      writeString(8, 'WAVE');
      writeString(12, 'fmt ');
      view.setUint32(16, 16, true);
      view.setUint16(20, 1, true);
      view.setUint16(22, 1, true);
      view.setUint32(24, sampleRate, true);
      view.setUint32(28, sampleRate * 2, true);
      view.setUint16(32, 2, true);
      view.setUint16(34, 16, true);
      writeString(36, 'data');
      view.setUint32(40, length * 2, true);
      
      let offset = 44;
      for (let i = 0; i < length; i++) {
        const sample = Math.max(-1, Math.min(1, audioData[i]));
        view.setInt16(offset, sample * 0x7FFF, true);
        offset += 2;
      }
      
      return new Blob([buffer], { type: 'audio/wav' });
    }

    // 添加處理中轉錄
    function addProcessingTranscription(segmentId, duration, qualityScore) {
      const display = document.getElementById('transcriptionDisplay');
      const timestamp = new Date().toLocaleTimeString();

      const item = document.createElement('div');
      item.className = 'transcription-item processing';
      item.id = `segment-${segmentId}`;
      item.innerHTML = `
        <div class="timestamp">${timestamp} - 語音段 ${segmentId} (${duration}秒) [質量: ${(qualityScore * 100).toFixed(0)}%]</div>
        <div class="text-content">🔄 語音轉錄中...</div>
      `;

      display.appendChild(item);
      display.scrollTop = display.scrollHeight;
    }

    // 添加拒絕轉錄
    function addRejectedTranscription(segmentId, duration, reason) {
      const display = document.getElementById('transcriptionDisplay');
      const timestamp = new Date().toLocaleTimeString();

      const item = document.createElement('div');
      item.className = 'transcription-item rejected';
      item.id = `segment-${segmentId}`;
      item.style.display = showRejected ? 'block' : 'none';
      item.innerHTML = `
        <div class="timestamp">${timestamp} - 拒絕段 ${segmentId} (${duration}秒)</div>
        <div class="text-content">🚫 已拒絕：${reason}</div>
      `;

      display.appendChild(item);
      display.scrollTop = display.scrollHeight;
    }

    // 發送轉錄
    async function sendAudioForTranscription(audioBlob, segmentId) {
      try {
        const formData = new FormData();
        formData.append('audio_file', audioBlob, `voice_${segmentId}.wav`);

        const response = await fetch('/api/transcribe', {
          method: 'POST',
          body: formData
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`);
        }

        const data = await response.json();
        updateTranscriptionResult(segmentId, data.transcription || '(無轉錄結果)');
        
      } catch (error) {
        console.error('轉錄錯誤:', error);
        updateTranscriptionResult(segmentId, `❌ 轉錄失敗: ${error.message}`);
      }
    }

    // 更新轉錄結果
    function updateTranscriptionResult(segmentId, transcription) {
      const item = document.getElementById(`segment-${segmentId}`);
      if (item) {
        const textContent = item.querySelector('.text-content');
        textContent.textContent = transcription;
        item.classList.remove('processing');
      }
    }

    // 切換顯示拒絕項目
    function toggleRejected() {
      showRejected = !showRejected;
      const rejectedItems = document.querySelectorAll('.transcription-item.rejected');
      const btn = document.getElementById('toggleRejectedBtn');
      
      rejectedItems.forEach(item => {
        item.style.display = showRejected ? 'block' : 'none';
      });
      
      btn.textContent = showRejected ? '👁️ 隱藏拒絕項目' : '👁️ 顯示拒絕項目';
    }

    // 清空轉錄
    function clearTranscriptions() {
      document.getElementById('transcriptionDisplay').innerHTML = '';
      stats = { totalSegments: 0, validSegments: 0, rejectedSegments: 0, silenceRejections: 0, qualityRejections: 0 };
      currentSegmentId = 0;
    }

    // 匯出轉錄
    function exportTranscriptions() {
      const display = document.getElementById('transcriptionDisplay');
      const validItems = display.querySelectorAll('.transcription-item:not(.rejected):not(.processing)');

      let exportText = '靜音感知轉錄記錄\n================\n\n';

      validItems.forEach(item => {
        const timestamp = item.querySelector('.timestamp').textContent;
        const textContent = item.querySelector('.text-content').textContent;
        if (!textContent.includes('轉錄失敗')) {
          exportText += `${timestamp}\n${textContent}\n\n`;
        }
      });

      const blob = new Blob([exportText], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `silence_aware_transcription_${new Date().toISOString().slice(0, 19)}.txt`;
      a.click();
      URL.revokeObjectURL(url);
    }

    // 複製有效內容
    function copyValidText() {
      const display = document.getElementById('transcriptionDisplay');
      const validItems = display.querySelectorAll('.transcription-item:not(.rejected):not(.processing)');

      let copyText = '';
      validItems.forEach(item => {
        const textContent = item.querySelector('.text-content').textContent;
        if (!textContent.includes('轉錄失敗') && !textContent.includes('轉錄中')) {
          copyText += textContent + '\n';
        }
      });

      navigator.clipboard.writeText(copyText).then(() => {
        alert(`已複製 ${copyText.split('\n').filter(line => line.trim()).length} 條有效轉錄內容`);
      });
    }
  </script>
</body>
</html>
