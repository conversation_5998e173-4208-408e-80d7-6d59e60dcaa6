<!DOCTYPE html>
<html lang="zh-TW">
<head>
  <meta charset="UTF-8">
  <title>可調整語音轉錄系統 (靈活檢測)</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      max-width: 1400px;
      margin: 0 auto;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      color: white;
    }
    
    .container {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 20px;
      padding: 30px;
      color: #333;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }
    
    h1 {
      text-align: center;
      color: #2c3e50;
      margin-bottom: 30px;
    }
    
    .section {
      background: #f8f9fa;
      border-radius: 15px;
      padding: 25px;
      margin: 20px 0;
      border: 2px solid #e9ecef;
    }
    
    button {
      margin: 5px 10px 5px 0;
      padding: 12px 24px;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
      border: none;
      border-radius: 8px;
      transition: all 0.3s ease;
    }
    
    button.primary {
      background: linear-gradient(45deg, #28a745, #20c997);
      color: white;
    }
    
    button.danger {
      background: linear-gradient(45deg, #dc3545, #fd7e14);
      color: white;
    }
    
    button.secondary {
      background: linear-gradient(45deg, #6c757d, #495057);
      color: white;
    }
    
    button.preset {
      background: linear-gradient(45deg, #007bff, #6610f2);
      color: white;
      margin: 5px;
      padding: 8px 16px;
      font-size: 14px;
    }
    
    button:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    
    button:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
      opacity: 0.6;
    }
    
    button.recording {
      animation: pulse 1.5s infinite;
    }
    
    @keyframes pulse {
      0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
      70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
      100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
    }
    
    select, input[type="range"] {
      margin: 5px 10px 5px 0;
      padding: 8px 12px;
      font-size: 16px;
      border: 2px solid #dee2e6;
      border-radius: 8px;
      background: white;
    }
    
    input[type="range"] {
      width: 200px;
    }
    
    .status {
      padding: 15px;
      margin: 10px 0;
      border-radius: 8px;
      font-weight: bold;
    }
    
    .status.success {
      background: #d4edda;
      color: #155724;
    }
    
    .status.error {
      background: #f8d7da;
      color: #721c24;
    }
    
    .status.info {
      background: #d1ecf1;
      color: #0c5460;
    }
    
    .status.warning {
      background: #fff3cd;
      color: #856404;
    }
    
    .transcription-display {
      background: white;
      border: 1px solid #dee2e6;
      border-radius: 10px;
      padding: 20px;
      margin: 20px 0;
      max-height: 500px;
      overflow-y: auto;
    }
    
    .transcription-item {
      background: #f8f9fa;
      margin: 10px 0;
      padding: 15px;
      border-radius: 8px;
      border-left: 4px solid #28a745;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      transition: all 0.3s ease;
    }
    
    .transcription-item.processing {
      border-left-color: #ffc107;
      background: #fff8e1;
    }
    
    .transcription-item.rejected {
      border-left-color: #dc3545;
      background: #ffe6e6;
      opacity: 0.7;
    }
    
    .timestamp {
      font-size: 12px;
      color: #6c757d;
      margin-bottom: 5px;
    }
    
    .text-content {
      font-size: 16px;
      line-height: 1.5;
      margin-top: 8px;
    }
    
    .success-box {
      background: #d4edda;
      border: 1px solid #c3e6cb;
      padding: 15px;
      border-radius: 10px;
      margin: 15px 0;
      color: #155724;
    }
    
    .adjustment-panel {
      background: #fff3cd;
      border: 2px solid #ffc107;
      border-radius: 15px;
      padding: 20px;
      margin: 20px 0;
    }
    
    .adjustment-row {
      display: flex;
      align-items: center;
      margin: 15px 0;
      flex-wrap: wrap;
      gap: 15px;
    }
    
    .adjustment-row label {
      min-width: 120px;
      font-weight: bold;
    }
    
    .value-display {
      background: white;
      padding: 5px 10px;
      border-radius: 5px;
      border: 1px solid #ddd;
      min-width: 80px;
      text-align: center;
      font-weight: bold;
    }
    
    .preset-buttons {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin: 15px 0;
    }
    
    .audio-visualizer {
      width: 100%;
      height: 120px;
      background: #2c3e50;
      border-radius: 8px;
      margin: 10px 0;
      position: relative;
      overflow: hidden;
    }
    
    .volume-bar {
      height: 40%;
      background: linear-gradient(90deg, #28a745, #ffc107, #dc3545);
      width: 0%;
      transition: width 0.1s ease;
      border-radius: 8px;
      margin-top: 30%;
    }
    
    .threshold-indicators {
      position: absolute;
      top: 0;
      bottom: 0;
      width: 100%;
    }
    
    .threshold-line {
      position: absolute;
      top: 0;
      bottom: 0;
      width: 2px;
      background: #ffc107;
    }
    
    .threshold-label {
      position: absolute;
      top: 5px;
      font-size: 10px;
      color: white;
      background: rgba(0,0,0,0.5);
      padding: 2px 4px;
      border-radius: 3px;
    }
    
    .detection-info {
      position: absolute;
      top: 10px;
      left: 10px;
      color: white;
      font-size: 12px;
      font-weight: bold;
    }
    
    .stats-info {
      position: absolute;
      bottom: 10px;
      left: 10px;
      color: white;
      font-size: 12px;
    }
    
    .quality-info {
      position: absolute;
      top: 10px;
      right: 10px;
      color: white;
      font-size: 12px;
      font-weight: bold;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🎛️ 可調整語音轉錄系統</h1>
    
    <!-- 說明 -->
    <div class="success-box">
      <h3>🔧 靈活調整機制</h3>
      <p>此系統提供完整的調整控制，避免過度敏感的檢測：</p>
      <ul>
        <li><strong>實時調整</strong>：可即時調整檢測參數</li>
        <li><strong>預設模式</strong>：提供寬鬆、平衡、嚴格三種模式</li>
        <li><strong>視覺化反饋</strong>：實時顯示檢測狀態和閾值</li>
        <li><strong>統計監控</strong>：顯示接受/拒絕比例</li>
      </ul>
    </div>
    
    <!-- 調整控制面板 -->
    <div class="adjustment-panel">
      <h3>🎚️ 檢測參數調整</h3>
      
      <!-- 預設模式 -->
      <div class="adjustment-row">
        <label>快速設定：</label>
        <div class="preset-buttons">
          <button class="preset" onclick="applyPreset('relaxed')">🟢 寬鬆模式</button>
          <button class="preset" onclick="applyPreset('balanced')">🟡 平衡模式</button>
          <button class="preset" onclick="applyPreset('strict')">🔴 嚴格模式</button>
          <button class="preset" onclick="applyPreset('custom')">⚙️ 自訂模式</button>
        </div>
      </div>
      
      <!-- 音量閾值 -->
      <div class="adjustment-row">
        <label>音量閾值：</label>
        <input type="range" id="volumeThreshold" min="0.001" max="0.05" step="0.001" value="0.008">
        <div class="value-display" id="volumeValue">0.008</div>
        <small>越小越敏感</small>
      </div>
      
      <!-- 最小語音時長 -->
      <div class="adjustment-row">
        <label>最小語音時長：</label>
        <input type="range" id="minVoiceDuration" min="300" max="2000" step="100" value="800">
        <div class="value-display" id="durationValue">800ms</div>
        <small>最短語音長度</small>
      </div>
      
      <!-- 靜音緩衝時間 -->
      <div class="adjustment-row">
        <label>靜音緩衝時間：</label>
        <input type="range" id="silenceBuffer" min="500" max="3000" step="100" value="1200">
        <div class="value-display" id="bufferValue">1200ms</div>
        <small>語音結束等待時間</small>
      </div>
      
      <!-- 質量閾值 -->
      <div class="adjustment-row">
        <label>質量閾值：</label>
        <input type="range" id="qualityThreshold" min="0.1" max="0.8" step="0.05" value="0.25">
        <div class="value-display" id="qualityValue">0.25</div>
        <small>越小越寬鬆</small>
      </div>
      
      <!-- 檢測條件數 -->
      <div class="adjustment-row">
        <label>必要檢測條件：</label>
        <input type="range" id="requiredConditions" min="1" max="4" step="1" value="2">
        <div class="value-display" id="conditionsValue">2/4</div>
        <small>需滿足的條件數</small>
      </div>
    </div>
    
    <!-- 實時監控 -->
    <div class="section">
      <h3>📊 實時檢測監控</h3>
      <div class="audio-visualizer">
        <div class="threshold-indicators" id="thresholdIndicators"></div>
        <div class="volume-bar" id="volumeBar"></div>
        <div class="detection-info" id="detectionInfo">檢測狀態：待機</div>
        <div class="stats-info" id="statsInfo">統計：0 接受 / 0 拒絕</div>
        <div class="quality-info" id="qualityInfo">質量：--</div>
      </div>
      
      <div id="detectionStatus" class="status info">調整參數後點擊開始轉錄</div>
    </div>
    
    <!-- 控制 -->
    <div class="section">
      <h3>🎯 轉錄控制</h3>
      
      <label for="audioQuality">音頻品質：</label>
      <select id="audioQuality">
        <option value="192000,44100">192 kbps, 44.1 kHz (推薦)</option>
        <option value="128000,44100">128 kbps, 44.1 kHz</option>
        <option value="64000,22050">64 kbps, 22.05 kHz</option>
      </select>
      <br><br>
      
      <button id="startCapture" class="primary">🎛️ 開始可調整轉錄</button>
      <button id="stopCapture" class="danger" disabled>⏹️ 停止轉錄</button>
      <button id="resetSettings" class="secondary" onclick="resetToDefaults()">🔄 重置設定</button>
      
      <div id="captureStatus" class="status info">調整參數後點擊開始轉錄</div>
    </div>
    
    <!-- 轉錄結果 -->
    <div class="section">
      <h3>📝 轉錄結果</h3>
      <button onclick="clearTranscriptions()" class="secondary">🗑️ 清空</button>
      <button onclick="exportTranscriptions()" class="secondary">💾 匯出</button>
      <button onclick="copyValidText()" class="secondary">📋 複製有效內容</button>
      <button onclick="toggleRejected()" class="secondary" id="toggleRejectedBtn">👁️ 顯示拒絕項目</button>
      
      <div class="transcription-display" id="transcriptionDisplay">
        <div class="transcription-item">
          <div class="timestamp">等待中...</div>
          <div class="text-content">調整檢測參數後開始轉錄，系統會根據您的設定進行語音檢測</div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 全局變量
    let capturedStream, micStream, audioContext;
    let isRecording = false;
    let audioBuffer = [];
    let currentSegmentId = 0;
    let showRejected = false;
    
    // 可調整的檢測參數
    let detectionParams = {
      volumeThreshold: 0.008,      // 放鬆的音量閾值
      minVoiceDuration: 800,       // 放鬆的最小時長
      silenceBuffer: 1200,         // 適中的緩衝時間
      qualityThreshold: 0.25,      // 放鬆的質量閾值
      requiredConditions: 2,       // 只需滿足2個條件
      voiceStartTime: null,
      lastVoiceTime: null,
      isVoiceActive: false,
      volumeHistory: []
    };
    
    // 統計數據
    let stats = {
      totalSegments: 0,
      acceptedSegments: 0,
      rejectedSegments: 0
    };

    // 預設模式配置
    const presets = {
      relaxed: {
        volumeThreshold: 0.005,
        minVoiceDuration: 500,
        silenceBuffer: 1000,
        qualityThreshold: 0.15,
        requiredConditions: 1
      },
      balanced: {
        volumeThreshold: 0.008,
        minVoiceDuration: 800,
        silenceBuffer: 1200,
        qualityThreshold: 0.25,
        requiredConditions: 2
      },
      strict: {
        volumeThreshold: 0.015,
        minVoiceDuration: 1200,
        silenceBuffer: 1800,
        qualityThreshold: 0.4,
        requiredConditions: 3
      },
      custom: {
        volumeThreshold: 0.008,
        minVoiceDuration: 800,
        silenceBuffer: 1200,
        qualityThreshold: 0.25,
        requiredConditions: 2
      }
    };

    // 初始化控制器
    window.onload = function() {
      setupControllers();
      applyPreset('balanced'); // 默認使用平衡模式
      updateThresholdIndicators();
    };

    // 設置控制器
    function setupControllers() {
      document.getElementById('volumeThreshold').addEventListener('input', updateParams);
      document.getElementById('minVoiceDuration').addEventListener('input', updateParams);
      document.getElementById('silenceBuffer').addEventListener('input', updateParams);
      document.getElementById('qualityThreshold').addEventListener('input', updateParams);
      document.getElementById('requiredConditions').addEventListener('input', updateParams);
    }

    // 更新參數
    function updateParams() {
      detectionParams.volumeThreshold = parseFloat(document.getElementById('volumeThreshold').value);
      detectionParams.minVoiceDuration = parseInt(document.getElementById('minVoiceDuration').value);
      detectionParams.silenceBuffer = parseInt(document.getElementById('silenceBuffer').value);
      detectionParams.qualityThreshold = parseFloat(document.getElementById('qualityThreshold').value);
      detectionParams.requiredConditions = parseInt(document.getElementById('requiredConditions').value);
      
      updateValueDisplays();
      updateThresholdIndicators();
    }

    // 更新數值顯示
    function updateValueDisplays() {
      document.getElementById('volumeValue').textContent = detectionParams.volumeThreshold.toFixed(3);
      document.getElementById('durationValue').textContent = detectionParams.minVoiceDuration + 'ms';
      document.getElementById('bufferValue').textContent = detectionParams.silenceBuffer + 'ms';
      document.getElementById('qualityValue').textContent = detectionParams.qualityThreshold.toFixed(2);
      document.getElementById('conditionsValue').textContent = detectionParams.requiredConditions + '/4';
    }

    // 應用預設模式
    function applyPreset(presetName) {
      const preset = presets[presetName];
      
      document.getElementById('volumeThreshold').value = preset.volumeThreshold;
      document.getElementById('minVoiceDuration').value = preset.minVoiceDuration;
      document.getElementById('silenceBuffer').value = preset.silenceBuffer;
      document.getElementById('qualityThreshold').value = preset.qualityThreshold;
      document.getElementById('requiredConditions').value = preset.requiredConditions;
      
      updateParams();
      
      // 更新狀態顯示
      const modeNames = {
        relaxed: '寬鬆模式 - 捕獲更多語音',
        balanced: '平衡模式 - 推薦設定',
        strict: '嚴格模式 - 高質量過濾',
        custom: '自訂模式 - 手動調整'
      };
      
      document.getElementById('detectionStatus').innerHTML = 
        `<div class="status info">✅ 已套用${modeNames[presetName]}</div>`;
    }

    // 重置到默認設定
    function resetToDefaults() {
      applyPreset('balanced');
    }

    // 更新閾值指示器
    function updateThresholdIndicators() {
      const indicators = document.getElementById('thresholdIndicators');
      const volumePercent = Math.min(detectionParams.volumeThreshold * 2000, 100);
      
      indicators.innerHTML = `
        <div class="threshold-line" style="left: ${volumePercent}%"></div>
        <div class="threshold-label" style="left: ${volumePercent}%">閾值</div>
      `;
    }

    const startCaptureButton = document.getElementById('startCapture');
    const stopCaptureButton = document.getElementById('stopCapture');
    const audioQualitySelect = document.getElementById('audioQuality');

    // 開始可調整轉錄
    startCaptureButton.addEventListener('click', async () => {
      try {
        const statusDiv = document.getElementById('captureStatus');
        statusDiv.innerHTML = '<div class="status info">🔄 啟動可調整轉錄...</div>';
        
        const [audioBitRate, audioSampleRate] = audioQualitySelect.value.split(',').map(Number);

        // 捕捉音頻
        capturedStream = await navigator.mediaDevices.getDisplayMedia({
          video: { width: { ideal: 1280 }, height: { ideal: 720 }, frameRate: { ideal: 30 } },
          audio: {
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false,
            sampleRate: audioSampleRate,
            channelCount: 2
          }
        });

        micStream = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false
          }
        });

        // 建立音頻混合
        audioContext = new AudioContext();
        const destination = audioContext.createMediaStreamDestination();

        if (capturedStream.getAudioTracks().length > 0) {
          const systemAudioSource = audioContext.createMediaStreamSource(capturedStream);
          systemAudioSource.connect(destination);
        }
        if (micStream.getAudioTracks().length > 0) {
          const micAudioSource = audioContext.createMediaStreamSource(micStream);
          micAudioSource.connect(destination);
        }

        // 設置可調整檢測處理
        setupAdjustableDetectionProcessing(destination.stream);

        startCaptureButton.disabled = true;
        stopCaptureButton.disabled = false;
        startCaptureButton.classList.add('recording');
        
        statusDiv.innerHTML = '<div class="status success">🎛️ 可調整轉錄中</div>';
        isRecording = true;

        // 開始狀態更新
        startStatusUpdate();

        capturedStream.getTracks().forEach(track => {
          track.onended = () => stopCapture();
        });

      } catch (err) {
        console.error('可調整轉錄啟動失敗:', err);
        document.getElementById('captureStatus').innerHTML = 
          `<div class="status error">❌ 啟動失敗：${err.message}</div>`;
      }
    });

    // 停止轉錄
    stopCaptureButton.addEventListener('click', stopCapture);

    function stopCapture() {
      isRecording = false;
      
      // 處理剩餘音頻
      if (audioBuffer.length > 0 && detectionParams.isVoiceActive) {
        processVoiceSegment();
      }
      
      [capturedStream, micStream].forEach(stream => {
        if (stream) {
          stream.getTracks().forEach(track => track.stop());
        }
      });
      
      if (audioContext) {
        audioContext.close();
        audioContext = null;
      }
      
      startCaptureButton.disabled = false;
      stopCaptureButton.disabled = true;
      startCaptureButton.classList.remove('recording');
      
      document.getElementById('captureStatus').innerHTML = 
        '<div class="status info">⏹️ 可調整轉錄已停止</div>';
    }

    // 設置可調整檢測處理
    function setupAdjustableDetectionProcessing(mixedStream) {
      const source = audioContext.createMediaStreamSource(mixedStream);
      const processor = audioContext.createScriptProcessor(4096, 1, 1);

      processor.onaudioprocess = function(event) {
        if (!isRecording) return;
        
        const audioData = event.inputBuffer.getChannelData(0);
        processAdjustableVoiceDetection(audioData);
      };

      source.connect(processor);
      processor.connect(audioContext.destination);
    }

    // 可調整語音檢測處理
    function processAdjustableVoiceDetection(audioData) {
      const now = Date.now();
      
      // 音頻分析
      const analysis = analyzeAudioSegment(audioData);
      
      // 更新音量歷史
      detectionParams.volumeHistory.push(analysis.rms);
      if (detectionParams.volumeHistory.length > 50) {
        detectionParams.volumeHistory.shift();
      }
      
      // 更新視覺化
      updateAdjustableVisualization(analysis);
      
      // 可調整語音活動檢測
      const isVoiceDetected = detectAdjustableVoiceActivity(analysis);
      
      if (isVoiceDetected) {
        if (!detectionParams.isVoiceActive) {
          // 語音開始
          detectionParams.voiceStartTime = now;
          detectionParams.isVoiceActive = true;
          console.log('🎤 語音活動開始');
        }
        detectionParams.lastVoiceTime = now;
        audioBuffer.push(...audioData);
        
      } else if (detectionParams.isVoiceActive) {
        // 檢查是否應該結束語音段
        const silenceDuration = now - detectionParams.lastVoiceTime;
        
        if (silenceDuration >= detectionParams.silenceBuffer) {
          // 語音結束
          const voiceDuration = now - detectionParams.voiceStartTime;
          
          if (voiceDuration >= detectionParams.minVoiceDuration && audioBuffer.length > 0) {
            processVoiceSegment();
            console.log(`✅ 處理語音段: ${(voiceDuration/1000).toFixed(1)}秒`);
          } else {
            // 語音太短，拒絕
            stats.rejectedSegments++;
            addRejectedTranscription(++currentSegmentId, (voiceDuration/1000).toFixed(1), '語音時長過短');
            console.log(`🚫 拒絕短語音段: ${(voiceDuration/1000).toFixed(1)}秒`);
          }
          
          // 重置狀態
          detectionParams.isVoiceActive = false;
          detectionParams.voiceStartTime = null;
          audioBuffer = [];
        } else {
          // 仍在緩衝期內
          audioBuffer.push(...audioData);
        }
      }
    }

    // 分析音頻片段
    function analyzeAudioSegment(audioData) {
      let sum = 0;
      let energy = 0;
      let zeroCrossings = 0;
      
      for (let i = 0; i < audioData.length; i++) {
        const sample = audioData[i];
        sum += sample * sample;
        energy += Math.abs(sample);
        
        if (i > 0 && audioData[i] * audioData[i-1] < 0) {
          zeroCrossings++;
        }
      }
      
      const rms = Math.sqrt(sum / audioData.length);
      const avgEnergy = energy / audioData.length;
      const zcr = zeroCrossings / audioData.length;
      
      return { rms, energy: avgEnergy, zcr, length: audioData.length };
    }

    // 可調整語音活動檢測
    function detectAdjustableVoiceActivity(analysis) {
      // 四個檢測條件
      const conditions = [
        analysis.rms > detectionParams.volumeThreshold,           // 音量檢測
        analysis.energy > detectionParams.volumeThreshold * 0.5, // 能量檢測
        analysis.zcr > 0.01 && analysis.zcr < 0.2,              // 過零率檢測
        analysis.rms > 0.002                                     // 基本音量檢測
      ];
      
      const passedConditions = conditions.filter(c => c).length;
      return passedConditions >= detectionParams.requiredConditions;
    }

    // 處理語音段
    function processVoiceSegment() {
      if (audioBuffer.length === 0) return;
      
      const segmentId = ++currentSegmentId;
      const duration = (audioBuffer.length / 44100).toFixed(1);
      
      // 質量檢查
      const finalAnalysis = analyzeAudioSegment(new Float32Array(audioBuffer));
      const qualityScore = calculateQualityScore(finalAnalysis);
      
      if (qualityScore < detectionParams.qualityThreshold) {
        // 質量過低，拒絕
        stats.rejectedSegments++;
        addRejectedTranscription(segmentId, duration, `質量過低 (${(qualityScore*100).toFixed(0)}%)`);
      } else {
        // 質量合格，進行轉錄
        const audioBlob = createWavBlob(new Float32Array(audioBuffer), 44100);
        addProcessingTranscription(segmentId, duration, qualityScore);
        sendAudioForTranscription(audioBlob, segmentId);
        stats.acceptedSegments++;
      }
      
      stats.totalSegments++;
      audioBuffer = [];
    }

    // 計算質量評分
    function calculateQualityScore(analysis) {
      let score = 0;
      if (analysis.rms > 0.005) score += 0.4;
      if (analysis.energy > 0.002) score += 0.3;
      if (analysis.zcr > 0.01 && analysis.zcr < 0.15) score += 0.3;
      return Math.min(score, 1.0);
    }

    // 開始狀態更新
    function startStatusUpdate() {
      setInterval(() => {
        if (isRecording) {
          updateDetectionStatus();
        }
      }, 1000);
    }

    // 更新檢測狀態
    function updateDetectionStatus() {
      const acceptanceRate = stats.totalSegments > 0 ? 
        ((stats.acceptedSegments / stats.totalSegments) * 100).toFixed(1) : 0;
      
      document.getElementById('statsInfo').textContent = 
        `統計：${stats.acceptedSegments} 接受 / ${stats.rejectedSegments} 拒絕 (${acceptanceRate}%)`;
    }

    // 更新可調整視覺化
    function updateAdjustableVisualization(analysis) {
      const volumeBar = document.getElementById('volumeBar');
      const detectionInfo = document.getElementById('detectionInfo');
      const qualityInfo = document.getElementById('qualityInfo');
      
      const volumePercent = Math.min(analysis.rms * 2000, 100);
      volumeBar.style.width = volumePercent + '%';
      
      const isActive = detectionParams.isVoiceActive;
      detectionInfo.textContent = `檢測狀態：${isActive ? '🎤 語音活躍' : '🔇 待機中'}`;
      detectionInfo.style.color = isActive ? '#28a745' : '#ffc107';
      
      const quality = calculateQualityScore(analysis);
      qualityInfo.textContent = `質量：${(quality * 100).toFixed(0)}%`;
      qualityInfo.style.color = quality > detectionParams.qualityThreshold ? '#28a745' : '#dc3545';
    }

    // 創建 WAV Blob
    function createWavBlob(audioData, sampleRate) {
      const length = audioData.length;
      const buffer = new ArrayBuffer(44 + length * 2);
      const view = new DataView(buffer);
      
      const writeString = (offset, string) => {
        for (let i = 0; i < string.length; i++) {
          view.setUint8(offset + i, string.charCodeAt(i));
        }
      };
      
      writeString(0, 'RIFF');
      view.setUint32(4, 36 + length * 2, true);
      writeString(8, 'WAVE');
      writeString(12, 'fmt ');
      view.setUint32(16, 16, true);
      view.setUint16(20, 1, true);
      view.setUint16(22, 1, true);
      view.setUint32(24, sampleRate, true);
      view.setUint32(28, sampleRate * 2, true);
      view.setUint16(32, 2, true);
      view.setUint16(34, 16, true);
      writeString(36, 'data');
      view.setUint32(40, length * 2, true);
      
      let offset = 44;
      for (let i = 0; i < length; i++) {
        const sample = Math.max(-1, Math.min(1, audioData[i]));
        view.setInt16(offset, sample * 0x7FFF, true);
        offset += 2;
      }
      
      return new Blob([buffer], { type: 'audio/wav' });
    }

    // 添加處理中轉錄
    function addProcessingTranscription(segmentId, duration, qualityScore) {
      const display = document.getElementById('transcriptionDisplay');
      const timestamp = new Date().toLocaleTimeString();

      const item = document.createElement('div');
      item.className = 'transcription-item processing';
      item.id = `segment-${segmentId}`;
      item.innerHTML = `
        <div class="timestamp">${timestamp} - 語音段 ${segmentId} (${duration}秒) [質量: ${(qualityScore * 100).toFixed(0)}%]</div>
        <div class="text-content">🔄 轉錄中...</div>
      `;

      display.appendChild(item);
      display.scrollTop = display.scrollHeight;
    }

    // 添加拒絕轉錄
    function addRejectedTranscription(segmentId, duration, reason) {
      const display = document.getElementById('transcriptionDisplay');
      const timestamp = new Date().toLocaleTimeString();

      const item = document.createElement('div');
      item.className = 'transcription-item rejected';
      item.id = `segment-${segmentId}`;
      item.style.display = showRejected ? 'block' : 'none';
      item.innerHTML = `
        <div class="timestamp">${timestamp} - 拒絕段 ${segmentId} (${duration}秒)</div>
        <div class="text-content">🚫 已拒絕：${reason}</div>
      `;

      display.appendChild(item);
      display.scrollTop = display.scrollHeight;
    }

    // 發送轉錄
    async function sendAudioForTranscription(audioBlob, segmentId) {
      try {
        const formData = new FormData();
        formData.append('audio_file', audioBlob, `adjustable_${segmentId}.wav`);

        const response = await fetch('/api/transcribe', {
          method: 'POST',
          body: formData
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`);
        }

        const data = await response.json();
        updateTranscriptionResult(segmentId, data.transcription || '(無轉錄結果)');
        
      } catch (error) {
        console.error('轉錄錯誤:', error);
        updateTranscriptionResult(segmentId, `❌ 轉錄失敗: ${error.message}`);
      }
    }

    // 更新轉錄結果
    function updateTranscriptionResult(segmentId, transcription) {
      const item = document.getElementById(`segment-${segmentId}`);
      if (item) {
        const textContent = item.querySelector('.text-content');
        textContent.textContent = transcription;
        item.classList.remove('processing');
      }
    }

    // 切換顯示拒絕項目
    function toggleRejected() {
      showRejected = !showRejected;
      const rejectedItems = document.querySelectorAll('.transcription-item.rejected');
      const btn = document.getElementById('toggleRejectedBtn');
      
      rejectedItems.forEach(item => {
        item.style.display = showRejected ? 'block' : 'none';
      });
      
      btn.textContent = showRejected ? '👁️ 隱藏拒絕項目' : '👁️ 顯示拒絕項目';
    }

    // 清空轉錄
    function clearTranscriptions() {
      document.getElementById('transcriptionDisplay').innerHTML = '';
      stats = { totalSegments: 0, acceptedSegments: 0, rejectedSegments: 0 };
      currentSegmentId = 0;
    }

    // 匯出轉錄
    function exportTranscriptions() {
      const display = document.getElementById('transcriptionDisplay');
      const validItems = display.querySelectorAll('.transcription-item:not(.rejected):not(.processing)');

      let exportText = '可調整轉錄記錄\n================\n\n';

      validItems.forEach(item => {
        const timestamp = item.querySelector('.timestamp').textContent;
        const textContent = item.querySelector('.text-content').textContent;
        if (!textContent.includes('轉錄失敗')) {
          exportText += `${timestamp}\n${textContent}\n\n`;
        }
      });

      const blob = new Blob([exportText], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `adjustable_transcription_${new Date().toISOString().slice(0, 19)}.txt`;
      a.click();
      URL.revokeObjectURL(url);
    }

    // 複製有效內容
    function copyValidText() {
      const display = document.getElementById('transcriptionDisplay');
      const validItems = display.querySelectorAll('.transcription-item:not(.rejected):not(.processing)');

      let copyText = '';
      validItems.forEach(item => {
        const textContent = item.querySelector('.text-content').textContent;
        if (!textContent.includes('轉錄失敗') && !textContent.includes('轉錄中')) {
          copyText += textContent + '\n';
        }
      });

      navigator.clipboard.writeText(copyText).then(() => {
        alert(`已複製 ${copyText.split('\n').filter(line => line.trim()).length} 條有效轉錄內容`);
      });
    }
  </script>
</body>
</html>
