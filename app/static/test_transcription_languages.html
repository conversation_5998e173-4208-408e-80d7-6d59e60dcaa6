<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>轉錄語言測試</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }
        select, button {
            padding: 10px;
            margin: 10px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background: #4CAF50;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background: #45a049;
        }
        .result {
            margin: 10px 0;
            padding: 15px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 轉錄語言功能測試</h1>
        
        <div class="test-section">
            <h3>選擇轉錄語言</h3>
            <select id="languageSelect">
                <option value="auto">🔍 自動檢測</option>
                <option value="chinese">🇨🇳 中文 (Chinese)</option>
                <option value="english">🇺🇸 英文 (English)</option>
                <option value="vietnamese">🇻🇳 越南文 (Tiếng Việt)</option>
            </select>
            <button onclick="testTranscription()">測試轉錄</button>
        </div>
        
        <div class="test-section">
            <h3>測試結果</h3>
            <div id="results"></div>
        </div>
        
        <div class="test-section">
            <h3>測試說明</h3>
            <p>1. 選擇要測試的轉錄語言</p>
            <p>2. 點擊"測試轉錄"按鈕</p>
            <p>3. 系統會發送測試音頻並顯示結果</p>
            <p>4. 觀察檢測語言是否符合設定</p>
        </div>
    </div>

    <script>
        async function testTranscription() {
            const language = document.getElementById('languageSelect').value;
            const resultsDiv = document.getElementById('results');
            
            resultsDiv.innerHTML = `<div class="result">🔄 測試轉錄語言: ${language}...</div>`;
            
            try {
                // 創建一個簡單的測試音頻 (靜音)
                const audioContext = new AudioContext();
                const buffer = audioContext.createBuffer(1, 44100, 44100); // 1秒靜音
                
                // 轉換為WAV格式
                const wav = audioBufferToWav(buffer);
                const blob = new Blob([wav], { type: 'audio/wav' });
                
                // 發送轉錄請求
                const formData = new FormData();
                formData.append('audio_file', blob, 'test.wav');
                formData.append('transcription_language', language);
                
                const response = await fetch('/api/transcribe', {
                    method: 'POST',
                    body: formData
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const data = await response.json();
                
                resultsDiv.innerHTML = `
                    <div class="result">
                        <strong>✅ 轉錄成功</strong><br>
                        設定語言: ${language}<br>
                        檢測語言: ${data.detected_language}<br>
                        語言名稱: ${data.language}<br>
                        轉錄結果: ${data.transcription || '(無內容)'}<br>
                        處理時間: ${data.processing_time.toFixed(2)}秒<br>
                        模型: ${data.model_name}<br>
                        設備: ${data.device_used}
                    </div>
                `;
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="result">❌ 測試失敗: ${error.message}</div>`;
            }
        }
        
        // 簡單的音頻緩衝區轉WAV函數
        function audioBufferToWav(buffer) {
            const length = buffer.length;
            const arrayBuffer = new ArrayBuffer(44 + length * 2);
            const view = new DataView(arrayBuffer);
            
            // WAV header
            const writeString = (offset, string) => {
                for (let i = 0; i < string.length; i++) {
                    view.setUint8(offset + i, string.charCodeAt(i));
                }
            };
            
            writeString(0, 'RIFF');
            view.setUint32(4, 36 + length * 2, true);
            writeString(8, 'WAVE');
            writeString(12, 'fmt ');
            view.setUint32(16, 16, true);
            view.setUint16(20, 1, true);
            view.setUint16(22, 1, true);
            view.setUint32(24, 44100, true);
            view.setUint32(28, 44100 * 2, true);
            view.setUint16(32, 2, true);
            view.setUint16(34, 16, true);
            writeString(36, 'data');
            view.setUint32(40, length * 2, true);
            
            // 音頻數據 (靜音)
            const channelData = buffer.getChannelData(0);
            let offset = 44;
            for (let i = 0; i < length; i++) {
                view.setInt16(offset, channelData[i] * 0x7FFF, true);
                offset += 2;
            }
            
            return arrayBuffer;
        }
    </script>
</body>
</html>
