<!DOCTYPE html>
<html lang="zh-TW">
<head>
  <meta charset="UTF-8">
  <title>自適應智能轉錄系統 (自動優化)</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      color: white;
    }
    
    .container {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 20px;
      padding: 30px;
      color: #333;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }
    
    h1 {
      text-align: center;
      color: #2c3e50;
      margin-bottom: 30px;
    }
    
    .section {
      background: #f8f9fa;
      border-radius: 15px;
      padding: 25px;
      margin: 20px 0;
      border: 2px solid #e9ecef;
    }
    
    button {
      margin: 5px 10px 5px 0;
      padding: 12px 24px;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
      border: none;
      border-radius: 8px;
      transition: all 0.3s ease;
    }
    
    button.primary {
      background: linear-gradient(45deg, #28a745, #20c997);
      color: white;
    }
    
    button.danger {
      background: linear-gradient(45deg, #dc3545, #fd7e14);
      color: white;
    }
    
    button.secondary {
      background: linear-gradient(45deg, #6c757d, #495057);
      color: white;
    }
    
    button:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    
    button:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
      opacity: 0.6;
    }
    
    button.recording {
      animation: pulse 1.5s infinite;
    }
    
    @keyframes pulse {
      0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
      70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
      100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
    }
    
    select {
      margin: 5px 10px 5px 0;
      padding: 8px 12px;
      font-size: 16px;
      border: 2px solid #dee2e6;
      border-radius: 8px;
      background: white;
    }
    
    .status {
      padding: 15px;
      margin: 10px 0;
      border-radius: 8px;
      font-weight: bold;
    }
    
    .status.success {
      background: #d4edda;
      color: #155724;
    }
    
    .status.error {
      background: #f8d7da;
      color: #721c24;
    }
    
    .status.info {
      background: #d1ecf1;
      color: #0c5460;
    }
    
    .status.warning {
      background: #fff3cd;
      color: #856404;
    }
    
    .transcription-display {
      background: white;
      border: 1px solid #dee2e6;
      border-radius: 10px;
      padding: 20px;
      margin: 20px 0;
      max-height: 500px;
      overflow-y: auto;
    }
    
    .transcription-item {
      background: #f8f9fa;
      margin: 10px 0;
      padding: 15px;
      border-radius: 8px;
      border-left: 4px solid #28a745;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      transition: all 0.3s ease;
    }
    
    .transcription-item.processing {
      border-left-color: #ffc107;
      background: #fff8e1;
    }
    
    .transcription-item.filtered {
      border-left-color: #dc3545;
      background: #ffe6e6;
      opacity: 0.7;
    }
    
    .timestamp {
      font-size: 12px;
      color: #6c757d;
      margin-bottom: 5px;
    }
    
    .text-content {
      font-size: 16px;
      line-height: 1.5;
      margin-top: 8px;
    }
    
    .success-box {
      background: #d4edda;
      border: 1px solid #c3e6cb;
      padding: 15px;
      border-radius: 10px;
      margin: 15px 0;
      color: #155724;
    }
    
    .adaptive-panel {
      background: #e3f2fd;
      border: 2px solid #2196f3;
      border-radius: 15px;
      padding: 20px;
      margin: 20px 0;
    }

    .adjustment-panel {
      background: #fff3cd;
      border: 2px solid #ffc107;
      border-radius: 15px;
      padding: 20px;
      margin: 20px 0;
    }

    .adaptive-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 10px 0;
      padding: 10px;
      background: white;
      border-radius: 8px;
    }

    .control-row {
      display: flex;
      align-items: center;
      margin: 15px 0;
      flex-wrap: wrap;
      gap: 15px;
    }

    .control-row label {
      min-width: 120px;
      font-weight: bold;
    }

    .value-display {
      background: white;
      padding: 5px 10px;
      border-radius: 5px;
      border: 1px solid #ddd;
      min-width: 80px;
      text-align: center;
      font-weight: bold;
    }

    .preset-buttons {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin: 5px 0;
    }

    button.preset {
      background: linear-gradient(45deg, #007bff, #6610f2);
      color: white;
      margin: 2px;
      padding: 6px 12px;
      font-size: 12px;
    }

    input[type="range"] {
      width: 200px;
    }
    
    .audio-visualizer {
      width: 100%;
      height: 100px;
      background: #2c3e50;
      border-radius: 8px;
      margin: 10px 0;
      position: relative;
      overflow: hidden;
    }

    .volume-bar {
      height: 50%;
      background: linear-gradient(90deg, #28a745, #ffc107, #dc3545);
      width: 0%;
      transition: width 0.1s ease;
      border-radius: 8px;
      margin-top: 25%;
    }

    .threshold-indicators {
      position: absolute;
      top: 0;
      bottom: 0;
      width: 100%;
    }

    .threshold-line {
      position: absolute;
      top: 0;
      bottom: 0;
      width: 2px;
      background: #ffc107;
      box-shadow: 0 0 4px #ffc107;
    }

    .threshold-label {
      position: absolute;
      top: 5px;
      font-size: 10px;
      color: white;
      background: rgba(255, 193, 7, 0.8);
      padding: 2px 4px;
      border-radius: 3px;
      font-weight: bold;
    }
    
    .adaptive-info {
      position: absolute;
      top: 10px;
      left: 10px;
      color: white;
      font-size: 12px;
      font-weight: bold;
    }
    
    .silence-info {
      position: absolute;
      bottom: 10px;
      left: 10px;
      color: white;
      font-size: 12px;
    }
    
    .quality-indicator {
      position: absolute;
      top: 10px;
      right: 10px;
      color: white;
      font-size: 12px;
      font-weight: bold;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🤖 自適應智能轉錄系統</h1>
    
    <!-- 說明 -->
    <div class="success-box">
      <h3>🧠 全自動智能優化</h3>
      <p>此系統會自動學習和優化，無需手動調整參數：</p>
      <ul>
        <li><strong>自動環境檢測</strong>：分析音頻環境自動調整參數</li>
        <li><strong>智能內容過濾</strong>：自動過濾無意義的轉錄結果</li>
        <li><strong>動態閾值調整</strong>：根據音頻特性實時優化</li>
        <li><strong>質量評估</strong>：自動評估轉錄質量並改進</li>
      </ul>
    </div>
    
    <!-- 自適應狀態面板 -->
    <div class="adaptive-panel">
      <h3>🔧 自適應狀態監控</h3>
      <div class="adaptive-item">
        <span>環境類型：</span>
        <span id="environmentType">檢測中...</span>
      </div>
      <div class="adaptive-item">
        <span>動態閾值：</span>
        <span id="dynamicThreshold">自動調整中...</span>
      </div>
      <div class="adaptive-item">
        <span>分段策略：</span>
        <span id="segmentStrategy">學習中...</span>
      </div>
      <div class="adaptive-item">
        <span>過濾效率：</span>
        <span id="filterEfficiency">0% (0/0)</span>
      </div>
    </div>

    <!-- 手動閾值調整面板 -->
    <div class="adjustment-panel">
      <h3>🎚️ 靜音閾值手動調整</h3>

      <!-- 預設模式 -->
      <div class="control-row">
        <label>快速設定：</label>
        <div class="preset-buttons">
          <button class="preset" onclick="applyPreset('sensitive')">🟢 敏感模式</button>
          <button class="preset" onclick="applyPreset('balanced')">🟡 平衡模式</button>
          <button class="preset" onclick="applyPreset('strict')">🔴 嚴格模式</button>
        </div>
      </div>

      <!-- 音量閾值調整 -->
      <div class="control-row">
        <label>音量閾值：</label>
        <input type="range" id="volumeThreshold" min="0.001" max="0.05" step="0.001" value="0.01">
        <div class="value-display" id="volumeValue">0.01</div>
        <small>越小越敏感，捕獲更多語音</small>
      </div>

      <!-- 靜音持續時間 -->
      <div class="control-row">
        <label>靜音持續時間：</label>
        <input type="range" id="silenceDuration" min="500" max="3000" step="100" value="1000">
        <div class="value-display" id="durationValue">1000ms</div>
        <small>靜音多久後觸發分段</small>
      </div>

      <!-- 最小音頻長度 -->
      <div class="control-row">
        <label>最小音頻長度：</label>
        <input type="range" id="minAudioLength" min="1" max="10" step="1" value="3">
        <div class="value-display" id="lengthValue">3秒</div>
        <small>最短音頻片段長度</small>
      </div>

      <!-- 質量閾值 -->
      <div class="control-row">
        <label>質量閾值：</label>
        <input type="range" id="qualityThreshold" min="0.1" max="0.8" step="0.05" value="0.3">
        <div class="value-display" id="qualityValue">0.3</div>
        <small>音頻質量要求</small>
      </div>

      <!-- 最長語音時間 -->
      <div class="control-row">
        <label>最長語音時間：</label>
        <input type="range" id="maxSpeechDuration" min="10" max="60" step="5" value="30">
        <div class="value-display" id="maxSpeechValue">30秒</div>
        <small>單句話最長時間限制</small>
      </div>
    </div>
    
    <!-- 音頻監控 -->
    <div class="section">
      <h3>🔊 智能音頻監控 (可視化閾值)</h3>
      <div class="audio-visualizer">
        <div class="threshold-indicators" id="thresholdIndicators"></div>
        <div class="volume-bar" id="volumeBar"></div>
        <div class="adaptive-info" id="adaptiveInfo">自適應模式：待機</div>
        <div class="silence-info" id="silenceInfo">靜音檢測：準備中</div>
        <div class="quality-indicator" id="qualityIndicator">質量：--</div>
      </div>
      <div id="audioStats" class="status info">音頻統計：等待開始 | 黃線顯示當前靜音檢測閾值</div>
    </div>
    
    <!-- 控制 -->
    <div class="section">
      <h3>🎯 自適應轉錄控制</h3>
      
      <label for="audioQuality">音頻品質：</label>
      <select id="audioQuality">
        <option value="192000,44100">192 kbps, 44.1 kHz (推薦)</option>
        <option value="128000,44100">128 kbps, 44.1 kHz</option>
        <option value="64000,22050">64 kbps, 22.05 kHz</option>
      </select>
      <br><br>
      
      <button id="startCapture" class="primary">🤖 開始自適應轉錄</button>
      <button id="stopCapture" class="danger" disabled>⏹️ 停止轉錄</button>
      <button id="toggleFiltering" class="secondary">🔍 切換過濾模式</button>
      
      <div id="captureStatus" class="status info">點擊開始自適應轉錄</div>
    </div>
    
    <!-- 轉錄結果 -->
    <div class="section">
      <h3>📝 智能轉錄結果</h3>
      <button onclick="clearTranscriptions()" class="secondary">🗑️ 清空</button>
      <button onclick="exportTranscriptions()" class="secondary">💾 匯出</button>
      <button onclick="copyValidText()" class="secondary">📋 複製有效內容</button>
      <button onclick="showFilteredItems()" class="secondary" id="showFilteredBtn">👁️ 顯示已過濾</button>
      
      <div class="transcription-display" id="transcriptionDisplay">
        <div class="transcription-item">
          <div class="timestamp">等待中...</div>
          <div class="text-content">自適應轉錄結果將在此顯示，系統會自動過濾無效內容</div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 全局變量
    let capturedStream, micStream, audioContext;
    let isRecording = false;
    let audioBuffer = [];
    let currentSegmentId = 0;
    let showFiltered = false;
    let enableFiltering = true;
    
    // 自適應參數 (加入手動調整)
    let adaptiveParams = {
      silenceThreshold: 0.01,        // 可手動調整
      silenceDuration: 1000,         // 可手動調整
      minAudioLength: 3,             // 可手動調整
      maxAudioLength: 30,            // 可手動調整
      maxSpeechDuration: 30,         // 最長語音時間 (新增)
      qualityThreshold: 0.3,         // 可手動調整
      environmentType: 'unknown',
      avgVolume: 0,
      volumeHistory: [],
      segmentQuality: [],
      manualOverride: false,         // 是否手動覆蓋自適應
      voiceStartTime: null,          // 語音開始時間
      lastVoiceTime: null            // 最後語音時間
    };

    // 預設模式配置
    const presets = {
      sensitive: {
        silenceThreshold: 0.005,
        silenceDuration: 800,
        minAudioLength: 2,
        maxSpeechDuration: 25,
        qualityThreshold: 0.2
      },
      balanced: {
        silenceThreshold: 0.01,
        silenceDuration: 1000,
        minAudioLength: 3,
        maxSpeechDuration: 30,
        qualityThreshold: 0.3
      },
      strict: {
        silenceThreshold: 0.02,
        silenceDuration: 1500,
        minAudioLength: 4,
        maxSpeechDuration: 20,
        qualityThreshold: 0.5
      }
    };
    
    // 統計數據
    let stats = {
      totalSegments: 0,
      validSegments: 0,
      filteredSegments: 0,
      avgConfidence: 0
    };

    // 初始化
    window.onload = function() {
      setupControllers();
      applyPreset('balanced');
      updateThresholdIndicators();
    };

    // 設置控制器
    function setupControllers() {
      document.getElementById('volumeThreshold').addEventListener('input', updateManualParams);
      document.getElementById('silenceDuration').addEventListener('input', updateManualParams);
      document.getElementById('minAudioLength').addEventListener('input', updateManualParams);
      document.getElementById('maxSpeechDuration').addEventListener('input', updateManualParams);
      document.getElementById('qualityThreshold').addEventListener('input', updateManualParams);
    }

    // 更新手動參數
    function updateManualParams() {
      adaptiveParams.silenceThreshold = parseFloat(document.getElementById('volumeThreshold').value);
      adaptiveParams.silenceDuration = parseInt(document.getElementById('silenceDuration').value);
      adaptiveParams.minAudioLength = parseInt(document.getElementById('minAudioLength').value);
      adaptiveParams.maxSpeechDuration = parseInt(document.getElementById('maxSpeechDuration').value);
      adaptiveParams.qualityThreshold = parseFloat(document.getElementById('qualityThreshold').value);
      adaptiveParams.manualOverride = true;

      updateValueDisplays();
      updateThresholdIndicators();
    }

    // 更新數值顯示
    function updateValueDisplays() {
      document.getElementById('volumeValue').textContent = adaptiveParams.silenceThreshold.toFixed(3);
      document.getElementById('durationValue').textContent = adaptiveParams.silenceDuration + 'ms';
      document.getElementById('lengthValue').textContent = adaptiveParams.minAudioLength + '秒';
      document.getElementById('maxSpeechValue').textContent = adaptiveParams.maxSpeechDuration + '秒';
      document.getElementById('qualityValue').textContent = adaptiveParams.qualityThreshold.toFixed(1);
    }

    // 應用預設模式
    function applyPreset(presetName) {
      const preset = presets[presetName];

      document.getElementById('volumeThreshold').value = preset.silenceThreshold;
      document.getElementById('silenceDuration').value = preset.silenceDuration;
      document.getElementById('minAudioLength').value = preset.minAudioLength;
      document.getElementById('maxSpeechDuration').value = preset.maxSpeechDuration;
      document.getElementById('qualityThreshold').value = preset.qualityThreshold;

      updateManualParams();

      const modeNames = {
        sensitive: '敏感模式 - 捕獲更多語音 (最長25秒)',
        balanced: '平衡模式 - 推薦設定 (最長30秒)',
        strict: '嚴格模式 - 高質量過濾 (最長20秒)'
      };

      document.getElementById('audioStats').innerHTML =
        `<div class="status info">✅ 已套用${modeNames[presetName]} | 黃線顯示當前靜音檢測閾值</div>`;
    }

    // 更新閾值指示器
    function updateThresholdIndicators() {
      const indicators = document.getElementById('thresholdIndicators');
      const thresholdPercent = Math.min(adaptiveParams.silenceThreshold * 2000, 100);

      indicators.innerHTML = `
        <div class="threshold-line" style="left: ${thresholdPercent}%"></div>
        <div class="threshold-label" style="left: ${thresholdPercent}%">閾值: ${adaptiveParams.silenceThreshold.toFixed(3)}</div>
      `;
    }

    const startCaptureButton = document.getElementById('startCapture');
    const stopCaptureButton = document.getElementById('stopCapture');
    const audioQualitySelect = document.getElementById('audioQuality');

    // 切換過濾模式
    document.getElementById('toggleFiltering').addEventListener('click', () => {
      enableFiltering = !enableFiltering;
      const btn = document.getElementById('toggleFiltering');
      btn.textContent = enableFiltering ? '🔍 過濾模式：開啟' : '🔍 過濾模式：關閉';
      btn.className = enableFiltering ? 'secondary' : 'warning';
    });

    // 顯示/隱藏已過濾項目
    document.getElementById('showFilteredBtn').addEventListener('click', showFilteredItems);

    // 開始自適應轉錄
    startCaptureButton.addEventListener('click', async () => {
      try {
        const statusDiv = document.getElementById('captureStatus');
        statusDiv.innerHTML = '<div class="status info">🔄 啟動自適應轉錄系統...</div>';
        
        const [audioBitRate, audioSampleRate] = audioQualitySelect.value.split(',').map(Number);

        // 捕捉音頻
        capturedStream = await navigator.mediaDevices.getDisplayMedia({
          video: { width: { ideal: 1280 }, height: { ideal: 720 }, frameRate: { ideal: 30 } },
          audio: {
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false,
            sampleRate: audioSampleRate,
            channelCount: 2
          }
        });

        micStream = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false
          }
        });

        // 建立音頻混合
        audioContext = new AudioContext();
        const destination = audioContext.createMediaStreamDestination();

        if (capturedStream.getAudioTracks().length > 0) {
          const systemAudioSource = audioContext.createMediaStreamSource(capturedStream);
          systemAudioSource.connect(destination);
        }
        if (micStream.getAudioTracks().length > 0) {
          const micAudioSource = audioContext.createMediaStreamSource(micStream);
          micAudioSource.connect(destination);
        }

        // 設置自適應處理
        setupAdaptiveProcessing(destination.stream);

        startCaptureButton.disabled = true;
        stopCaptureButton.disabled = false;
        startCaptureButton.classList.add('recording');
        
        statusDiv.innerHTML = '<div class="status success">🤖 自適應轉錄中 (智能優化)</div>';
        isRecording = true;

        // 開始環境學習
        startEnvironmentLearning();

        capturedStream.getTracks().forEach(track => {
          track.onended = () => stopCapture();
        });

      } catch (err) {
        console.error('自適應轉錄啟動失敗:', err);
        document.getElementById('captureStatus').innerHTML = 
          `<div class="status error">❌ 啟動失敗：${err.message}</div>`;
      }
    });

    // 停止轉錄
    stopCaptureButton.addEventListener('click', stopCapture);

    function stopCapture() {
      isRecording = false;
      
      if (audioBuffer.length > 0) {
        processRemainingAudio();
      }
      
      [capturedStream, micStream].forEach(stream => {
        if (stream) {
          stream.getTracks().forEach(track => track.stop());
        }
      });
      
      if (audioContext) {
        audioContext.close();
        audioContext = null;
      }
      
      startCaptureButton.disabled = false;
      stopCaptureButton.disabled = true;
      startCaptureButton.classList.remove('recording');
      
      document.getElementById('captureStatus').innerHTML = 
        '<div class="status info">⏹️ 自適應轉錄已停止</div>';
      
      updateAdaptiveStatus();
    }

    // 設置自適應處理
    function setupAdaptiveProcessing(mixedStream) {
      const source = audioContext.createMediaStreamSource(mixedStream);
      const processor = audioContext.createScriptProcessor(4096, 1, 1);

      processor.onaudioprocess = function(event) {
        if (!isRecording) return;
        
        const audioData = event.inputBuffer.getChannelData(0);
        processAdaptiveAudio(audioData);
      };

      source.connect(processor);
      processor.connect(audioContext.destination);
    }

    // 自適應音頻處理 (加強靜音檢測)
    function processAdaptiveAudio(audioData) {
      // 多重音頻分析
      const analysis = analyzeAudioSegment(audioData);

      // 更新音量歷史
      adaptiveParams.volumeHistory.push(analysis.rms);
      if (adaptiveParams.volumeHistory.length > 1000) {
        adaptiveParams.volumeHistory.shift();
      }

      // 動態調整閾值 (如果沒有手動覆蓋)
      if (!adaptiveParams.manualOverride) {
        updateDynamicThreshold();
      }

      // 更新視覺化
      updateAdaptiveVisualization(analysis);

      // 嚴格的語音活動檢測
      const isVoiceActive = detectVoiceActivity(analysis);

      if (isVoiceActive) {
        // 檢測到語音，添加到緩衝區
        audioBuffer.push(...audioData);
        adaptiveParams.lastVoiceTime = Date.now();

        if (!adaptiveParams.voiceStartTime) {
          adaptiveParams.voiceStartTime = Date.now();
        }

        // 檢查是否超過最長語音時間限制
        const currentSpeechDuration = (Date.now() - adaptiveParams.voiceStartTime) / 1000;
        if (currentSpeechDuration >= adaptiveParams.maxSpeechDuration) {
          console.log(`⏰ 達到最長語音時間限制: ${currentSpeechDuration.toFixed(1)}秒，強制分段`);
          processAudioSegment();
          adaptiveParams.voiceStartTime = Date.now(); // 重新開始計時
        }

      } else if (audioBuffer.length > 0) {
        // 沒有語音但有緩衝區內容，檢查是否應該處理
        const silenceDuration = Date.now() - (adaptiveParams.lastVoiceTime || Date.now());
        const bufferDurationMs = (audioBuffer.length / 44100) * 1000;

        if (silenceDuration >= adaptiveParams.silenceDuration &&
            bufferDurationMs >= adaptiveParams.minAudioLength * 1000) {
          processAudioSegment();
          adaptiveParams.voiceStartTime = null;
        } else if (bufferDurationMs >= adaptiveParams.maxAudioLength * 1000) {
          // 強制分段避免過長
          processAudioSegment();
          adaptiveParams.voiceStartTime = null;
        }
      }
    }

    // 分析音頻片段 (多重指標)
    function analyzeAudioSegment(audioData) {
      let sum = 0;
      let energy = 0;
      let zeroCrossings = 0;
      let maxAmplitude = 0;

      for (let i = 0; i < audioData.length; i++) {
        const sample = Math.abs(audioData[i]);
        sum += audioData[i] * audioData[i];
        energy += sample;
        maxAmplitude = Math.max(maxAmplitude, sample);

        if (i > 0 && audioData[i] * audioData[i-1] < 0) {
          zeroCrossings++;
        }
      }

      const rms = Math.sqrt(sum / audioData.length);
      const avgEnergy = energy / audioData.length;
      const zcr = zeroCrossings / audioData.length;

      return {
        rms: rms,
        energy: avgEnergy,
        zcr: zcr,
        maxAmplitude: maxAmplitude,
        length: audioData.length
      };
    }

    // 嚴格的語音活動檢測
    function detectVoiceActivity(analysis) {
      // 多重檢測條件
      const volumeCheck = analysis.rms > adaptiveParams.silenceThreshold;
      const energyCheck = analysis.energy > adaptiveParams.silenceThreshold * 0.5;
      const amplitudeCheck = analysis.maxAmplitude > adaptiveParams.silenceThreshold * 2;
      const zcrCheck = analysis.zcr > 0.01 && analysis.zcr < 0.15; // 合理的過零率範圍
      const basicCheck = analysis.rms > 0.002; // 基本音量檢查

      // 至少滿足3個條件才認為是語音
      const conditions = [volumeCheck, energyCheck, amplitudeCheck, zcrCheck, basicCheck];
      const passedConditions = conditions.filter(c => c).length;

      return passedConditions >= 3;
    }

    // 動態調整閾值
    function updateDynamicThreshold() {
      if (adaptiveParams.volumeHistory.length < 100) return;
      
      const recent = adaptiveParams.volumeHistory.slice(-100);
      const avg = recent.reduce((a, b) => a + b, 0) / recent.length;
      const std = Math.sqrt(recent.reduce((a, b) => a + (b - avg) ** 2, 0) / recent.length);
      
      // 動態調整閾值
      adaptiveParams.silenceThreshold = Math.max(0.005, avg - std * 1.5);
      adaptiveParams.avgVolume = avg;
      
      // 環境類型檢測
      if (avg < 0.01 && std < 0.005) {
        adaptiveParams.environmentType = '安靜環境';
        adaptiveParams.silenceDuration = 800;
      } else if (avg > 0.05 || std > 0.02) {
        adaptiveParams.environmentType = '嘈雜環境';
        adaptiveParams.silenceDuration = 1500;
      } else {
        adaptiveParams.environmentType = '一般環境';
        adaptiveParams.silenceDuration = 1000;
      }
    }

    // 智能分段判斷
    function shouldSegmentAudio(rms, bufferDurationMs) {
      const isQuiet = rms < adaptiveParams.silenceThreshold;
      const minDuration = adaptiveParams.minAudioLength * 1000;
      const maxDuration = adaptiveParams.maxAudioLength * 1000;
      
      // 強制分段（避免過長）
      if (bufferDurationMs >= maxDuration) {
        return true;
      }
      
      // 靜音分段
      if (isQuiet && bufferDurationMs >= minDuration) {
        return true;
      }
      
      return false;
    }

    // 處理音頻段 (加強質量檢查)
    function processAudioSegment() {
      if (audioBuffer.length === 0) return;

      const segmentId = ++currentSegmentId;
      const duration = (audioBuffer.length / 44100).toFixed(1);

      // 最終音頻質量評估
      const finalAnalysis = analyzeAudioSegment(new Float32Array(audioBuffer));
      const qualityScore = calculateAudioQuality(finalAnalysis);

      // 嚴格的質量檢查
      if (enableFiltering && qualityScore < adaptiveParams.qualityThreshold) {
        // 低質量音頻，直接過濾
        stats.filteredSegments++;
        addFilteredTranscription(segmentId, duration, `音頻質量過低 (${(qualityScore*100).toFixed(0)}%)`);
        console.log(`🚫 拒絕低質量音頻段: ${duration}秒, 質量: ${(qualityScore*100).toFixed(0)}%`);
      } else if (enableFiltering && !detectVoiceActivity(finalAnalysis)) {
        // 最終檢查仍無語音活動
        stats.filteredSegments++;
        addFilteredTranscription(segmentId, duration, '未檢測到有效語音活動');
        console.log(`🚫 拒絕無語音活動段: ${duration}秒`);
      } else {
        // 質量合格，進行轉錄
        const audioBlob = createWavBlob(new Float32Array(audioBuffer), 44100);
        addProcessingTranscription(segmentId, duration, qualityScore);
        sendAudioForTranscription(audioBlob, segmentId, finalAnalysis);
        stats.validSegments++;
        console.log(`✅ 處理音頻段: ${duration}秒, 質量: ${(qualityScore*100).toFixed(0)}%`);
      }

      audioBuffer = [];
      stats.totalSegments++;
    }

    // 評估音頻質量
    function evaluateAudioQuality(buffer) {
      let energy = 0;
      let zeroCrossings = 0;
      
      for (let i = 0; i < buffer.length; i++) {
        energy += buffer[i] * buffer[i];
        if (i > 0 && buffer[i] * buffer[i-1] < 0) {
          zeroCrossings++;
        }
      }
      
      const rms = Math.sqrt(energy / buffer.length);
      const zcr = zeroCrossings / buffer.length;
      
      // 質量評分 (0-1)
      let score = 0;
      if (rms > 0.005) score += 0.4; // 有足夠音量
      if (rms < 0.1) score += 0.2;   // 不過載
      if (zcr > 0.01 && zcr < 0.1) score += 0.4; // 合理的頻率變化
      
      return {
        score: score,
        rms: rms,
        zcr: zcr,
        quality: score > 0.7 ? '高' : score > 0.4 ? '中' : '低'
      };
    }

    // 開始環境學習
    function startEnvironmentLearning() {
      setInterval(() => {
        if (isRecording) {
          updateAdaptiveStatus();
        }
      }, 2000);
    }

    // 更新自適應狀態
    function updateAdaptiveStatus() {
      document.getElementById('environmentType').textContent = adaptiveParams.environmentType;
      document.getElementById('dynamicThreshold').textContent = adaptiveParams.silenceThreshold.toFixed(4);
      document.getElementById('segmentStrategy').textContent = 
        `${adaptiveParams.minAudioLength}-${adaptiveParams.maxAudioLength}秒 / ${adaptiveParams.silenceDuration}ms`;
      
      const efficiency = stats.totalSegments > 0 ? 
        ((stats.filteredSegments / stats.totalSegments) * 100).toFixed(1) : 0;
      document.getElementById('filterEfficiency').textContent = 
        `${efficiency}% (${stats.filteredSegments}/${stats.totalSegments})`;
    }

    // 更新自適應視覺化 (加入閾值線顯示)
    function updateAdaptiveVisualization(analysis) {
      const volumeBar = document.getElementById('volumeBar');
      const adaptiveInfo = document.getElementById('adaptiveInfo');
      const silenceInfo = document.getElementById('silenceInfo');
      const qualityIndicator = document.getElementById('qualityIndicator');

      // 更新音量條
      const volumePercent = Math.min(analysis.rms * 2000, 100);
      volumeBar.style.width = volumePercent + '%';

      // 更新閾值指示器
      updateThresholdIndicators();

      // 語音活動狀態
      const isVoiceActive = detectVoiceActivity(analysis);
      if (isVoiceActive) {
        adaptiveInfo.textContent = '🎤 語音活躍';
        adaptiveInfo.style.color = '#28a745';
      } else {
        adaptiveInfo.textContent = '🔇 靜音中';
        adaptiveInfo.style.color = '#ffc107';
      }

      // 詳細信息
      silenceInfo.textContent = `閾值：${adaptiveParams.silenceThreshold.toFixed(4)} | 音量：${analysis.rms.toFixed(4)} | 能量：${analysis.energy.toFixed(4)}`;

      // 質量評估
      const qualityScore = calculateAudioQuality(analysis);
      const qualityText = qualityScore > adaptiveParams.qualityThreshold ? '優良' : '偏低';
      qualityIndicator.textContent = `質量：${qualityText} (${(qualityScore*100).toFixed(0)}%)`;
      qualityIndicator.style.color = qualityScore > adaptiveParams.qualityThreshold ? '#28a745' : '#dc3545';
    }

    // 計算音頻質量評分
    function calculateAudioQuality(analysis) {
      let score = 0;

      // 音量評分 (0-0.4)
      if (analysis.rms > 0.01) score += 0.4;
      else if (analysis.rms > 0.005) score += 0.3;
      else if (analysis.rms > 0.002) score += 0.2;

      // 能量評分 (0-0.3)
      if (analysis.energy > 0.005) score += 0.3;
      else if (analysis.energy > 0.002) score += 0.2;
      else if (analysis.energy > 0.001) score += 0.1;

      // 過零率評分 (0-0.2)
      if (analysis.zcr > 0.02 && analysis.zcr < 0.1) score += 0.2;
      else if (analysis.zcr > 0.01 && analysis.zcr < 0.15) score += 0.1;

      // 振幅評分 (0-0.1)
      if (analysis.maxAmplitude > 0.02) score += 0.1;

      return Math.min(score, 1.0);
    }

    // 處理剩餘音頻
    function processRemainingAudio() {
      if (audioBuffer.length > 0) {
        const duration = (audioBuffer.length / 44100).toFixed(1);
        if (parseFloat(duration) >= 1.0) {
          processAudioSegment();
        }
      }
    }

    // 創建 WAV Blob
    function createWavBlob(audioData, sampleRate) {
      const length = audioData.length;
      const buffer = new ArrayBuffer(44 + length * 2);
      const view = new DataView(buffer);
      
      const writeString = (offset, string) => {
        for (let i = 0; i < string.length; i++) {
          view.setUint8(offset + i, string.charCodeAt(i));
        }
      };
      
      writeString(0, 'RIFF');
      view.setUint32(4, 36 + length * 2, true);
      writeString(8, 'WAVE');
      writeString(12, 'fmt ');
      view.setUint32(16, 16, true);
      view.setUint16(20, 1, true);
      view.setUint16(22, 1, true);
      view.setUint32(24, sampleRate, true);
      view.setUint32(28, sampleRate * 2, true);
      view.setUint16(32, 2, true);
      view.setUint16(34, 16, true);
      writeString(36, 'data');
      view.setUint32(40, length * 2, true);
      
      let offset = 44;
      for (let i = 0; i < length; i++) {
        const sample = Math.max(-1, Math.min(1, audioData[i]));
        view.setInt16(offset, sample * 0x7FFF, true);
        offset += 2;
      }
      
      return new Blob([buffer], { type: 'audio/wav' });
    }

    // 添加處理中轉錄
    function addProcessingTranscription(segmentId, duration, qualityScore) {
      const display = document.getElementById('transcriptionDisplay');
      const timestamp = new Date().toLocaleTimeString();

      const item = document.createElement('div');
      item.className = 'transcription-item processing';
      item.id = `segment-${segmentId}`;
      item.innerHTML = `
        <div class="timestamp">${timestamp} - 段落 ${segmentId} (${duration}秒) [質量: ${(qualityScore * 100).toFixed(0)}%]</div>
        <div class="text-content">🔄 智能轉錄中...</div>
      `;

      display.appendChild(item);
      display.scrollTop = display.scrollHeight;
    }

    // 添加已過濾轉錄
    function addFilteredTranscription(segmentId, duration, reason) {
      const display = document.getElementById('transcriptionDisplay');
      const timestamp = new Date().toLocaleTimeString();

      const item = document.createElement('div');
      item.className = 'transcription-item filtered';
      item.id = `segment-${segmentId}`;
      item.style.display = showFiltered ? 'block' : 'none';
      item.innerHTML = `
        <div class="timestamp">${timestamp} - 段落 ${segmentId} (${duration}秒) [已過濾]</div>
        <div class="text-content">🚫 ${reason}</div>
      `;

      display.appendChild(item);
      display.scrollTop = display.scrollHeight;
    }

    // 發送轉錄
    async function sendAudioForTranscription(audioBlob, segmentId, analysis) {
      try {
        console.log(`🚀 發送音頻進行轉錄 - 段落${segmentId}, 大小: ${audioBlob.size} bytes`);

        const formData = new FormData();
        formData.append('audio_file', audioBlob, `adaptive_${segmentId}.wav`);

        const response = await fetch('/api/transcribe', {
          method: 'POST',
          body: formData
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`);
        }

        const data = await response.json();
        console.log(`📥 收到轉錄回應 - 段落${segmentId}: ${data.transcription}`);

        // 智能內容過濾
        const filteredResult = intelligentContentFilter(data.transcription || '', analysis);
        console.log(`🔍 過濾結果 - 段落${segmentId}: ${filteredResult.text} (有效: ${filteredResult.isValid})`);

        updateTranscriptionResult(segmentId, filteredResult.text, filteredResult.isValid);

      } catch (error) {
        console.error('轉錄錯誤:', error);
        updateTranscriptionResult(segmentId, `❌ 轉錄失敗: ${error.message}`, false);
      }
    }

    // 智能內容過濾
    function intelligentContentFilter(text, analysis) {
      if (!text || text.trim().length === 0) {
        return { text: '🔇 無語音內容', isValid: false };
      }

      const cleanText = text.trim();

      // 過濾條件
      const filters = [
        { test: t => t.length < 3, reason: '內容過短' },
        { test: t => /^[^\w\u4e00-\u9fff]*$/.test(t), reason: '無有效文字' },
        { test: t => /^(呃|嗯|啊|哦|唔){2,}$/.test(t), reason: '僅含語助詞' },
        { test: t => t.split('').every(c => c === t[0]), reason: '重複字符' },
        { test: t => /^(.+?)\1{3,}/.test(t), reason: '重複內容' },
        { test: t => /^我认识你/.test(t) && t.length > 20, reason: '無意義重複' }
      ];

      for (const filter of filters) {
        if (enableFiltering && filter.test(cleanText)) {
          return { text: `🚫 已過濾：${filter.reason}`, isValid: false };
        }
      }

      return { text: cleanText, isValid: true };
    }

    // 更新轉錄結果
    function updateTranscriptionResult(segmentId, transcription, isValid) {
      console.log(`📝 更新轉錄結果 - 段落${segmentId}: ${transcription} (有效: ${isValid})`);

      const item = document.getElementById(`segment-${segmentId}`);
      if (item) {
        const textContent = item.querySelector('.text-content');
        textContent.textContent = transcription;
        item.classList.remove('processing');

        if (!isValid) {
          item.classList.add('filtered');
          item.style.display = showFiltered ? 'block' : 'none';
        }

        console.log(`✅ 轉錄結果已更新到畫面`);
      } else {
        console.error(`❌ 找不到段落元素: segment-${segmentId}`);
      }
    }

    // 顯示/隱藏已過濾項目
    function showFilteredItems() {
      showFiltered = !showFiltered;
      const filteredItems = document.querySelectorAll('.transcription-item.filtered');
      const btn = document.getElementById('showFilteredBtn');
      
      filteredItems.forEach(item => {
        item.style.display = showFiltered ? 'block' : 'none';
      });
      
      btn.textContent = showFiltered ? '👁️ 隱藏已過濾' : '👁️ 顯示已過濾';
    }

    // 清空轉錄
    function clearTranscriptions() {
      document.getElementById('transcriptionDisplay').innerHTML = '';
      stats = { totalSegments: 0, validSegments: 0, filteredSegments: 0, avgConfidence: 0 };
      currentSegmentId = 0;
    }

    // 匯出轉錄
    function exportTranscriptions() {
      const display = document.getElementById('transcriptionDisplay');
      const validItems = display.querySelectorAll('.transcription-item:not(.filtered):not(.processing)');

      let exportText = '自適應智能轉錄記錄\n================\n\n';

      validItems.forEach(item => {
        const timestamp = item.querySelector('.timestamp').textContent;
        const textContent = item.querySelector('.text-content').textContent;
        if (!textContent.includes('轉錄失敗') && !textContent.includes('已過濾')) {
          exportText += `${timestamp}\n${textContent}\n\n`;
        }
      });

      const blob = new Blob([exportText], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `adaptive_transcription_${new Date().toISOString().slice(0, 19)}.txt`;
      a.click();
      URL.revokeObjectURL(url);
    }

    // 複製有效內容
    function copyValidText() {
      const display = document.getElementById('transcriptionDisplay');
      const validItems = display.querySelectorAll('.transcription-item:not(.filtered):not(.processing)');

      let copyText = '';
      validItems.forEach(item => {
        const textContent = item.querySelector('.text-content').textContent;
        if (!textContent.includes('轉錄失敗') && !textContent.includes('已過濾') && 
            !textContent.includes('轉錄中')) {
          copyText += textContent + '\n';
        }
      });

      navigator.clipboard.writeText(copyText).then(() => {
        alert(`已複製 ${copyText.split('\n').filter(line => line.trim()).length} 條有效轉錄內容`);
      });
    }
  </script>
</body>
</html>
