<!DOCTYPE html>
<!-- saved from url=(0056)http://localhost:9004/static/adaptive_transcription.html -->
<html lang="zh-TW"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  
  <title>自適應智能轉錄系統 (自動優化)</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      color: white;
    }
    
    .container {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 20px;
      padding: 30px;
      color: #333;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }
    
    h1 {
      text-align: center;
      color: #2c3e50;
      margin-bottom: 30px;
    }
    
    .section {
      background: #f8f9fa;
      border-radius: 15px;
      padding: 25px;
      margin: 20px 0;
      border: 2px solid #e9ecef;
    }
    
    button {
      margin: 5px 10px 5px 0;
      padding: 12px 24px;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
      border: none;
      border-radius: 8px;
      transition: all 0.3s ease;
    }
    
    button.primary {
      background: linear-gradient(45deg, #28a745, #20c997);
      color: white;
    }
    
    button.danger {
      background: linear-gradient(45deg, #dc3545, #fd7e14);
      color: white;
    }
    
    button.secondary {
      background: linear-gradient(45deg, #6c757d, #495057);
      color: white;
    }
    
    button:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    
    button:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
      opacity: 0.6;
    }
    
    button.recording {
      animation: pulse 1.5s infinite;
    }
    
    @keyframes pulse {
      0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
      70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
      100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
    }
    
    select {
      margin: 5px 10px 5px 0;
      padding: 8px 12px;
      font-size: 16px;
      border: 2px solid #dee2e6;
      border-radius: 8px;
      background: white;
    }
    
    .status {
      padding: 15px;
      margin: 10px 0;
      border-radius: 8px;
      font-weight: bold;
    }
    
    .status.success {
      background: #d4edda;
      color: #155724;
    }
    
    .status.error {
      background: #f8d7da;
      color: #721c24;
    }
    
    .status.info {
      background: #d1ecf1;
      color: #0c5460;
    }
    
    .status.warning {
      background: #fff3cd;
      color: #856404;
    }
    
    .transcription-display {
      background: white;
      border: 1px solid #dee2e6;
      border-radius: 10px;
      padding: 20px;
      margin: 20px 0;
      max-height: 500px;
      overflow-y: auto;
    }
    
    .transcription-item {
      background: #f8f9fa;
      margin: 10px 0;
      padding: 15px;
      border-radius: 8px;
      border-left: 4px solid #28a745;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      transition: all 0.3s ease;
    }
    
    .transcription-item.processing {
      border-left-color: #ffc107;
      background: #fff8e1;
    }
    
    .transcription-item.filtered {
      border-left-color: #dc3545;
      background: #ffe6e6;
      opacity: 0.7;
    }
    
    .timestamp {
      font-size: 12px;
      color: #6c757d;
      margin-bottom: 5px;
    }
    
    .text-content {
      font-size: 16px;
      line-height: 1.5;
      margin-top: 8px;
    }
    
    .success-box {
      background: #d4edda;
      border: 1px solid #c3e6cb;
      padding: 15px;
      border-radius: 10px;
      margin: 15px 0;
      color: #155724;
    }
    
    .adaptive-panel {
      background: #e3f2fd;
      border: 2px solid #2196f3;
      border-radius: 15px;
      padding: 20px;
      margin: 20px 0;
    }
    
    .adaptive-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 10px 0;
      padding: 10px;
      background: white;
      border-radius: 8px;
    }
    
    .audio-visualizer {
      width: 100%;
      height: 80px;
      background: #2c3e50;
      border-radius: 8px;
      margin: 10px 0;
      position: relative;
      overflow: hidden;
    }
    
    .volume-bar {
      height: 100%;
      background: linear-gradient(90deg, #28a745, #ffc107, #dc3545);
      width: 0%;
      transition: width 0.1s ease;
      border-radius: 8px;
    }
    
    .adaptive-info {
      position: absolute;
      top: 10px;
      left: 10px;
      color: white;
      font-size: 12px;
      font-weight: bold;
    }
    
    .silence-info {
      position: absolute;
      bottom: 10px;
      left: 10px;
      color: white;
      font-size: 12px;
    }
    
    .quality-indicator {
      position: absolute;
      top: 10px;
      right: 10px;
      color: white;
      font-size: 12px;
      font-weight: bold;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🤖 自適應智能轉錄系統</h1>
    
    <!-- 說明 -->
    <div class="success-box">
      <h3>🧠 全自動智能優化</h3>
      <p>此系統會自動學習和優化，無需手動調整參數：</p>
      <ul>
        <li><strong>自動環境檢測</strong>：分析音頻環境自動調整參數</li>
        <li><strong>智能內容過濾</strong>：自動過濾無意義的轉錄結果</li>
        <li><strong>動態閾值調整</strong>：根據音頻特性實時優化</li>
        <li><strong>質量評估</strong>：自動評估轉錄質量並改進</li>
      </ul>
    </div>
    
    <!-- 自適應狀態面板 -->
    <div class="adaptive-panel">
      <h3>🔧 自適應狀態監控</h3>
      <div class="adaptive-item">
        <span>環境類型：</span>
        <span id="environmentType">嘈雜環境</span>
      </div>
      <div class="adaptive-item">
        <span>動態閾值：</span>
        <span id="dynamicThreshold">0.0050</span>
      </div>
      <div class="adaptive-item">
        <span>分段策略：</span>
        <span id="segmentStrategy">3-30秒 / 1500ms</span>
      </div>
      <div class="adaptive-item">
        <span>過濾效率：</span>
        <span id="filterEfficiency">9.2% (33/359)</span>
      </div>
    </div>
    
    <!-- 音頻監控 -->
    <div class="section">
      <h3>🔊 智能音頻監控</h3>
      <div class="audio-visualizer">
        <div class="volume-bar" id="volumeBar" style="width: 100%;"></div>
        <div class="adaptive-info" id="adaptiveInfo">自適應：嘈雜環境</div>
        <div class="silence-info" id="silenceInfo">閾值：0.0050 | 音量：0.1119</div>
        <div class="quality-indicator" id="qualityIndicator">狀態：良好</div>
      </div>
    </div>
    
    <!-- 控制 -->
    <div class="section">
      <h3>🎯 自適應轉錄控制</h3>
      
      <label for="audioQuality">音頻品質：</label>
      <select id="audioQuality">
        <option value="192000,44100">192 kbps, 44.1 kHz (推薦)</option>
        <option value="128000,44100">128 kbps, 44.1 kHz</option>
        <option value="64000,22050">64 kbps, 22.05 kHz</option>
      </select>
      <br><br>
      
      <button id="startCapture" class="primary">🤖 開始自適應轉錄</button>
      <button id="stopCapture" class="danger" disabled="">⏹️ 停止轉錄</button>
      <button id="toggleFiltering" class="secondary">🔍 過濾模式：開啟</button>
      
      <div id="captureStatus" class="status info"><div class="status info">⏹️ 自適應轉錄已停止</div></div>
    </div>
    
    <!-- 轉錄結果 -->
    <div class="section">
      <h3>📝 智能轉錄結果</h3>
      <button onclick="clearTranscriptions()" class="secondary">🗑️ 清空</button>
      <button onclick="exportTranscriptions()" class="secondary">💾 匯出</button>
      <button onclick="copyValidText()" class="secondary">📋 複製有效內容</button>
      <button onclick="showFilteredItems()" class="secondary" id="showFilteredBtn">👁️ 顯示已過濾</button>
      
      <div class="transcription-display" id="transcriptionDisplay"><div class="transcription-item" id="segment-1">
        <div class="timestamp">下午1:41:47 - 段落 1 (11.7秒) [質量: 80%]</div>
        <div class="text-content">他沒有局限 說你是表就大家動氣腦筋了就跟說連法科的那個騰騎捨路犬到了深圳之後他們就把手機搞出各式這樣的樣子對 因為</div>
      </div><div class="transcription-item" id="segment-2">
        <div class="timestamp">下午1:41:50 - 段落 2 (3.6秒) [質量: 80%]</div>
        <div class="text-content">所以大家開始注意</div>
      </div><div class="transcription-item" id="segment-3">
        <div class="timestamp">下午1:41:53 - 段落 3 (3.6秒) [質量: 100%]</div>
        <div class="text-content">我不記得Exactly是什麼時間</div>
      </div><div class="transcription-item" id="segment-4">
        <div class="timestamp">下午1:41:56 - 段落 4 (3.4秒) [質量: 100%]</div>
        <div class="text-content">可能是孔鄉重新送我那邊書是</div>
      </div><div class="transcription-item" id="segment-5">
        <div class="timestamp">下午1:42:02 - 段落 5 (5.8秒) [質量: 100%]</div>
        <div class="text-content">我們眾人願意願意是孔翔聰對 他那時候是清華的校友</div>
      </div><div class="transcription-item" id="segment-6">
        <div class="timestamp">下午1:42:06 - 段落 6 (4.4秒) [質量: 80%]</div>
        <div class="text-content">他人家就算我們台灣現在也有</div>
      </div><div class="transcription-item" id="segment-7">
        <div class="timestamp">下午1:42:09 - 段落 7 (3.3秒) [質量: 100%]</div>
        <div class="text-content">做電競技,做技體電路</div>
      </div><div class="transcription-item" id="segment-8">
        <div class="timestamp">下午1:42:12 - 段落 8 (3.1秒) [質量: 100%]</div>
        <div class="text-content">他就不知道使用安排來參觀</div>
      </div><div class="transcription-item" id="segment-9">
        <div class="timestamp">下午1:42:14 - 段落 9 (2.1秒) [質量: 100%]</div>
        <div class="text-content">我那時候是唱</div>
      </div><div class="transcription-item" id="segment-10">
        <div class="timestamp">下午2:03:56 - 段落 10 (7.0秒) [質量: 100%]</div>
        <div class="text-content">臺灣人的數字後來我就跟他有一段時間變成比較密切的朋友</div>
      </div><div class="transcription-item" id="segment-11">
        <div class="timestamp">下午2:04:00 - 段落 11 (3.7秒) [質量: 100%]</div>
        <div class="text-content">我遺到美國去我就會想辦法去看他</div>
      </div><div class="transcription-item" id="segment-12">
        <div class="timestamp">下午2:04:03 - 段落 12 (3.6秒) [質量: 100%]</div>
        <div class="text-content">那他那時候已經把他的課程了多在</div>
      </div><div class="transcription-item" id="segment-13">
        <div class="timestamp">下午2:04:06 - 段落 13 (3.2秒) [質量: 100%]</div>
        <div class="text-content">很多学校在试演开课</div>
      </div><div class="transcription-item" id="segment-14">
        <div class="timestamp">下午2:04:10 - 段落 14 (4.6秒) [質量: 100%]</div>
        <div class="text-content">我就去訪問了很多跟這有關係的加州的很多學校</div>
      </div><div class="transcription-item" id="segment-15">
        <div class="timestamp">下午2:04:17 - 段落 15 (7.1秒) [質量: 100%]</div>
        <div class="text-content">他說我們不排除電機系之外的所有的學生,任何學生,只要他腦筋是邏輯</div>
      </div><div class="transcription-item" id="segment-16">
        <div class="timestamp">下午2:04:20 - 段落 16 (3.5秒) [質量: 100%]</div>
        <div class="text-content">有逻辑思想的都可以来设计VLSI</div>
      </div><div class="transcription-item" id="segment-17">
        <div class="timestamp">下午2:04:25 - 段落 17 (5.3秒) [質量: 100%]</div>
        <div class="text-content">就說他,那時候他已經開始把設計跟製造分開了</div>
      </div><div class="transcription-item" id="segment-18">
        <div class="timestamp">下午2:04:33 - 段落 18 (8.4秒) [質量: 100%]</div>
        <div class="text-content">他在学校只能够这样子啊他就是说所有的这些价值创新都是来自于设计技术当然也是</div>
      </div><div class="transcription-item" id="segment-19">
        <div class="timestamp">下午2:04:35 - 段落 19 (3.1秒) [質量: 100%]</div>
        <div class="text-content">但是技術</div>
      </div><div class="transcription-item filtered" id="segment-20" style="display: none;">
        <div class="timestamp">下午2:04:38 - 段落 20 (3.1秒) [質量: 100%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-21" style="display: none;">
        <div class="timestamp">下午2:04:41 - 段落 21 (3.1秒) [質量: 100%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-22" style="display: none;">
        <div class="timestamp">下午2:04:44 - 段落 22 (3.1秒) [質量: 100%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-23" style="display: none;">
        <div class="timestamp">下午2:04:47 - 段落 23 (3.1秒) [質量: 100%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item" id="segment-24">
        <div class="timestamp">下午2:04:49 - 段落 24 (3.1秒) [質量: 100%]</div>
        <div class="text-content">現在不會</div>
      </div><div class="transcription-item" id="segment-25">
        <div class="timestamp">下午2:04:52 - 段落 25 (3.2秒) [質量: 100%]</div>
        <div class="text-content">對</div>
      </div><div class="transcription-item" id="segment-26">
        <div class="timestamp">下午2:04:55 - 段落 26 (3.1秒) [質量: 100%]</div>
        <div class="text-content">到前退,那一段</div>
      </div><div class="transcription-item filtered" id="segment-27" style="display: none;">
        <div class="timestamp">下午2:04:58 - 段落 27 (3.1秒) [質量: 60%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-28" style="display: none;">
        <div class="timestamp">下午2:05:01 - 段落 28 (3.1秒) [質量: 60%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-29" style="display: none;">
        <div class="timestamp">下午2:05:04 - 段落 29 (3.4秒) [質量: 100%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item" id="segment-30">
        <div class="timestamp">下午2:05:07 - 段落 30 (3.4秒) [質量: 100%]</div>
        <div class="text-content">對因為因為</div>
      </div><div class="transcription-item" id="segment-31">
        <div class="timestamp">下午2:05:10 - 段落 31 (3.6秒) [質量: 100%]</div>
        <div class="text-content">現在設計上就是轉路完就就完</div>
      </div><div class="transcription-item" id="segment-32">
        <div class="timestamp">下午2:05:13 - 段落 32 (3.2秒) [質量: 100%]</div>
        <div class="text-content">那我現在還沒有</div>
      </div><div class="transcription-item" id="segment-33">
        <div class="timestamp">下午2:05:17 - 段落 33 (3.6秒) [質量: 100%]</div>
        <div class="text-content">他再回去看那个前面那一</div>
      </div><div class="transcription-item" id="segment-34">
        <div class="timestamp">下午2:05:20 - 段落 34 (3.2秒) [質量: 100%]</div>
        <div class="text-content">因為這一塊</div>
      </div><div class="transcription-item" id="segment-35">
        <div class="timestamp">下午2:05:22 - 段落 35 (3.1秒) [質量: 100%]</div>
        <div class="text-content">對</div>
      </div><div class="transcription-item" id="segment-36">
        <div class="timestamp">下午2:05:26 - 段落 36 (3.5秒) [質量: 100%]</div>
        <div class="text-content">那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那,那</div>
      </div><div class="transcription-item" id="segment-37">
        <div class="timestamp">下午2:05:29 - 段落 37 (3.3秒) [質量: 100%]</div>
        <div class="text-content">就是你看線上像譬如說之前的呢</div>
      </div><div class="transcription-item" id="segment-38">
        <div class="timestamp">下午2:05:32 - 段落 38 (4.1秒) [質量: 100%]</div>
        <div class="text-content">當入片,那邊做那個部分</div>
      </div><div class="transcription-item" id="segment-39">
        <div class="timestamp">下午2:05:35 - 段落 39 (3.2秒) [質量: 100%]</div>
        <div class="text-content">他們就是用</div>
      </div><div class="transcription-item" id="segment-40">
        <div class="timestamp">下午2:05:38 - 段落 40 (3.1秒) [質量: 100%]</div>
        <div class="text-content">H-on-the-wint-20-naker</div>
      </div><div class="transcription-item" id="segment-41">
        <div class="timestamp">下午2:05:41 - 段落 41 (3.1秒) [質量: 100%]</div>
        <div class="text-content">軟體套辦法去做那麼及時的</div>
      </div><div class="transcription-item" id="segment-42">
        <div class="timestamp">下午2:05:44 - 段落 42 (3.1秒) [質量: 100%]</div>
        <div class="text-content">那我們現在直接用這個</div>
      </div><div class="transcription-item" id="segment-43">
        <div class="timestamp">下午2:05:47 - 段落 43 (4.1秒) [質量: 100%]</div>
        <div class="text-content">那講的話可能就</div>
      </div><div class="transcription-item" id="segment-44">
        <div class="timestamp">下午2:05:51 - 段落 44 (3.5秒) [質量: 100%]</div>
        <div class="text-content">因為像我這邊我也不知用線上我只</div>
      </div><div class="transcription-item" id="segment-45">
        <div class="timestamp">下午2:05:54 - 段落 45 (3.3秒) [質量: 100%]</div>
        <div class="text-content">用我自己電腦上面</div>
      </div><div class="transcription-item" id="segment-46">
        <div class="timestamp">下午2:05:58 - 段落 46 (4.3秒) [質量: 100%]</div>
        <div class="text-content">的顯卡,現在再做按照以這樣子來講</div>
      </div><div class="transcription-item" id="segment-47">
        <div class="timestamp">下午2:06:01 - 段落 47 (4.2秒) [質量: 100%]</div>
        <div class="text-content">他的部分還沒有辦法</div>
      </div><div class="transcription-item" id="segment-48">
        <div class="timestamp">下午2:06:04 - 段落 48 (3.1秒) [質量: 100%]</div>
        <div class="text-content">做到那麼好</div>
      </div><div class="transcription-item" id="segment-49">
        <div class="timestamp">下午2:06:07 - 段落 49 (3.1秒) [質量: 100%]</div>
        <div class="text-content">就是一步一步來,我現在可能先</div>
      </div><div class="transcription-item" id="segment-50">
        <div class="timestamp">下午2:06:10 - 段落 50 (3.6秒) [質量: 100%]</div>
        <div class="text-content">那他就可以即使現在就是去</div>
      </div><div class="transcription-item" id="segment-51">
        <div class="timestamp">下午2:06:13 - 段落 51 (3.3秒) [質量: 100%]</div>
        <div class="text-content">不怪雨嶺啊, worst</div>
      </div><div class="transcription-item" id="segment-52">
        <div class="timestamp">下午2:06:17 - 段落 52 (3.7秒) [質量: 100%]</div>
        <div class="text-content">我可能那個這個回憶</div>
      </div><div class="transcription-item" id="segment-53">
        <div class="timestamp">下午2:06:20 - 段落 53 (3.1秒) [質量: 100%]</div>
        <div class="text-content">無分的聲音都可以去做</div>
      </div><div class="transcription-item" id="segment-54">
        <div class="timestamp">下午2:06:23 - 段落 54 (3.8秒) [質量: 100%]</div>
        <div class="text-content">即使的那個文字出來,然後後面再來</div>
      </div><div class="transcription-item" id="segment-55">
        <div class="timestamp">下午2:06:26 - 段落 55 (3.1秒) [質量: 100%]</div>
        <div class="text-content">後續就是會做即時翻譯</div>
      </div><div class="transcription-item" id="segment-56">
        <div class="timestamp">下午2:06:29 - 段落 56 (3.1秒) [質量: 100%]</div>
        <div class="text-content">對</div>
      </div><div class="transcription-item filtered" id="segment-57" style="display: none;">
        <div class="timestamp">下午2:06:32 - 段落 57 (3.1秒) [質量: 60%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-58" style="display: none;">
        <div class="timestamp">下午2:06:33 - 段落 58 (1.8秒) [質量: 60%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-59" style="display: none;">
        <div class="timestamp">下午2:06:44 - 段落 59 (3.1秒) [質量: 60%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-60" style="display: none;">
        <div class="timestamp">下午2:06:47 - 段落 60 (3.1秒) [質量: 60%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item" id="segment-61">
        <div class="timestamp">下午2:06:50 - 段落 61 (3.3秒) [質量: 20%]</div>
        <div class="text-content">我认识了我认识了我认识了我认识了我认识了我认识了我认识了我认识了我认识了我认识了我认识了我认识了我认识了我认识了我认识了我认识了我认识了我认识了我认识了我认识了我认识了我认识了我认识了我认识了我认识了我认识了我认识了我认识了</div>
      </div><div class="transcription-item filtered" id="segment-62" style="display: none;">
        <div class="timestamp">下午2:06:54 - 段落 62 (4.0秒) [質量: 60%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-63" style="display: none;">
        <div class="timestamp">下午2:06:57 - 段落 63 (3.1秒) [質量: 60%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item" id="segment-64">
        <div class="timestamp">下午2:07:00 - 段落 64 (3.5秒) [質量: 100%]</div>
        <div class="text-content">呃...問到什麼情形喔</div>
      </div><div class="transcription-item filtered" id="segment-65" style="display: none;">
        <div class="timestamp">下午2:07:03 - 段落 65 (3.9秒) [質量: 100%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-66" style="display: none;">
        <div class="timestamp">下午2:07:06 - 段落 66 (3.2秒) [質量: 100%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-67" style="display: none;">
        <div class="timestamp">下午2:07:10 - 段落 67 (4.0秒) [質量: 100%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item" id="segment-68">
        <div class="timestamp">下午2:08:03 - 段落 68 (3.4秒) [質量: 100%]</div>
        <div class="text-content">對,依靈</div>
      </div><div class="transcription-item" id="segment-69">
        <div class="timestamp">下午2:08:06 - 段落 69 (3.4秒) [質量: 100%]</div>
        <div class="text-content">網頁界面的話,它有很</div>
      </div><div class="transcription-item" id="segment-70">
        <div class="timestamp">下午2:08:09 - 段落 70 (3.1秒) [質量: 100%]</div>
        <div class="text-content">很多套件是要往眼那邊</div>
      </div><div class="transcription-item" id="segment-71">
        <div class="timestamp">下午2:08:12 - 段落 71 (3.2秒) [質量: 100%]</div>
        <div class="text-content">有,有</div>
      </div><div class="transcription-item" id="segment-72">
        <div class="timestamp">下午2:08:14 - 段落 72 (3.1秒) [質量: 100%]</div>
        <div class="text-content">喔喔喔喔喔喔喔喔喔喔喔喔喔喔喔喔喔喔喔喔喔喔喔喔喔喔喔喔喔喔</div>
      </div><div class="transcription-item" id="segment-73">
        <div class="timestamp">下午2:08:18 - 段落 73 (4.4秒) [質量: 100%]</div>
        <div class="text-content">拍攝他有套件上面有那個套件</div>
      </div><div class="transcription-item" id="segment-74">
        <div class="timestamp">下午2:08:21 - 段落 74 (3.1秒) [質量: 100%]</div>
        <div class="text-content">可以敲裂那哦</div>
      </div><div class="transcription-item" id="segment-75">
        <div class="timestamp">下午2:08:24 - 段落 75 (3.1秒) [質量: 100%]</div>
        <div class="text-content">往右面右,對</div>
      </div><div class="transcription-item" id="segment-76">
        <div class="timestamp">下午2:08:27 - 段落 76 (3.5秒) [質量: 100%]</div>
        <div class="text-content">我像這個也是</div>
      </div><div class="transcription-item" id="segment-77">
        <div class="timestamp">下午2:08:30 - 段落 77 (3.1秒) [質量: 100%]</div>
        <div class="text-content">先前的那個技術</div>
      </div><div class="transcription-item" id="segment-78">
        <div class="timestamp">下午2:08:33 - 段落 78 (3.1秒) [質量: 100%]</div>
        <div class="text-content">發現可以網頁錄影</div>
      </div><div class="transcription-item" id="segment-79">
        <div class="timestamp">下午2:08:36 - 段落 79 (3.2秒) [質量: 100%]</div>
        <div class="text-content">特别够呢,穿那边里面的瓦达。</div>
      </div><div class="transcription-item" id="segment-80">
        <div class="timestamp">下午2:08:39 - 段落 80 (3.1秒) [質量: 100%]</div>
        <div class="text-content">開發出來可以做這個機師</div>
      </div><div class="transcription-item" id="segment-81">
        <div class="timestamp">下午2:08:41 - 段落 81 (3.1秒) [質量: 100%]</div>
        <div class="text-content">像我現在就是</div>
      </div><div class="transcription-item" id="segment-82">
        <div class="timestamp">下午2:08:44 - 段落 82 (3.1秒) [質量: 100%]</div>
        <div class="text-content">用網頁錄影那種分享的方式</div>
      </div><div class="transcription-item" id="segment-83">
        <div class="timestamp">下午2:08:47 - 段落 83 (3.3秒) [質量: 100%]</div>
        <div class="text-content">去把聲音抓出來</div>
      </div><div class="transcription-item" id="segment-84">
        <div class="timestamp">下午2:08:50 - 段落 84 (3.3秒) [質量: 100%]</div>
        <div class="text-content">然後再把它丟</div>
      </div><div class="transcription-item" id="segment-85">
        <div class="timestamp">下午2:08:54 - 段落 85 (3.5秒) [質量: 100%]</div>
        <div class="text-content">放到後台去做那個</div>
      </div><div class="transcription-item" id="segment-86">
        <div class="timestamp">下午2:08:57 - 段落 86 (3.2秒) [質量: 100%]</div>
        <div class="text-content">转路,然后再拔回</div>
      </div><div class="transcription-item" id="segment-87">
        <div class="timestamp">下午2:08:59 - 段落 87 (3.1秒) [質量: 100%]</div>
        <div class="text-content">稍微穩持這邊</div>
      </div><div class="transcription-item" id="segment-88">
        <div class="timestamp">下午2:09:03 - 段落 88 (3.8秒) [質量: 100%]</div>
        <div class="text-content">這一塊不分</div>
      </div><div class="transcription-item" id="segment-89">
        <div class="timestamp">下午2:09:06 - 段落 89 (3.7秒) [質量: 100%]</div>
        <div class="text-content">就是後面就是要</div>
      </div><div class="transcription-item" id="segment-90">
        <div class="timestamp">下午2:09:09 - 段落 90 (3.1秒) [質量: 100%]</div>
        <div class="text-content">即使翻譯</div>
      </div><div class="transcription-item filtered" id="segment-91" style="display: none;">
        <div class="timestamp">下午2:09:12 - 段落 91 (3.1秒) [質量: 60%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-92" style="display: none;">
        <div class="timestamp">下午2:09:15 - 段落 92 (3.1秒) [質量: 100%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item" id="segment-93">
        <div class="timestamp">下午2:09:18 - 段落 93 (3.1秒) [質量: 20%]</div>
        <div class="text-content">作词作曲</div>
      </div><div class="transcription-item filtered" id="segment-94" style="display: none;">
        <div class="timestamp">下午2:09:20 - 段落 94 (3.2秒) [質量: 100%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item" id="segment-95">
        <div class="timestamp">下午2:09:24 - 段落 95 (3.8秒) [質量: 100%]</div>
        <div class="text-content">它現在還是有一點怪怪的像那種</div>
      </div><div class="transcription-item" id="segment-96">
        <div class="timestamp">下午2:09:27 - 段落 96 (3.3秒) [質量: 100%]</div>
        <div class="text-content">就是我們要的主要更多</div>
      </div><div class="transcription-item" id="segment-97">
        <div class="timestamp">下午2:09:31 - 段落 97 (4.2秒) [質量: 100%]</div>
        <div class="text-content">像那個質量像就是以他的那個聲音跟</div>
      </div><div class="transcription-item" id="segment-98">
        <div class="timestamp">下午2:09:34 - 段落 98 (3.1秒) [質量: 100%]</div>
        <div class="text-content">就那個部份要去</div>
      </div><div class="transcription-item" id="segment-99">
        <div class="timestamp">下午2:09:37 - 段落 99 (3.9秒) [質量: 100%]</div>
        <div class="text-content">線縮一個紙,也沒有一個紙,就讓它</div>
      </div><div class="transcription-item" id="segment-100">
        <div class="timestamp">下午2:09:40 - 段落 100 (3.2秒) [質量: 100%]</div>
        <div class="text-content">不會取得主來的顏色</div>
      </div><div class="transcription-item filtered" id="segment-101" style="display: none;">
        <div class="timestamp">下午2:09:43 - 段落 101 (3.1秒) [質量: 60%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-102" style="display: none;">
        <div class="timestamp">下午2:09:46 - 段落 102 (3.1秒) [質量: 20%]</div>
        <div class="text-content">🚫 已過濾：音頻質量過低</div>
      </div><div class="transcription-item filtered" id="segment-103" style="display: none;">
        <div class="timestamp">下午2:09:49 - 段落 103 (3.1秒) [質量: 20%]</div>
        <div class="text-content">🚫 已過濾：音頻質量過低</div>
      </div><div class="transcription-item" id="segment-104">
        <div class="timestamp">下午2:09:51 - 段落 104 (3.1秒) [質量: 100%]</div>
        <div class="text-content">玉玉,玉玉。</div>
      </div><div class="transcription-item filtered" id="segment-105" style="display: none;">
        <div class="timestamp">下午2:09:54 - 段落 105 (3.1秒) [質量: 60%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-106" style="display: none;">
        <div class="timestamp">下午2:09:57 - 段落 106 (3.1秒) [質量: 60%]</div>
        <div class="text-content">🚫 已過濾：內容過短</div>
      </div><div class="transcription-item filtered" id="segment-107" style="display: none;">
        <div class="timestamp">下午2:10:00 - 段落 107 (3.1秒) [已過濾]</div>
        <div class="text-content">🚫 音頻質量過低，已自動過濾</div>
      </div><div class="transcription-item filtered" id="segment-108" style="display: none;">
        <div class="timestamp">下午2:10:03 - 段落 108 (3.1秒) [已過濾]</div>
        <div class="text-content">🚫 音頻質量過低，已自動過濾</div>
      </div><div class="transcription-item filtered" id="segment-109" style="display: none;">
        <div class="timestamp">下午2:10:06 - 段落 109 (3.5秒) [質量: 100%]</div>
        <div class="text-content">🚫 已過濾：內容過短</div>
      </div><div class="transcription-item filtered" id="segment-110" style="display: none;">
        <div class="timestamp">下午2:10:09 - 段落 110 (3.1秒) [質量: 100%]</div>
        <div class="text-content">🚫 已過濾：內容過短</div>
      </div><div class="transcription-item" id="segment-111">
        <div class="timestamp">下午2:10:11 - 段落 111 (3.1秒) [質量: 100%]</div>
        <div class="text-content">就知道</div>
      </div><div class="transcription-item" id="segment-112">
        <div class="timestamp">下午2:10:16 - 段落 112 (4.7秒) [質量: 100%]</div>
        <div class="text-content">他這個還是要讓他有可以參數調整好了</div>
      </div><div class="transcription-item" id="segment-113">
        <div class="timestamp">下午2:10:20 - 段落 113 (4.8秒) [質量: 100%]</div>
        <div class="text-content">就不斷完全不知道到现在要干吗</div>
      </div><div class="transcription-item filtered" id="segment-114" style="display: none;">
        <div class="timestamp">下午2:10:23 - 段落 114 (3.4秒) [質量: 100%]</div>
        <div class="text-content">🚫 已過濾：內容過短</div>
      </div><div class="transcription-item filtered" id="segment-115" style="display: none;">
        <div class="timestamp">下午2:10:26 - 段落 115 (3.1秒) [質量: 60%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-116" style="display: none;">
        <div class="timestamp">下午2:10:29 - 段落 116 (3.1秒) [質量: 100%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-117" style="display: none;">
        <div class="timestamp">下午2:10:32 - 段落 117 (3.1秒) [質量: 100%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-118" style="display: none;">
        <div class="timestamp">下午2:10:35 - 段落 118 (3.2秒) [已過濾]</div>
        <div class="text-content">🚫 音頻質量過低，已自動過濾</div>
      </div><div class="transcription-item filtered" id="segment-119" style="display: none;">
        <div class="timestamp">下午2:10:38 - 段落 119 (3.3秒) [質量: 100%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-120" style="display: none;">
        <div class="timestamp">下午2:10:41 - 段落 120 (3.1秒) [質量: 60%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item" id="segment-121">
        <div class="timestamp">下午2:10:44 - 段落 121 (3.3秒) [質量: 100%]</div>
        <div class="text-content">誒...無相</div>
      </div><div class="transcription-item" id="segment-122">
        <div class="timestamp">下午2:10:47 - 段落 122 (3.3秒) [質量: 100%]</div>
        <div class="text-content">開火,我讓他用心</div>
      </div><div class="transcription-item filtered" id="segment-123" style="display: none;">
        <div class="timestamp">下午2:10:50 - 段落 123 (3.5秒) [質量: 100%]</div>
        <div class="text-content">🚫 已過濾：重複字符</div>
      </div><div class="transcription-item" id="segment-124">
        <div class="timestamp">下午2:10:53 - 段落 124 (3.1秒) [質量: 100%]</div>
        <div class="text-content">沒辦法你會覺得</div>
      </div><div class="transcription-item" id="segment-125">
        <div class="timestamp">下午2:10:56 - 段落 125 (3.1秒) [質量: 100%]</div>
        <div class="text-content">我伴到這個</div>
      </div><div class="transcription-item filtered" id="segment-126" style="display: none;">
        <div class="timestamp">下午2:10:58 - 段落 126 (3.2秒) [質量: 100%]</div>
        <div class="text-content">🚫 已過濾：內容過短</div>
      </div><div class="transcription-item filtered" id="segment-127" style="display: none;">
        <div class="timestamp">下午2:11:01 - 段落 127 (3.1秒) [質量: 60%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-128" style="display: none;">
        <div class="timestamp">下午2:11:04 - 段落 128 (3.1秒) [質量: 100%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item" id="segment-129">
        <div class="timestamp">下午2:11:07 - 段落 129 (3.3秒) [質量: 100%]</div>
        <div class="text-content">這個我現在是</div>
      </div><div class="transcription-item" id="segment-130">
        <div class="timestamp">下午2:11:10 - 段落 130 (3.1秒) [質量: 100%]</div>
        <div class="text-content">穿我的,我的部分</div>
      </div><div class="transcription-item" id="segment-131">
        <div class="timestamp">下午2:11:13 - 段落 131 (3.1秒) [質量: 100%]</div>
        <div class="text-content">我的部分,它 need a部分</div>
      </div><div class="transcription-item filtered" id="segment-132" style="display: none;">
        <div class="timestamp">下午2:11:16 - 段落 132 (3.9秒) [質量: 100%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-133" style="display: none;">
        <div class="timestamp">下午2:11:19 - 段落 133 (3.1秒) [質量: 100%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-134" style="display: none;">
        <div class="timestamp">下午2:11:22 - 段落 134 (3.1秒) [質量: 60%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item" id="segment-135">
        <div class="timestamp">下午2:11:25 - 段落 135 (3.3秒) [質量: 100%]</div>
        <div class="text-content">沒有啊,就是我的部分的按鈕在變</div>
      </div><div class="transcription-item" id="segment-136">
        <div class="timestamp">下午2:11:28 - 段落 136 (3.2秒) [質量: 100%]</div>
        <div class="text-content">我又一次没有</div>
      </div><div class="transcription-item" id="segment-137">
        <div class="timestamp">下午2:11:31 - 段落 137 (3.1秒) [質量: 100%]</div>
        <div class="text-content">主要進來所以大家也沒辦法</div>
      </div><div class="transcription-item" id="segment-138">
        <div class="timestamp">下午2:11:33 - 段落 138 (3.1秒) [質量: 100%]</div>
        <div class="text-content">等我一下</div>
      </div><div class="transcription-item" id="segment-139">
        <div class="timestamp">下午2:11:36 - 段落 139 (3.2秒) [質量: 100%]</div>
        <div class="text-content">用網頁板的蓋蓬</div>
      </div><div class="transcription-item" id="segment-140">
        <div class="timestamp">下午2:11:39 - 段落 140 (3.3秒) [質量: 100%]</div>
        <div class="text-content">可以等我呀</div>
      </div><div class="transcription-item filtered" id="segment-141" style="display: none;">
        <div class="timestamp">下午2:11:42 - 段落 141 (3.1秒) [質量: 60%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-142" style="display: none;">
        <div class="timestamp">下午2:11:45 - 段落 142 (3.5秒) [質量: 100%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-143" style="display: none;">
        <div class="timestamp">下午2:11:48 - 段落 143 (3.1秒) [質量: 60%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-144" style="display: none;">
        <div class="timestamp">下午2:11:51 - 段落 144 (3.1秒) [質量: 60%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-145" style="display: none;">
        <div class="timestamp">下午2:11:54 - 段落 145 (3.3秒) [質量: 60%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-146" style="display: none;">
        <div class="timestamp">下午2:11:57 - 段落 146 (3.6秒) [質量: 100%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-147" style="display: none;">
        <div class="timestamp">下午2:12:01 - 段落 147 (3.6秒) [質量: 100%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-148" style="display: none;">
        <div class="timestamp">下午2:12:04 - 段落 148 (3.1秒) [質量: 60%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-149" style="display: none;">
        <div class="timestamp">下午2:12:06 - 段落 149 (3.1秒) [質量: 60%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-150" style="display: none;">
        <div class="timestamp">下午2:12:09 - 段落 150 (3.4秒) [質量: 100%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-151" style="display: none;">
        <div class="timestamp">下午2:12:13 - 段落 151 (3.3秒) [質量: 60%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-152" style="display: none;">
        <div class="timestamp">下午2:12:15 - 段落 152 (3.2秒) [質量: 60%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-153" style="display: none;">
        <div class="timestamp">下午2:12:19 - 段落 153 (3.3秒) [質量: 100%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-154" style="display: none;">
        <div class="timestamp">下午2:12:22 - 段落 154 (4.1秒) [質量: 100%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-155" style="display: none;">
        <div class="timestamp">下午2:12:25 - 段落 155 (3.4秒) [質量: 100%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-156" style="display: none;">
        <div class="timestamp">下午2:12:28 - 段落 156 (3.1秒) [質量: 100%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-157" style="display: none;">
        <div class="timestamp">下午2:12:31 - 段落 157 (3.3秒) [質量: 100%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-158" style="display: none;">
        <div class="timestamp">下午2:12:34 - 段落 158 (3.1秒) [質量: 100%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-159" style="display: none;">
        <div class="timestamp">下午2:12:37 - 段落 159 (3.1秒) [質量: 60%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-160" style="display: none;">
        <div class="timestamp">下午2:12:40 - 段落 160 (3.1秒) [質量: 60%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-161" style="display: none;">
        <div class="timestamp">下午2:12:43 - 段落 161 (3.1秒) [質量: 100%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-162" style="display: none;">
        <div class="timestamp">下午2:12:45 - 段落 162 (3.1秒) [已過濾]</div>
        <div class="text-content">🚫 音頻質量過低，已自動過濾</div>
      </div><div class="transcription-item filtered" id="segment-163" style="display: none;">
        <div class="timestamp">下午2:12:48 - 段落 163 (3.1秒) [質量: 60%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-164" style="display: none;">
        <div class="timestamp">下午2:12:51 - 段落 164 (3.1秒) [已過濾]</div>
        <div class="text-content">🚫 音頻質量過低，已自動過濾</div>
      </div><div class="transcription-item filtered" id="segment-165" style="display: none;">
        <div class="timestamp">下午2:12:54 - 段落 165 (3.1秒) [質量: 100%]</div>
        <div class="text-content">🚫 已過濾：內容過短</div>
      </div><div class="transcription-item" id="segment-166">
        <div class="timestamp">下午2:12:58 - 段落 166 (4.9秒) [質量: 100%]</div>
        <div class="text-content">喔? 聲音比較青春我現在是</div>
      </div><div class="transcription-item filtered" id="segment-167" style="display: none;">
        <div class="timestamp">下午2:13:01 - 段落 167 (3.3秒) [已過濾]</div>
        <div class="text-content">🚫 音頻質量過低，已自動過濾</div>
      </div><div class="transcription-item" id="segment-168">
        <div class="timestamp">下午2:13:05 - 段落 168 (3.3秒) [質量: 100%]</div>
        <div class="text-content">就是抓</div>
      </div><div class="transcription-item" id="segment-169">
        <div class="timestamp">下午2:13:07 - 段落 169 (3.2秒) [質量: 100%]</div>
        <div class="text-content">那个 页面就是我 网页</div>
      </div><div class="transcription-item" id="segment-170">
        <div class="timestamp">下午2:13:10 - 段落 170 (3.3秒) [質量: 100%]</div>
        <div class="text-content">版的這個頁面,然後我再</div>
      </div><div class="transcription-item" id="segment-171">
        <div class="timestamp">下午2:13:13 - 段落 171 (3.1秒) [質量: 100%]</div>
        <div class="text-content">把它噢</div>
      </div><div class="transcription-item" id="segment-172">
        <div class="timestamp">下午2:13:16 - 段落 172 (3.1秒) [質量: 100%]</div>
        <div class="text-content">Sure</div>
      </div><div class="transcription-item" id="segment-173">
        <div class="timestamp">下午2:13:19 - 段落 173 (3.1秒) [質量: 100%]</div>
        <div class="text-content">整個螢幕的畫面</div>
      </div><div class="transcription-item filtered" id="segment-174" style="display: none;">
        <div class="timestamp">下午2:13:22 - 段落 174 (3.1秒) [質量: 100%]</div>
        <div class="text-content">🚫 已過濾：內容過短</div>
      </div><div class="transcription-item" id="segment-175">
        <div class="timestamp">下午2:13:25 - 段落 175 (3.3秒) [質量: 100%]</div>
        <div class="text-content">以這樣來的話</div>
      </div><div class="transcription-item" id="segment-176">
        <div class="timestamp">下午2:13:28 - 段落 176 (3.1秒) [質量: 100%]</div>
        <div class="text-content">該就可以把我們</div>
      </div><div class="transcription-item" id="segment-177">
        <div class="timestamp">下午2:13:31 - 段落 177 (3.5秒) [質量: 100%]</div>
        <div class="text-content">兩個人的講話是</div>
      </div><div class="transcription-item" id="segment-178">
        <div class="timestamp">下午2:13:34 - 段落 178 (3.1秒) [質量: 100%]</div>
        <div class="text-content">完全转度就是</div>
      </div><div class="transcription-item" id="segment-179">
        <div class="timestamp">下午2:13:36 - 段落 179 (3.1秒) [質量: 100%]</div>
        <div class="text-content">就拿我试试看</div>
      </div><div class="transcription-item filtered" id="segment-180" style="display: none;">
        <div class="timestamp">下午2:13:39 - 段落 180 (3.1秒) [質量: 60%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-181" style="display: none;">
        <div class="timestamp">下午2:13:42 - 段落 181 (3.2秒) [質量: 60%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item" id="segment-182">
        <div class="timestamp">下午2:13:45 - 段落 182 (3.3秒) [質量: 100%]</div>
        <div class="text-content">沒有啊 你要講話啊</div>
      </div><div class="transcription-item filtered" id="segment-183" style="display: none;">
        <div class="timestamp">下午2:13:48 - 段落 183 (3.1秒) [質量: 60%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item" id="segment-184">
        <div class="timestamp">下午2:13:52 - 段落 184 (4.1秒) [質量: 100%]</div>
        <div class="text-content">哎,我剛有講話,是那個,突然斷掉了</div>
      </div><div class="transcription-item" id="segment-185">
        <div class="timestamp">下午2:13:55 - 段落 185 (3.1秒) [質量: 100%]</div>
        <div class="text-content">我從Milk飛</div>
      </div><div class="transcription-item" id="segment-186">
        <div class="timestamp">下午2:13:57 - 段落 186 (3.1秒) [質量: 100%]</div>
        <div class="text-content">哦,有了,啊,冰有了</div>
      </div><div class="transcription-item filtered" id="segment-187" style="display: none;">
        <div class="timestamp">下午2:14:00 - 段落 187 (3.1秒) [質量: 100%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item" id="segment-188">
        <div class="timestamp">下午2:14:03 - 段落 188 (3.1秒) [質量: 100%]</div>
        <div class="text-content">字幕製作品</div>
      </div><div class="transcription-item" id="segment-189">
        <div class="timestamp">下午2:14:06 - 段落 189 (3.4秒) [質量: 100%]</div>
        <div class="text-content">因為在無半頭這邊比較麻煩啊,那樣</div>
      </div><div class="transcription-item" id="segment-190">
        <div class="timestamp">下午2:14:09 - 段落 190 (3.2秒) [質量: 100%]</div>
        <div class="text-content">後面就是,等到弄好的話爆打殼</div>
      </div><div class="transcription-item" id="segment-191">
        <div class="timestamp">下午2:14:12 - 段落 191 (3.7秒) [質量: 100%]</div>
        <div class="text-content">然後移回到我我會斷在裡面就</div>
      </div><div class="transcription-item" id="segment-192">
        <div class="timestamp">下午2:14:15 - 段落 192 (3.3秒) [質量: 100%]</div>
        <div class="text-content">可以嗯</div>
      </div><div class="transcription-item" id="segment-193">
        <div class="timestamp">下午2:14:19 - 段落 193 (3.8秒) [質量: 100%]</div>
        <div class="text-content">對,那至少</div>
      </div><div class="transcription-item filtered" id="segment-194" style="display: none;">
        <div class="timestamp">下午2:14:22 - 段落 194 (3.4秒) [質量: 100%]</div>
        <div class="text-content">🚫 已過濾：內容過短</div>
      </div><div class="transcription-item filtered" id="segment-195" style="display: none;">
        <div class="timestamp">下午2:14:25 - 段落 195 (3.1秒) [質量: 100%]</div>
        <div class="text-content">🚫 已過濾：內容過短</div>
      </div><div class="transcription-item" id="segment-196">
        <div class="timestamp">下午2:14:28 - 段落 196 (3.1秒) [質量: 100%]</div>
        <div class="text-content">這個什麼我覺得</div>
      </div><div class="transcription-item" id="segment-197">
        <div class="timestamp">下午2:14:31 - 段落 197 (3.8秒) [質量: 100%]</div>
        <div class="text-content">使用秋季發表會部份</div>
      </div><div class="transcription-item" id="segment-198">
        <div class="timestamp">下午2:14:34 - 段落 198 (3.5秒) [質量: 100%]</div>
        <div class="text-content">就把往下做偏要一点,然后那个</div>
      </div><div class="transcription-item" id="segment-199">
        <div class="timestamp">下午2:14:38 - 段落 199 (3.6秒) [質量: 100%]</div>
        <div class="text-content">會議的部分18追個</div>
      </div><div class="transcription-item filtered" id="segment-200" style="display: none;">
        <div class="timestamp">下午2:14:41 - 段落 200 (3.1秒) [質量: 100%]</div>
        <div class="text-content">🚫 已過濾：內容過短</div>
      </div><div class="transcription-item" id="segment-201">
        <div class="timestamp">下午2:14:43 - 段落 201 (3.1秒) [質量: 100%]</div>
        <div class="text-content">阻制搞,拿在转</div>
      </div><div class="transcription-item" id="segment-202">
        <div class="timestamp">下午2:14:46 - 段落 202 (3.1秒) [質量: 100%]</div>
        <div class="text-content">5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2,5,2</div>
      </div><div class="transcription-item" id="segment-203">
        <div class="timestamp">下午2:14:49 - 段落 203 (3.4秒) [質量: 100%]</div>
        <div class="text-content">看这个东西就</div>
      </div><div class="transcription-item" id="segment-204">
        <div class="timestamp">下午2:14:53 - 段落 204 (3.4秒) [質量: 100%]</div>
        <div class="text-content">不行我不會可以</div>
      </div><div class="transcription-item" id="segment-205">
        <div class="timestamp">下午2:14:55 - 段落 205 (3.1秒) [質量: 100%]</div>
        <div class="text-content">比較有用一點</div>
      </div><div class="transcription-item" id="segment-206">
        <div class="timestamp">下午2:14:58 - 段落 206 (3.1秒) [質量: 100%]</div>
        <div class="text-content">然後這個</div>
      </div><div class="transcription-item" id="segment-207">
        <div class="timestamp">下午2:15:01 - 段落 207 (3.1秒) [質量: 100%]</div>
        <div class="text-content">不知道來,那要碰匙,可能要再看一下</div>
      </div><div class="transcription-item" id="segment-208">
        <div class="timestamp">下午2:15:05 - 段落 208 (3.8秒) [質量: 100%]</div>
        <div class="text-content">那個有時候裝出來的東西都怪怪的</div>
      </div><div class="transcription-item" id="segment-209">
        <div class="timestamp">下午2:15:09 - 段落 209 (5.0秒) [質量: 100%]</div>
        <div class="text-content">因為他他就想EK死的那個</div>
      </div><div class="transcription-item" id="segment-210">
        <div class="timestamp">下午2:15:14 - 段落 210 (5.0秒) [質量: 100%]</div>
        <div class="text-content">Wisper一樣,他就是數字稿,就是一定會遇到了</div>
      </div><div class="transcription-item" id="segment-211">
        <div class="timestamp">下午2:15:17 - 段落 211 (3.1秒) [質量: 100%]</div>
        <div class="text-content">他會有時候會拯救我的媽媽</div>
      </div><div class="transcription-item" id="segment-212">
        <div class="timestamp">下午2:15:22 - 段落 212 (5.8秒) [質量: 80%]</div>
        <div class="text-content">但是我覺得因為當初那一套至少後面能用是</div>
      </div><div class="transcription-item" id="segment-213">
        <div class="timestamp">下午2:15:25 - 段落 213 (3.3秒) [質量: 100%]</div>
        <div class="text-content">因为就是你们要所有把那个主bt.dead</div>
      </div><div class="transcription-item" id="segment-214">
        <div class="timestamp">下午2:15:31 - 段落 214 (6.7秒) [質量: 100%]</div>
        <div class="text-content">他會有一個原始祇的然後再去升整個去</div>
      </div><div class="transcription-item" id="segment-215">
        <div class="timestamp">下午2:15:35 - 段落 215 (3.8秒) [質量: 100%]</div>
        <div class="text-content">看,I really dreamt about good</div>
      </div><div class="transcription-item" id="segment-216">
        <div class="timestamp">下午2:15:38 - 段落 216 (4.0秒) [質量: 100%]</div>
        <div class="text-content">那邊也是可以啊就是反射也是可以不夠</div>
      </div><div class="transcription-item" id="segment-217">
        <div class="timestamp">下午2:15:42 - 段落 217 (3.6秒) [質量: 100%]</div>
        <div class="text-content">我現在後面就需要這把</div>
      </div><div class="transcription-item" id="segment-218">
        <div class="timestamp">下午2:15:44 - 段落 218 (3.1秒) [質量: 100%]</div>
        <div class="text-content">B2的套進來</div>
      </div><div class="transcription-item" id="segment-219">
        <div class="timestamp">下午2:15:47 - 段落 219 (3.1秒) [質量: 60%]</div>
        <div class="text-content">請稍後再稍後</div>
      </div><div class="transcription-item" id="segment-220">
        <div class="timestamp">下午2:15:51 - 段落 220 (3.9秒) [質量: 100%]</div>
        <div class="text-content">至少說後面</div>
      </div><div class="transcription-item" id="segment-221">
        <div class="timestamp">下午2:15:54 - 段落 221 (3.1秒) [質量: 100%]</div>
        <div class="text-content">可以做那個後面的</div>
      </div><div class="transcription-item" id="segment-222">
        <div class="timestamp">下午2:15:56 - 段落 222 (3.1秒) [質量: 100%]</div>
        <div class="text-content">一些運用</div>
      </div><div class="transcription-item filtered" id="segment-223" style="display: none;">
        <div class="timestamp">下午2:15:59 - 段落 223 (3.1秒) [質量: 100%]</div>
        <div class="text-content">🚫 已過濾：內容過短</div>
      </div><div class="transcription-item" id="segment-224">
        <div class="timestamp">下午2:16:03 - 段落 224 (4.2秒) [質量: 100%]</div>
        <div class="text-content">不會啦 我覺得這就是如果你把這個</div>
      </div><div class="transcription-item" id="segment-225">
        <div class="timestamp">下午2:16:06 - 段落 225 (3.5秒) [質量: 100%]</div>
        <div class="text-content">回憶的那個分成一層這種</div>
      </div><div class="transcription-item" id="segment-226">
        <div class="timestamp">下午2:16:09 - 段落 226 (3.2秒) [質量: 100%]</div>
        <div class="text-content">足自搞的我覺得大家就更會喜歡用而且</div>
      </div><div class="transcription-item" id="segment-227">
        <div class="timestamp">下午2:16:13 - 段落 227 (3.8秒) [質量: 100%]</div>
        <div class="text-content">你有用那個嗎?你有看那個之前那個</div>
      </div><div class="transcription-item" id="segment-228">
        <div class="timestamp">下午2:16:16 - 段落 228 (3.8秒) [質量: 100%]</div>
        <div class="text-content">什麼?IT Tools不是有一個新的那個</div>
      </div><div class="transcription-item" id="segment-229">
        <div class="timestamp">下午2:16:20 - 段落 229 (4.6秒) [質量: 100%]</div>
        <div class="text-content">也是有一個那個會議組織高</div>
      </div><div class="transcription-item" id="segment-230">
        <div class="timestamp">下午2:16:24 - 段落 230 (4.0秒) [質量: 100%]</div>
        <div class="text-content">而且那現在不能用啊對欸你有發覺</div>
      </div><div class="transcription-item" id="segment-231">
        <div class="timestamp">下午2:16:28 - 段落 231 (3.9秒) [質量: 100%]</div>
        <div class="text-content">他就是後來又搜掉了對,又倒了那邊就搜了</div>
      </div><div class="transcription-item" id="segment-232">
        <div class="timestamp">下午2:16:30 - 段落 232 (3.1秒) [質量: 100%]</div>
        <div class="text-content">不讓你養了</div>
      </div><div class="transcription-item" id="segment-233">
        <div class="timestamp">下午2:16:33 - 段落 233 (3.1秒) [質量: 100%]</div>
        <div class="text-content">因為 突然 因為之前</div>
      </div><div class="transcription-item" id="segment-234">
        <div class="timestamp">下午2:16:36 - 段落 234 (3.1秒) [質量: 100%]</div>
        <div class="text-content">再用之後然後到這轉都轉反替掰掰</div>
      </div><div class="transcription-item" id="segment-235">
        <div class="timestamp">下午2:16:39 - 段落 235 (3.3秒) [質量: 100%]</div>
        <div class="text-content">簡體的然後轉出來的東西</div>
      </div><div class="transcription-item" id="segment-236">
        <div class="timestamp">下午2:16:42 - 段落 236 (3.1秒) [質量: 100%]</div>
        <div class="text-content">知道,我好用</div>
      </div><div class="transcription-item" id="segment-237">
        <div class="timestamp">下午2:16:45 - 段落 237 (3.2秒) [質量: 80%]</div>
        <div class="text-content">不過因為他那一刻我三次就</div>
      </div><div class="transcription-item" id="segment-238">
        <div class="timestamp">下午2:16:48 - 段落 238 (4.0秒) [質量: 100%]</div>
        <div class="text-content">他剛開的時候那才沒所作為有玩他是會回去</div>
      </div><div class="transcription-item" id="segment-239">
        <div class="timestamp">下午2:16:53 - 段落 239 (4.7秒) [質量: 100%]</div>
        <div class="text-content">再照你的那個字,就是會有一個再把它那個字,再從修剝</div>
      </div><div class="transcription-item" id="segment-240">
        <div class="timestamp">下午2:16:59 - 段落 240 (7.0秒) [質量: 80%]</div>
        <div class="text-content">是沒錯他就是邊邊路 但是他會是哪一段可是他也是用斷路啊 用斷路</div>
      </div><div class="transcription-item" id="segment-241">
        <div class="timestamp">下午2:17:03 - 段落 241 (4.2秒) [質量: 100%]</div>
        <div class="text-content">即使在去處理我才應該因為流量吧</div>
      </div><div class="transcription-item" id="segment-242">
        <div class="timestamp">下午2:17:06 - 段落 242 (3.1秒) [質量: 100%]</div>
        <div class="text-content">那他在玩,那一下就搜尋了</div>
      </div><div class="transcription-item" id="segment-243">
        <div class="timestamp">下午2:17:09 - 段落 243 (3.1秒) [質量: 100%]</div>
        <div class="text-content">我們現在也很難</div>
      </div><div class="transcription-item" id="segment-244">
        <div class="timestamp">下午2:17:11 - 段落 244 (3.1秒) [質量: 100%]</div>
        <div class="text-content">我這邊這個</div>
      </div><div class="transcription-item" id="segment-245">
        <div class="timestamp">下午2:17:14 - 段落 245 (3.1秒) [質量: 100%]</div>
        <div class="text-content">先求有,後面</div>
      </div><div class="transcription-item" id="segment-246">
        <div class="timestamp">下午2:17:17 - 段落 246 (3.1秒) [質量: 100%]</div>
        <div class="text-content">這個再回修這個功能再</div>
      </div><div class="transcription-item filtered" id="segment-247" style="display: none;">
        <div class="timestamp">下午2:17:21 - 段落 247 (3.9秒) [質量: 100%]</div>
        <div class="text-content">🚫 已過濾：內容過短</div>
      </div><div class="transcription-item filtered" id="segment-248" style="display: none;">
        <div class="timestamp">下午2:17:24 - 段落 248 (3.8秒) [質量: 100%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-249" style="display: none;">
        <div class="timestamp">下午2:17:27 - 段落 249 (3.1秒) [質量: 60%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-250" style="display: none;">
        <div class="timestamp">下午2:17:30 - 段落 250 (3.1秒) [質量: 100%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item filtered" id="segment-251" style="display: none;">
        <div class="timestamp">下午2:17:33 - 段落 251 (3.1秒) [質量: 100%]</div>
        <div class="text-content">🚫 已過濾：內容過短</div>
      </div><div class="transcription-item" id="segment-252">
        <div class="timestamp">下午2:17:35 - 段落 252 (3.1秒) [質量: 100%]</div>
        <div class="text-content">你可以</div>
      </div><div class="transcription-item" id="segment-253">
        <div class="timestamp">下午2:17:38 - 段落 253 (3.1秒) [質量: 100%]</div>
        <div class="text-content">這小小按你</div>
      </div><div class="transcription-item" id="segment-254">
        <div class="timestamp">下午2:17:41 - 段落 254 (3.1秒) [質量: 100%]</div>
        <div class="text-content">這個還有圖片的那一個</div>
      </div><div class="transcription-item" id="segment-255">
        <div class="timestamp">下午2:17:45 - 段落 255 (4.3秒) [質量: 100%]</div>
        <div class="text-content">乾脆又把它結合</div>
      </div><div class="transcription-item filtered" id="segment-256" style="display: none;">
        <div class="timestamp">下午2:17:49 - 段落 256 (3.9秒) [質量: 100%]</div>
        <div class="text-content">🚫 已過濾：內容過短</div>
      </div><div class="transcription-item" id="segment-257">
        <div class="timestamp">下午2:17:53 - 段落 257 (4.4秒) [質量: 100%]</div>
        <div class="text-content">同一個夜面之類的</div>
      </div><div class="transcription-item" id="segment-258">
        <div class="timestamp">下午2:17:56 - 段落 258 (3.5秒) [質量: 100%]</div>
        <div class="text-content">就上面那個會</div>
      </div><div class="transcription-item" id="segment-259">
        <div class="timestamp">下午2:17:59 - 段落 259 (3.1秒) [質量: 100%]</div>
        <div class="text-content">經理嘛會一致錄嘛對不對</div>
      </div><div class="transcription-item" id="segment-260">
        <div class="timestamp">下午2:18:02 - 段落 260 (3.6秒) [質量: 100%]</div>
        <div class="text-content">然後這個部分還有</div>
      </div><div class="transcription-item" id="segment-261">
        <div class="timestamp">下午2:18:06 - 段落 261 (4.2秒) [質量: 100%]</div>
        <div class="text-content">主持高轉移這個夜面其實</div>
      </div><div class="transcription-item" id="segment-262">
        <div class="timestamp">下午2:18:09 - 段落 262 (3.3秒) [質量: 100%]</div>
        <div class="text-content">可以把他整合起來,按了你圖片的部分</div>
      </div><div class="transcription-item filtered" id="segment-263" style="display: none;">
        <div class="timestamp">下午2:18:12 - 段落 263 (3.1秒) [質量: 100%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item" id="segment-264">
        <div class="timestamp">下午2:18:15 - 段落 264 (3.6秒) [質量: 100%]</div>
        <div class="text-content">我丢的</div>
      </div><div class="transcription-item" id="segment-265">
        <div class="timestamp">下午2:18:18 - 段落 265 (3.2秒) [質量: 100%]</div>
        <div class="text-content">那是蜜蜂 蜜蜂</div>
      </div><div class="transcription-item" id="segment-266">
        <div class="timestamp">下午2:18:23 - 段落 266 (5.8秒) [質量: 80%]</div>
        <div class="text-content">所以要死月年快就到了啊啊啊啊啊不是要面對啊我用</div>
      </div><div class="transcription-item" id="segment-267">
        <div class="timestamp">下午2:18:26 - 段落 267 (3.1秒) [質量: 100%]</div>
        <div class="text-content">我跟下来的东西</div>
      </div><div class="transcription-item" id="segment-268">
        <div class="timestamp">下午2:18:29 - 段落 268 (3.3秒) [質量: 100%]</div>
        <div class="text-content">我昨天晚上一直在想在弄</div>
      </div><div class="transcription-item" id="segment-269">
        <div class="timestamp">下午2:18:33 - 段落 269 (4.3秒) [質量: 100%]</div>
        <div class="text-content">然後後面今天早上反正這幾天那個</div>
      </div><div class="transcription-item" id="segment-270">
        <div class="timestamp">下午2:18:36 - 段落 270 (3.1秒) [質量: 100%]</div>
        <div class="text-content">唯大大不在,比較有空</div>
      </div><div class="transcription-item" id="segment-271">
        <div class="timestamp">下午2:18:39 - 段落 271 (3.1秒) [質量: 100%]</div>
        <div class="text-content">我真的用這個</div>
      </div><div class="transcription-item filtered" id="segment-272" style="display: none;">
        <div class="timestamp">下午2:18:41 - 段落 272 (3.1秒) [質量: 100%]</div>
        <div class="text-content">🚫 已過濾：內容過短</div>
      </div><div class="transcription-item" id="segment-273">
        <div class="timestamp">下午2:18:44 - 段落 273 (3.4秒) [質量: 100%]</div>
        <div class="text-content">想要東西講,然後慢慢</div>
      </div><div class="transcription-item" id="segment-274">
        <div class="timestamp">下午2:18:47 - 段落 274 (3.1秒) [質量: 100%]</div>
        <div class="text-content">就可以把它湊起來</div>
      </div><div class="transcription-item" id="segment-275">
        <div class="timestamp">下午2:18:50 - 段落 275 (3.1秒) [質量: 100%]</div>
        <div class="text-content">像這個就花兩天而已就</div>
      </div><div class="transcription-item" id="segment-276">
        <div class="timestamp">下午2:18:53 - 段落 276 (3.1秒) [質量: 100%]</div>
        <div class="text-content">可以處理到這樣子</div>
      </div><div class="transcription-item" id="segment-277">
        <div class="timestamp">下午2:18:56 - 段落 277 (3.5秒) [質量: 100%]</div>
        <div class="text-content">嗯對啊,所以先有技喔</div>
      </div><div class="transcription-item" id="segment-278">
        <div class="timestamp">下午2:18:59 - 段落 278 (3.1秒) [質量: 100%]</div>
        <div class="text-content">你想做到怎麽樣子</div>
      </div><div class="transcription-item" id="segment-279">
        <div class="timestamp">下午2:19:02 - 段落 279 (3.1秒) [質量: 60%]</div>
        <div class="text-content">但是他比較</div>
      </div><div class="transcription-item" id="segment-280">
        <div class="timestamp">下午2:19:05 - 段落 280 (3.1秒) [質量: 100%]</div>
        <div class="text-content">好一點就是我之前的那個</div>
      </div><div class="transcription-item" id="segment-281">
        <div class="timestamp">下午2:19:08 - 段落 281 (3.9秒) [質量: 100%]</div>
        <div class="text-content">那個會自錄已經要給</div>
      </div><div class="transcription-item" id="segment-282">
        <div class="timestamp">下午2:19:11 - 段落 282 (3.3秒) [質量: 100%]</div>
        <div class="text-content">基礎,去找到,OK</div>
      </div><div class="transcription-item" id="segment-283">
        <div class="timestamp">下午2:19:15 - 段落 283 (3.5秒) [質量: 100%]</div>
        <div class="text-content">去用分下</div>
      </div><div class="transcription-item" id="segment-284">
        <div class="timestamp">下午2:19:18 - 段落 284 (3.9秒) [質量: 100%]</div>
        <div class="text-content">默画面的方式往下就可以去</div>
      </div><div class="transcription-item" id="segment-285">
        <div class="timestamp">下午2:19:22 - 段落 285 (3.8秒) [質量: 100%]</div>
        <div class="text-content">做那個聲音的結局</div>
      </div><div class="transcription-item" id="segment-286">
        <div class="timestamp">下午2:19:25 - 段落 286 (3.2秒) [質量: 100%]</div>
        <div class="text-content">後面倒比較困埋</div>
      </div><div class="transcription-item" id="segment-287">
        <div class="timestamp">下午2:19:28 - 段落 287 (3.7秒) [質量: 100%]</div>
        <div class="text-content">要不然我還沒提供他這個功能之前</div>
      </div><div class="transcription-item" id="segment-288">
        <div class="timestamp">下午2:19:31 - 段落 288 (3.1秒) [質量: 100%]</div>
        <div class="text-content">昨天我再死啦</div>
      </div><div class="transcription-item" id="segment-289">
        <div class="timestamp">下午2:19:34 - 段落 289 (3.3秒) [質量: 100%]</div>
        <div class="text-content">所有的功能到后面都卡住</div>
      </div><div class="transcription-item" id="segment-290">
        <div class="timestamp">下午2:19:37 - 段落 290 (3.4秒) [質量: 100%]</div>
        <div class="text-content">沒辦法這樣丟現在我一個人</div>
      </div><div class="transcription-item" id="segment-291">
        <div class="timestamp">下午2:19:40 - 段落 291 (3.3秒) [質量: 100%]</div>
        <div class="text-content">看到真的可以用網頁在這樣子做這個</div>
      </div><div class="transcription-item" id="segment-292">
        <div class="timestamp">下午2:19:43 - 段落 292 (3.1秒) [質量: 100%]</div>
        <div class="text-content">用功能的部分就只有用碗</div>
      </div><div class="transcription-item" id="segment-293">
        <div class="timestamp">下午2:19:46 - 段落 293 (3.7秒) [質量: 100%]</div>
        <div class="text-content">越分享這一塊,才要辦法這樣做</div>
      </div><div class="transcription-item filtered" id="segment-294" style="display: none;">
        <div class="timestamp">下午2:19:49 - 段落 294 (3.1秒) [質量: 100%]</div>
        <div class="text-content">🚫 已過濾：內容過短</div>
      </div><div class="transcription-item filtered" id="segment-295" style="display: none;">
        <div class="timestamp">下午2:19:52 - 段落 295 (3.1秒) [質量: 100%]</div>
        <div class="text-content">🚫 已過濾：內容過短</div>
      </div><div class="transcription-item filtered" id="segment-296" style="display: none;">
        <div class="timestamp">下午2:19:55 - 段落 296 (3.1秒) [質量: 100%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item" id="segment-297">
        <div class="timestamp">下午2:20:00 - 段落 297 (5.8秒) [質量: 100%]</div>
        <div class="text-content">好吧那就先這樣看明好 感謝俊雄哥的分享</div>
      </div><div class="transcription-item" id="segment-298">
        <div class="timestamp">下午2:20:03 - 段落 298 (3.1秒) [質量: 100%]</div>
        <div class="text-content">以我為你揭霉</div>
      </div><div class="transcription-item" id="segment-299">
        <div class="timestamp">下午2:20:06 - 段落 299 (3.7秒) [質量: 100%]</div>
        <div class="text-content">可以在ID的角色</div>
      </div><div class="transcription-item filtered" id="segment-300" style="display: none;">
        <div class="timestamp">下午2:20:09 - 段落 300 (3.3秒) [質量: 100%]</div>
        <div class="text-content">🚫 已過濾：內容過短</div>
      </div><div class="transcription-item filtered" id="segment-301" style="display: none;">
        <div class="timestamp">下午2:20:12 - 段落 301 (3.1秒) [質量: 100%]</div>
        <div class="text-content">🚫 已過濾：內容過短</div>
      </div><div class="transcription-item" id="segment-302">
        <div class="timestamp">下午2:20:15 - 段落 302 (3.6秒) [質量: 100%]</div>
        <div class="text-content">他沒有他沒有</div>
      </div><div class="transcription-item" id="segment-303">
        <div class="timestamp">下午2:20:18 - 段落 303 (3.2秒) [質量: 100%]</div>
        <div class="text-content">不然不能有...在奇怪他沒有...</div>
      </div><div class="transcription-item" id="segment-304">
        <div class="timestamp">下午2:20:21 - 段落 304 (3.1秒) [質量: 100%]</div>
        <div class="text-content">中英文夹杂</div>
      </div><div class="transcription-item" id="segment-305">
        <div class="timestamp">下午2:20:24 - 段落 305 (3.1秒) [質量: 80%]</div>
        <div class="text-content">有的還有庄議員,剛剛那個Whisper還有</div>
      </div><div class="transcription-item" id="segment-306">
        <div class="timestamp">下午2:20:27 - 段落 306 (3.3秒) [質量: 100%]</div>
        <div class="text-content">好用的咖啡</div>
      </div><div class="transcription-item" id="segment-307">
        <div class="timestamp">下午2:20:31 - 段落 307 (4.6秒) [質量: 80%]</div>
        <div class="text-content">许红哥的 idea 和发的部队</div>
      </div><div class="transcription-item filtered" id="segment-308" style="display: none;">
        <div class="timestamp">下午2:20:34 - 段落 308 (3.3秒) [質量: 100%]</div>
        <div class="text-content">🚫 已過濾：重複字符</div>
      </div><div class="transcription-item" id="segment-309">
        <div class="timestamp">下午2:20:38 - 段落 309 (4.1秒) [質量: 100%]</div>
        <div class="text-content">所以他還是會挑你講的英文</div>
      </div><div class="transcription-item" id="segment-310">
        <div class="timestamp">下午2:20:42 - 段落 310 (5.0秒) [質量: 100%]</div>
        <div class="text-content">對對對他可能比較喜歡紅姐子</div>
      </div><div class="transcription-item filtered" id="segment-311" style="display: none;">
        <div class="timestamp">下午2:20:45 - 段落 311 (3.1秒) [質量: 100%]</div>
        <div class="text-content">🚫 已過濾：內容過短</div>
      </div><div class="transcription-item" id="segment-312">
        <div class="timestamp">下午2:20:50 - 段落 312 (4.8秒) [質量: 100%]</div>
        <div class="text-content">你現在一起的紅節嗎?</div>
      </div><div class="transcription-item filtered" id="segment-313" style="display: none;">
        <div class="timestamp">下午2:20:53 - 段落 313 (3.5秒) [質量: 100%]</div>
        <div class="text-content">🚫 已過濾：重複字符</div>
      </div><div class="transcription-item" id="segment-314">
        <div class="timestamp">下午2:20:57 - 段落 314 (4.4秒) [質量: 100%]</div>
        <div class="text-content">不是一起的红结是对他的那个红结我知道啊那个</div>
      </div><div class="transcription-item" id="segment-315">
        <div class="timestamp">下午2:21:01 - 段落 315 (4.6秒) [質量: 100%]</div>
        <div class="text-content">冷夜,不再恐懼,恐懼</div>
      </div><div class="transcription-item" id="segment-316">
        <div class="timestamp">下午2:21:04 - 段落 316 (3.6秒) [質量: 100%]</div>
        <div class="text-content">他...他...他...</div>
      </div><div class="transcription-item" id="segment-317">
        <div class="timestamp">下午2:21:07 - 段落 317 (3.2秒) [質量: 100%]</div>
        <div class="text-content">那個可能是抹</div>
      </div><div class="transcription-item" id="segment-318">
        <div class="timestamp">下午2:21:10 - 段落 318 (3.3秒) [質量: 100%]</div>
        <div class="text-content">那某裂偶像</div>
      </div><div class="transcription-item" id="segment-319">
        <div class="timestamp">下午2:21:14 - 段落 319 (3.6秒) [質量: 100%]</div>
        <div class="text-content">好,那倒在就這樣子</div>
      </div><div class="transcription-item" id="segment-320">
        <div class="timestamp">下午2:21:17 - 段落 320 (3.1秒) [質量: 100%]</div>
        <div class="text-content">好啦,我能吃什麼東西</div>
      </div><div class="transcription-item" id="segment-321">
        <div class="timestamp">下午2:21:19 - 段落 321 (3.2秒) [質量: 100%]</div>
        <div class="text-content">其他沒辦法坐在有的屁屋</div>
      </div><div class="transcription-item" id="segment-322">
        <div class="timestamp">下午2:21:23 - 段落 322 (3.8秒) [質量: 100%]</div>
        <div class="text-content">这个可以做出来继续续续续</div>
      </div><div class="transcription-item" id="segment-323">
        <div class="timestamp">下午2:21:26 - 段落 323 (3.3秒) [質量: 100%]</div>
        <div class="text-content">不過我覺得這顆</div>
      </div><div class="transcription-item" id="segment-324">
        <div class="timestamp">下午2:21:29 - 段落 324 (3.4秒) [質量: 100%]</div>
        <div class="text-content">沒有值在想他的這個後台的</div>
      </div><div class="transcription-item" id="segment-325">
        <div class="timestamp">下午2:21:32 - 段落 325 (3.1秒) [質量: 100%]</div>
        <div class="text-content">后台的那个应该是一样的Visper</div>
      </div><div class="transcription-item" id="segment-326">
        <div class="timestamp">下午2:21:37 - 段落 326 (5.9秒) [質量: 100%]</div>
        <div class="text-content">對 你覺得他能翻你的字我跟你講 因為我原本用FaceAp</div>
      </div><div class="transcription-item" id="segment-327">
        <div class="timestamp">下午2:21:40 - 段落 327 (3.1秒) [質量: 100%]</div>
        <div class="text-content">Fast Whisper proven,還有Wonti</div>
      </div><div class="transcription-item" id="segment-328">
        <div class="timestamp">下午2:21:44 - 段落 328 (3.9秒) [質量: 100%]</div>
        <div class="text-content">所以到後面就能用一個</div>
      </div><div class="transcription-item" id="segment-329">
        <div class="timestamp">下午2:21:47 - 段落 329 (3.3秒) [質量: 100%]</div>
        <div class="text-content">偶封airways盒,踏踩去正確</div>
      </div><div class="transcription-item" id="segment-330">
        <div class="timestamp">下午2:21:50 - 段落 330 (3.1秒) [質量: 100%]</div>
        <div class="text-content">所以没办法然后</div>
      </div><div class="transcription-item" id="segment-331">
        <div class="timestamp">下午2:21:53 - 段落 331 (3.6秒) [質量: 100%]</div>
        <div class="text-content">你的FPS是最大的嗎?</div>
      </div><div class="transcription-item" id="segment-332">
        <div class="timestamp">下午2:21:56 - 段落 332 (3.5秒) [質量: 100%]</div>
        <div class="text-content">不是,我現在只能要base,因為...</div>
      </div><div class="transcription-item" id="segment-333">
        <div class="timestamp">下午2:21:59 - 段落 333 (3.1秒) [質量: 100%]</div>
        <div class="text-content">他喔,在</div>
      </div><div class="transcription-item" id="segment-334">
        <div class="timestamp">下午2:22:03 - 段落 334 (4.2秒) [質量: 100%]</div>
        <div class="text-content">樹肚上來講的話這是塊對</div>
      </div><div class="transcription-item" id="segment-335">
        <div class="timestamp">下午2:22:06 - 段落 335 (3.3秒) [質量: 100%]</div>
        <div class="text-content">然後他的質量會降低</div>
      </div><div class="transcription-item" id="segment-336">
        <div class="timestamp">下午2:22:09 - 段落 336 (3.4秒) [質量: 100%]</div>
        <div class="text-content">但問題是輸出這樣才是追</div>
      </div><div class="transcription-item" id="segment-337">
        <div class="timestamp">下午2:22:13 - 段落 337 (4.0秒) [質量: 100%]</div>
        <div class="text-content">對啊因為滾回阿翁呢</div>
      </div><div class="transcription-item" id="segment-338">
        <div class="timestamp">下午2:22:16 - 段落 338 (3.4秒) [質量: 100%]</div>
        <div class="text-content">然後就直接用一瓶就是</div>
      </div><div class="transcription-item" id="segment-339">
        <div class="timestamp">下午2:22:19 - 段落 339 (3.9秒) [質量: 100%]</div>
        <div class="text-content">之前我們去年測的時候是還好</div>
      </div><div class="transcription-item" id="segment-340">
        <div class="timestamp">下午2:22:23 - 段落 340 (4.1秒) [質量: 100%]</div>
        <div class="text-content">因為他要丟過去才丟回來,丟過去就丟回來</div>
      </div><div class="transcription-item" id="segment-341">
        <div class="timestamp">下午2:22:26 - 段落 341 (3.6秒) [質量: 100%]</div>
        <div class="text-content">所以他的傳說時間</div>
      </div><div class="transcription-item" id="segment-342">
        <div class="timestamp">下午2:22:29 - 段落 342 (3.1秒) [質量: 100%]</div>
        <div class="text-content">然後他還不能一直丟太</div>
      </div><div class="transcription-item" id="segment-343">
        <div class="timestamp">下午2:22:32 - 段落 343 (3.1秒) [質量: 100%]</div>
        <div class="text-content">呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃�</div>
      </div><div class="transcription-item" id="segment-344">
        <div class="timestamp">下午2:22:35 - 段落 344 (3.1秒) [質量: 100%]</div>
        <div class="text-content">通然是他最常可能</div>
      </div><div class="transcription-item" id="segment-345">
        <div class="timestamp">下午2:22:39 - 段落 345 (4.4秒) [質量: 100%]</div>
        <div class="text-content">只能不能超過10秒之類的</div>
      </div><div class="transcription-item filtered" id="segment-346" style="display: none;">
        <div class="timestamp">下午2:22:42 - 段落 346 (3.7秒) [質量: 100%]</div>
        <div class="text-content">🔇 無語音內容</div>
      </div><div class="transcription-item" id="segment-347">
        <div class="timestamp">下午2:22:45 - 段落 347 (3.3秒) [質量: 100%]</div>
        <div class="text-content">三點點錢,三點額</div>
      </div><div class="transcription-item" id="segment-348">
        <div class="timestamp">下午2:22:48 - 段落 348 (3.1秒) [質量: 100%]</div>
        <div class="text-content">主要部分</div>
      </div><div class="transcription-item" id="segment-349">
        <div class="timestamp">下午2:22:51 - 段落 349 (3.1秒) [質量: 100%]</div>
        <div class="text-content">除了安德加汽焉,呢,</div>
      </div><div class="transcription-item" id="segment-350">
        <div class="timestamp">下午2:22:54 - 段落 350 (3.1秒) [質量: 100%]</div>
        <div class="text-content">要切的那個長度</div>
      </div><div class="transcription-item" id="segment-351">
        <div class="timestamp">下午2:22:57 - 段落 351 (3.2秒) [質量: 100%]</div>
        <div class="text-content">還要用那個</div>
      </div><div class="transcription-item" id="segment-352">
        <div class="timestamp">下午2:23:00 - 段落 352 (3.3秒) [質量: 100%]</div>
        <div class="text-content">中間的聲音有中段</div>
      </div><div class="transcription-item" id="segment-353">
        <div class="timestamp">下午2:23:03 - 段落 353 (3.3秒) [質量: 100%]</div>
        <div class="text-content">嗯,然後去送</div>
      </div><div class="transcription-item filtered" id="segment-354" style="display: none;">
        <div class="timestamp">下午2:23:06 - 段落 354 (3.1秒) [質量: 100%]</div>
        <div class="text-content">🚫 已過濾：內容過短</div>
      </div><div class="transcription-item" id="segment-355">
        <div class="timestamp">下午2:23:09 - 段落 355 (3.3秒) [質量: 100%]</div>
        <div class="text-content">對,對,為何很沒信任</div>
      </div><div class="transcription-item filtered" id="segment-356" style="display: none;">
        <div class="timestamp">下午2:23:11 - 段落 356 (3.2秒) [質量: 100%]</div>
        <div class="text-content">🚫 已過濾：僅含語助詞</div>
      </div><div class="transcription-item" id="segment-357">
        <div class="timestamp">下午2:23:16 - 段落 357 (4.4秒) [質量: 100%]</div>
        <div class="text-content">它短它還不順勢</div>
      </div><div class="transcription-item" id="segment-358">
        <div class="timestamp">下午2:23:23 - 段落 358 (7.8秒) [質量: 100%]</div>
        <div class="text-content">好啦,沒洗飯店,這個排,東西就是有一段路要走</div>
      </div><div class="transcription-item filtered" id="segment-359" style="display: none;">
        <div class="timestamp">下午2:23:24 - 段落 359 (1.3秒) [質量: 100%]</div>
        <div class="text-content">🚫 已過濾：內容過短</div>
      </div></div>
    </div>
  </div>

  <script>
    // 全局變量
    let capturedStream, micStream, audioContext;
    let isRecording = false;
    let audioBuffer = [];
    let currentSegmentId = 0;
    let showFiltered = false;
    let enableFiltering = true;
    
    // 自適應參數
    let adaptiveParams = {
      silenceThreshold: 0.01,
      silenceDuration: 1000,
      minAudioLength: 3,
      maxAudioLength: 30,
      environmentType: 'unknown',
      avgVolume: 0,
      volumeHistory: [],
      segmentQuality: []
    };
    
    // 統計數據
    let stats = {
      totalSegments: 0,
      validSegments: 0,
      filteredSegments: 0,
      avgConfidence: 0
    };

    const startCaptureButton = document.getElementById('startCapture');
    const stopCaptureButton = document.getElementById('stopCapture');
    const audioQualitySelect = document.getElementById('audioQuality');

    // 切換過濾模式
    document.getElementById('toggleFiltering').addEventListener('click', () => {
      enableFiltering = !enableFiltering;
      const btn = document.getElementById('toggleFiltering');
      btn.textContent = enableFiltering ? '🔍 過濾模式：開啟' : '🔍 過濾模式：關閉';
      btn.className = enableFiltering ? 'secondary' : 'warning';
    });

    // 顯示/隱藏已過濾項目
    document.getElementById('showFilteredBtn').addEventListener('click', showFilteredItems);

    // 開始自適應轉錄
    startCaptureButton.addEventListener('click', async () => {
      try {
        const statusDiv = document.getElementById('captureStatus');
        statusDiv.innerHTML = '<div class="status info">🔄 啟動自適應轉錄系統...</div>';
        
        const [audioBitRate, audioSampleRate] = audioQualitySelect.value.split(',').map(Number);

        // 捕捉音頻
        capturedStream = await navigator.mediaDevices.getDisplayMedia({
          video: { width: { ideal: 1280 }, height: { ideal: 720 }, frameRate: { ideal: 30 } },
          audio: {
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false,
            sampleRate: audioSampleRate,
            channelCount: 2
          }
        });

        micStream = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false
          }
        });

        // 建立音頻混合
        audioContext = new AudioContext();
        const destination = audioContext.createMediaStreamDestination();

        if (capturedStream.getAudioTracks().length > 0) {
          const systemAudioSource = audioContext.createMediaStreamSource(capturedStream);
          systemAudioSource.connect(destination);
        }
        if (micStream.getAudioTracks().length > 0) {
          const micAudioSource = audioContext.createMediaStreamSource(micStream);
          micAudioSource.connect(destination);
        }

        // 設置自適應處理
        setupAdaptiveProcessing(destination.stream);

        startCaptureButton.disabled = true;
        stopCaptureButton.disabled = false;
        startCaptureButton.classList.add('recording');
        
        statusDiv.innerHTML = '<div class="status success">🤖 自適應轉錄中 (智能優化)</div>';
        isRecording = true;

        // 開始環境學習
        startEnvironmentLearning();

        capturedStream.getTracks().forEach(track => {
          track.onended = () => stopCapture();
        });

      } catch (err) {
        console.error('自適應轉錄啟動失敗:', err);
        document.getElementById('captureStatus').innerHTML = 
          `<div class="status error">❌ 啟動失敗：${err.message}</div>`;
      }
    });

    // 停止轉錄
    stopCaptureButton.addEventListener('click', stopCapture);

    function stopCapture() {
      isRecording = false;
      
      if (audioBuffer.length > 0) {
        processRemainingAudio();
      }
      
      [capturedStream, micStream].forEach(stream => {
        if (stream) {
          stream.getTracks().forEach(track => track.stop());
        }
      });
      
      if (audioContext) {
        audioContext.close();
        audioContext = null;
      }
      
      startCaptureButton.disabled = false;
      stopCaptureButton.disabled = true;
      startCaptureButton.classList.remove('recording');
      
      document.getElementById('captureStatus').innerHTML = 
        '<div class="status info">⏹️ 自適應轉錄已停止</div>';
      
      updateAdaptiveStatus();
    }

    // 設置自適應處理
    function setupAdaptiveProcessing(mixedStream) {
      const source = audioContext.createMediaStreamSource(mixedStream);
      const processor = audioContext.createScriptProcessor(4096, 1, 1);

      processor.onaudioprocess = function(event) {
        if (!isRecording) return;
        
        const audioData = event.inputBuffer.getChannelData(0);
        processAdaptiveAudio(audioData);
      };

      source.connect(processor);
      processor.connect(audioContext.destination);
    }

    // 自適應音頻處理
    function processAdaptiveAudio(audioData) {
      // 計算音量
      let sum = 0;
      for (let i = 0; i < audioData.length; i++) {
        sum += audioData[i] * audioData[i];
      }
      const rms = Math.sqrt(sum / audioData.length);
      
      // 更新音量歷史
      adaptiveParams.volumeHistory.push(rms);
      if (adaptiveParams.volumeHistory.length > 1000) {
        adaptiveParams.volumeHistory.shift();
      }
      
      // 動態調整閾值
      updateDynamicThreshold();
      
      // 更新視覺化
      updateAdaptiveVisualization(rms);
      
      // 添加到緩衝區
      audioBuffer.push(...audioData);
      
      const bufferDurationMs = (audioBuffer.length / 44100) * 1000;
      
      // 智能分段判斷
      if (shouldSegmentAudio(rms, bufferDurationMs)) {
        processAudioSegment();
      }
    }

    // 動態調整閾值
    function updateDynamicThreshold() {
      if (adaptiveParams.volumeHistory.length < 100) return;
      
      const recent = adaptiveParams.volumeHistory.slice(-100);
      const avg = recent.reduce((a, b) => a + b, 0) / recent.length;
      const std = Math.sqrt(recent.reduce((a, b) => a + (b - avg) ** 2, 0) / recent.length);
      
      // 動態調整閾值
      adaptiveParams.silenceThreshold = Math.max(0.005, avg - std * 1.5);
      adaptiveParams.avgVolume = avg;
      
      // 環境類型檢測
      if (avg < 0.01 && std < 0.005) {
        adaptiveParams.environmentType = '安靜環境';
        adaptiveParams.silenceDuration = 800;
      } else if (avg > 0.05 || std > 0.02) {
        adaptiveParams.environmentType = '嘈雜環境';
        adaptiveParams.silenceDuration = 1500;
      } else {
        adaptiveParams.environmentType = '一般環境';
        adaptiveParams.silenceDuration = 1000;
      }
    }

    // 智能分段判斷
    function shouldSegmentAudio(rms, bufferDurationMs) {
      const isQuiet = rms < adaptiveParams.silenceThreshold;
      const minDuration = adaptiveParams.minAudioLength * 1000;
      const maxDuration = adaptiveParams.maxAudioLength * 1000;
      
      // 強制分段（避免過長）
      if (bufferDurationMs >= maxDuration) {
        return true;
      }
      
      // 靜音分段
      if (isQuiet && bufferDurationMs >= minDuration) {
        return true;
      }
      
      return false;
    }

    // 處理音頻段
    function processAudioSegment() {
      if (audioBuffer.length === 0) return;
      
      const segmentId = ++currentSegmentId;
      const audioBlob = createWavBlob(new Float32Array(audioBuffer), 44100);
      const duration = (audioBuffer.length / 44100).toFixed(1);
      
      // 預評估音頻質量
      const quality = evaluateAudioQuality(audioBuffer);
      
      if (enableFiltering && quality.score < 0.3) {
        // 低質量音頻，直接過濾
        stats.filteredSegments++;
        addFilteredTranscription(segmentId, duration, '音頻質量過低，已自動過濾');
      } else {
        // 添加處理中項目
        addProcessingTranscription(segmentId, duration, quality.score);
        sendAudioForTranscription(audioBlob, segmentId, quality);
      }
      
      audioBuffer = [];
      stats.totalSegments++;
    }

    // 評估音頻質量
    function evaluateAudioQuality(buffer) {
      let energy = 0;
      let zeroCrossings = 0;
      
      for (let i = 0; i < buffer.length; i++) {
        energy += buffer[i] * buffer[i];
        if (i > 0 && buffer[i] * buffer[i-1] < 0) {
          zeroCrossings++;
        }
      }
      
      const rms = Math.sqrt(energy / buffer.length);
      const zcr = zeroCrossings / buffer.length;
      
      // 質量評分 (0-1)
      let score = 0;
      if (rms > 0.005) score += 0.4; // 有足夠音量
      if (rms < 0.1) score += 0.2;   // 不過載
      if (zcr > 0.01 && zcr < 0.1) score += 0.4; // 合理的頻率變化
      
      return {
        score: score,
        rms: rms,
        zcr: zcr,
        quality: score > 0.7 ? '高' : score > 0.4 ? '中' : '低'
      };
    }

    // 開始環境學習
    function startEnvironmentLearning() {
      setInterval(() => {
        if (isRecording) {
          updateAdaptiveStatus();
        }
      }, 2000);
    }

    // 更新自適應狀態
    function updateAdaptiveStatus() {
      document.getElementById('environmentType').textContent = adaptiveParams.environmentType;
      document.getElementById('dynamicThreshold').textContent = adaptiveParams.silenceThreshold.toFixed(4);
      document.getElementById('segmentStrategy').textContent = 
        `${adaptiveParams.minAudioLength}-${adaptiveParams.maxAudioLength}秒 / ${adaptiveParams.silenceDuration}ms`;
      
      const efficiency = stats.totalSegments > 0 ? 
        ((stats.filteredSegments / stats.totalSegments) * 100).toFixed(1) : 0;
      document.getElementById('filterEfficiency').textContent = 
        `${efficiency}% (${stats.filteredSegments}/${stats.totalSegments})`;
    }

    // 更新自適應視覺化
    function updateAdaptiveVisualization(rms) {
      const volumeBar = document.getElementById('volumeBar');
      const adaptiveInfo = document.getElementById('adaptiveInfo');
      const silenceInfo = document.getElementById('silenceInfo');
      const qualityIndicator = document.getElementById('qualityIndicator');
      
      const volumePercent = Math.min(rms * 1000, 100);
      volumeBar.style.width = volumePercent + '%';
      
      adaptiveInfo.textContent = `自適應：${adaptiveParams.environmentType}`;
      silenceInfo.textContent = `閾值：${adaptiveParams.silenceThreshold.toFixed(4)} | 音量：${rms.toFixed(4)}`;
      
      const quality = rms > adaptiveParams.silenceThreshold ? '良好' : '靜音';
      qualityIndicator.textContent = `狀態：${quality}`;
    }

    // 處理剩餘音頻
    function processRemainingAudio() {
      if (audioBuffer.length > 0) {
        const duration = (audioBuffer.length / 44100).toFixed(1);
        if (parseFloat(duration) >= 1.0) {
          processAudioSegment();
        }
      }
    }

    // 創建 WAV Blob
    function createWavBlob(audioData, sampleRate) {
      const length = audioData.length;
      const buffer = new ArrayBuffer(44 + length * 2);
      const view = new DataView(buffer);
      
      const writeString = (offset, string) => {
        for (let i = 0; i < string.length; i++) {
          view.setUint8(offset + i, string.charCodeAt(i));
        }
      };
      
      writeString(0, 'RIFF');
      view.setUint32(4, 36 + length * 2, true);
      writeString(8, 'WAVE');
      writeString(12, 'fmt ');
      view.setUint32(16, 16, true);
      view.setUint16(20, 1, true);
      view.setUint16(22, 1, true);
      view.setUint32(24, sampleRate, true);
      view.setUint32(28, sampleRate * 2, true);
      view.setUint16(32, 2, true);
      view.setUint16(34, 16, true);
      writeString(36, 'data');
      view.setUint32(40, length * 2, true);
      
      let offset = 44;
      for (let i = 0; i < length; i++) {
        const sample = Math.max(-1, Math.min(1, audioData[i]));
        view.setInt16(offset, sample * 0x7FFF, true);
        offset += 2;
      }
      
      return new Blob([buffer], { type: 'audio/wav' });
    }

    // 添加處理中轉錄
    function addProcessingTranscription(segmentId, duration, qualityScore) {
      const display = document.getElementById('transcriptionDisplay');
      const timestamp = new Date().toLocaleTimeString();

      const item = document.createElement('div');
      item.className = 'transcription-item processing';
      item.id = `segment-${segmentId}`;
      item.innerHTML = `
        <div class="timestamp">${timestamp} - 段落 ${segmentId} (${duration}秒) [質量: ${(qualityScore * 100).toFixed(0)}%]</div>
        <div class="text-content">🔄 智能轉錄中...</div>
      `;

      display.appendChild(item);
      display.scrollTop = display.scrollHeight;
    }

    // 添加已過濾轉錄
    function addFilteredTranscription(segmentId, duration, reason) {
      const display = document.getElementById('transcriptionDisplay');
      const timestamp = new Date().toLocaleTimeString();

      const item = document.createElement('div');
      item.className = 'transcription-item filtered';
      item.id = `segment-${segmentId}`;
      item.style.display = showFiltered ? 'block' : 'none';
      item.innerHTML = `
        <div class="timestamp">${timestamp} - 段落 ${segmentId} (${duration}秒) [已過濾]</div>
        <div class="text-content">🚫 ${reason}</div>
      `;

      display.appendChild(item);
      display.scrollTop = display.scrollHeight;
    }

    // 發送轉錄
    async function sendAudioForTranscription(audioBlob, segmentId, quality) {
      try {
        const formData = new FormData();
        formData.append('audio_file', audioBlob, `adaptive_${segmentId}.wav`);

        const response = await fetch('/api/transcribe', {
          method: 'POST',
          body: formData
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`);
        }

        const data = await response.json();
        
        // 智能內容過濾
        const filteredResult = intelligentContentFilter(data.transcription || '', quality);
        updateTranscriptionResult(segmentId, filteredResult.text, filteredResult.isValid);
        
      } catch (error) {
        console.error('轉錄錯誤:', error);
        updateTranscriptionResult(segmentId, `❌ 轉錄失敗: ${error.message}`, false);
      }
    }

    // 智能內容過濾
    function intelligentContentFilter(text, quality) {
      if (!text || text.trim().length === 0) {
        return { text: '🔇 無語音內容', isValid: false };
      }
      
      const cleanText = text.trim();
      
      // 過濾條件
      const filters = [
        { test: t => t.length < 3, reason: '內容過短' },
        { test: t => /^[^\w\u4e00-\u9fff]*$/.test(t), reason: '無有效文字' },
        { test: t => /^(呃|嗯|啊|哦|唔){2,}$/.test(t), reason: '僅含語助詞' },
        { test: t => t.split('').every(c => c === t[0]), reason: '重複字符' },
        { test: t => quality.score < 0.3, reason: '音頻質量過低' }
      ];
      
      for (const filter of filters) {
        if (enableFiltering && filter.test(cleanText)) {
          stats.filteredSegments++;
          return { text: `🚫 已過濾：${filter.reason}`, isValid: false };
        }
      }
      
      stats.validSegments++;
      return { text: cleanText, isValid: true };
    }

    // 更新轉錄結果
    function updateTranscriptionResult(segmentId, transcription, isValid) {
      const item = document.getElementById(`segment-${segmentId}`);
      if (item) {
        const textContent = item.querySelector('.text-content');
        textContent.textContent = transcription;
        item.classList.remove('processing');
        
        if (!isValid) {
          item.classList.add('filtered');
          item.style.display = showFiltered ? 'block' : 'none';
        }
      }
    }

    // 顯示/隱藏已過濾項目
    function showFilteredItems() {
      showFiltered = !showFiltered;
      const filteredItems = document.querySelectorAll('.transcription-item.filtered');
      const btn = document.getElementById('showFilteredBtn');
      
      filteredItems.forEach(item => {
        item.style.display = showFiltered ? 'block' : 'none';
      });
      
      btn.textContent = showFiltered ? '👁️ 隱藏已過濾' : '👁️ 顯示已過濾';
    }

    // 清空轉錄
    function clearTranscriptions() {
      document.getElementById('transcriptionDisplay').innerHTML = '';
      stats = { totalSegments: 0, validSegments: 0, filteredSegments: 0, avgConfidence: 0 };
      currentSegmentId = 0;
    }

    // 匯出轉錄
    function exportTranscriptions() {
      const display = document.getElementById('transcriptionDisplay');
      const validItems = display.querySelectorAll('.transcription-item:not(.filtered):not(.processing)');

      let exportText = '自適應智能轉錄記錄\n================\n\n';

      validItems.forEach(item => {
        const timestamp = item.querySelector('.timestamp').textContent;
        const textContent = item.querySelector('.text-content').textContent;
        if (!textContent.includes('轉錄失敗') && !textContent.includes('已過濾')) {
          exportText += `${timestamp}\n${textContent}\n\n`;
        }
      });

      const blob = new Blob([exportText], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `adaptive_transcription_${new Date().toISOString().slice(0, 19)}.txt`;
      a.click();
      URL.revokeObjectURL(url);
    }

    // 複製有效內容
    function copyValidText() {
      const display = document.getElementById('transcriptionDisplay');
      const validItems = display.querySelectorAll('.transcription-item:not(.filtered):not(.processing)');

      let copyText = '';
      validItems.forEach(item => {
        const textContent = item.querySelector('.text-content').textContent;
        if (!textContent.includes('轉錄失敗') && !textContent.includes('已過濾') && 
            !textContent.includes('轉錄中')) {
          copyText += textContent + '\n';
        }
      });

      navigator.clipboard.writeText(copyText).then(() => {
        alert(`已複製 ${copyText.split('\n').filter(line => line.trim()).length} 條有效轉錄內容`);
      });
    }
  </script>


</body></html>