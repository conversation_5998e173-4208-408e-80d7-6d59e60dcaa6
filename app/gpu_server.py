#!/usr/bin/env python3
"""
GPU 加速的语音转文字服务器
"""

import logging
import io
import numpy as np
import soundfile as sf
from flask import Flask, request, jsonify
from flask_cors import CORS
from faster_whisper import WhisperModel

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 创建 Flask 应用
app = Flask(__name__)
CORS(app)

# 全局变量
whisper_model = None

def initialize_whisper_model():
    """初始化 Whisper 模型"""
    global whisper_model
    
    device = "cuda"
    compute_type = "float16"
    
    print("🔍 检查 GPU 可用性...")
    
    # 检查 GPU 可用性
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            print(f"✅ 检测到 CUDA，GPU 数量: {gpu_count}")
            print(f"🎮 GPU 名称: {gpu_name}")
            print(f"💾 GPU 内存: {gpu_memory:.1f} GB")
        else:
            print("⚠️ CUDA 不可用，回退到 CPU")
            device = "cpu"
            compute_type = "int8"
    except ImportError:
        print("⚠️ PyTorch 未安装，回退到 CPU")
        device = "cpu"
        compute_type = "int8"
    
    try:
        print(f"🔄 正在加载 Whisper 模型: base (设备: {device}, 计算类型: {compute_type})")
        whisper_model = WhisperModel("base", device=device, compute_type=compute_type)
        print(f"✅ Whisper 模型加载成功 (设备: {device})")
        return True
    except Exception as e:
        print(f"❌ 加载 Whisper 模型失败: {e}")
        # 如果 GPU 失败，尝试 CPU
        if device == "cuda":
            print("🔄 尝试使用 CPU 加载模型...")
            try:
                whisper_model = WhisperModel("base", device="cpu", compute_type="int8")
                print("✅ Whisper 模型加载成功 (CPU 回退)")
                return True
            except Exception as cpu_e:
                print(f"❌ CPU 回退也失败: {cpu_e}")
        return False

def evaluate_transcription_quality(text: str, info) -> float:
    """评估转录质量"""
    if not text or len(text.strip()) == 0:
        return 0.0

    score = 0.0

    # 语言匹配分数
    if info.language in ['zh', 'zh-cn', 'zh-tw']:
        score += 0.5
    elif info.language_probability > 0.8:
        score += 0.2

    # 重复检测 - 惩罚过多重复
    words = text.strip().split()
    if len(words) > 0:
        unique_words = len(set(words))
        repetition_ratio = unique_words / len(words)
        score += repetition_ratio * 0.3

    # 中文字符检测
    chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
    if chinese_chars > 0:
        score += 0.3

    # 长度合理性
    if 5 <= len(text.strip()) <= 1000:
        score += 0.2

    return min(score, 1.0)

def transcribe_audio(audio_data: bytes, language: str = "zh") -> dict:
    """使用 Faster Whisper 进行语音转文字"""
    global whisper_model
    
    if whisper_model is None:
        raise Exception("Whisper 模型未初始化")
    
    try:
        # 将音频数据转换为 numpy 数组
        audio_io = io.BytesIO(audio_data)
        audio_array, sample_rate = sf.read(audio_io, dtype='float32')
        
        logger.info(f"音频信息: 采样率={sample_rate}, 形状={audio_array.shape}, 数据类型={audio_array.dtype}, 时长={len(audio_array)/sample_rate:.2f}秒")
        
        # 确保音频是单声道
        if len(audio_array.shape) > 1:
            audio_array = np.mean(audio_array, axis=1)
        
        # 确保数据类型为 float32 并且在正确范围内
        audio_array = np.array(audio_array, dtype=np.float32)
        
        # 音频预处理：正规化到 [-1, 1] 范围
        max_val = np.max(np.abs(audio_array))
        if max_val > 0:
            audio_array = audio_array / max_val
        
        # 确保没有 NaN 或 inf 值
        audio_array = np.nan_to_num(audio_array, nan=0.0, posinf=1.0, neginf=-1.0)
        
        # 进行转录 - 强制使用中文
        logger.info(f"开始转录，强制语言: {language}")

        # 尝试多种策略
        transcription_attempts = [
            # 策略1: 强制中文，较保守参数
            {
                "language": "zh",
                "beam_size": 1,
                "best_of": 1,
                "temperature": 0.0,
                "condition_on_previous_text": False,
                "vad_filter": False
            },
            # 策略2: 强制中文，稍高参数
            {
                "language": "zh",
                "beam_size": 3,
                "best_of": 3,
                "temperature": 0.1,
                "condition_on_previous_text": False,
                "vad_filter": False
            },
            # 策略3: 自动检测但偏向中文
            {
                "language": None,
                "beam_size": 1,
                "best_of": 1,
                "temperature": 0.0,
                "condition_on_previous_text": False,
                "vad_filter": False,
                "initial_prompt": "以下是中文语音转录："
            }
        ]

        best_result = None
        best_score = -1

        for i, params in enumerate(transcription_attempts):
            try:
                logger.info(f"尝试策略 {i+1}: 语言={params.get('language', 'auto')}")
                segments, info = whisper_model.transcribe(audio_array, **params)

                # 收集结果
                transcription_text = ""
                for segment in segments:
                    transcription_text += segment.text

                # 评估结果质量
                score = evaluate_transcription_quality(transcription_text, info)
                logger.info(f"策略 {i+1} 结果: 语言={info.language}, 概率={info.language_probability:.3f}, 质量分数={score:.3f}")
                logger.info(f"策略 {i+1} 文本预览: {transcription_text[:50]}...")

                if score > best_score:
                    best_score = score
                    best_result = (segments, info, transcription_text)

                # 如果是中文且质量不错，直接使用
                if info.language in ['zh', 'zh-cn', 'zh-tw'] and score > 0.5:
                    logger.info(f"找到满意的中文结果，使用策略 {i+1}")
                    break

            except Exception as e:
                logger.warning(f"策略 {i+1} 失败: {e}")
                continue

        if best_result is None:
            raise Exception("所有转录策略都失败了")

        segments, info, transcription_text = best_result
        
        logger.info(f"最终选择: 语言={info.language}, 概率={info.language_probability:.3f}, 质量分数={best_score:.3f}")

        # 收集转录结果
        segments_list = []
        for segment in segments:
            segments_list.append({
                "start": segment.start,
                "end": segment.end,
                "text": segment.text
            })
        
        logger.info(f"转录完成，结果长度: {len(transcription_text)}")
        logger.info(f"转录结果: {transcription_text[:100]}...")  # 只显示前100个字符

        return {
            "transcription": transcription_text.strip(),
            "language": info.language,
            "language_probability": info.language_probability,
            "quality_score": best_score,
            "segments": segments_list
        }
        
    except Exception as e:
        logger.error(f"转录错误: {e}")
        raise Exception(f"转录失败: {e}")

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查端点"""
    return jsonify({
        "status": "healthy",
        "model_loaded": whisper_model is not None,
        "timestamp": "2025-07-18T00:00:00Z",
        "gpu_enabled": whisper_model.model.device if whisper_model else "unknown"
    })

@app.route('/api/transcribe', methods=['POST'])
def transcribe_file():
    """文件转录端点"""
    try:
        if 'audio_file' not in request.files:
            return jsonify({"error": "没有上传音频文件"}), 400
        
        audio_file = request.files['audio_file']
        if audio_file.filename == '':
            return jsonify({"error": "没有选择文件"}), 400
        
        # 读取音频数据
        audio_data = audio_file.read()
        logger.info(f"收到音频文件: {audio_file.filename}, 大小: {len(audio_data)} bytes")
        
        # 进行转录
        result = transcribe_audio(audio_data)
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"文件转录错误: {e}")
        return jsonify({"error": str(e)}), 500

def main():
    print("🚀 启动 GPU 加速语音转文字服务器...")
    print("📋 配置信息:")
    print("   - 模型: Whisper base")
    print("   - 设备: GPU (CUDA) 优先，CPU 回退")
    print("   - 端口: 9004")
    
    # 初始化模型
    print("\n🔄 初始化 Whisper 模型...")
    if initialize_whisper_model():
        print("✅ 模型初始化成功!")
        print("\n🌐 启动 Flask 服务器...")
        print("   访问地址: http://localhost:9004")
        print("   健康检查: http://localhost:9004/health")
        print("   按 Ctrl+C 停止服务器")
        print("-" * 50)
        
        try:
            app.run(host="0.0.0.0", port=9004, debug=False)
        except KeyboardInterrupt:
            print("\n👋 服务器已停止")
        except Exception as e:
            print(f"\n❌ 服务器错误: {e}")
    else:
        print("❌ 模型初始化失败!")

if __name__ == "__main__":
    main()
