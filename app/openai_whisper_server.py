#!/usr/bin/env python3
"""
OpenAI Whisper GPU FastAPI 服务器
专门使用 OpenAI Whisper 进行语音转文字
"""

import logging
import io
import numpy as np
import soundfile as sf
from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
import whisper  # OpenAI Whisper
import torch
from typing import Optional, Dict, Any, List
from pydantic import BaseModel
import uvicorn
import time
import tempfile
import os
import httpx
import asyncio

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 创建 FastAPI 应用
app = FastAPI(
    title="OpenAI Whisper GPU API",
    description="基于 OpenAI Whisper GPU 的高质量语音转文字服务",
    version="3.0.0"
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加静态文件服务
app.mount("/static", StaticFiles(directory="static"), name="static")

# 全局变量
whisper_models: Dict[str, Any] = {}  # 存储多个模型
current_model_name: str = "base"
device: str = "cpu"

# 响应模型
class HealthResponse(BaseModel):
    status: str
    model_loaded: bool
    timestamp: str
    device: str
    model_name: str
    gpu_info: Optional[str] = None

class TranscriptionRequest(BaseModel):
    transcription_language: Optional[str] = "auto"  # "auto", "zh", "en", "vi"

class TranscriptionResponse(BaseModel):
    transcription: str
    language: str
    detected_language: str
    segments: List[dict]
    processing_time: float
    model_name: str
    device_used: str
    transcription_language: str

class ModelSwitchResponse(BaseModel):
    success: bool
    message: str
    current_model: str
    available_models: List[str]

class TranslationRequest(BaseModel):
    text: str
    target_language: str  # 'english', 'traditional_chinese', 'vietnamese'

class TranslationResponse(BaseModel):
    original_text: str
    translated_text: str
    target_language: str
    processing_time: float

def initialize_whisper_model() -> bool:
    """初始化 OpenAI Whisper 模型 - 预加载 base 模型"""
    global whisper_models, current_model_name, device

    logger.info("🔍 检查 GPU 可用性...")

    # 检查 CUDA 可用性
    if torch.cuda.is_available():
        device = "cuda"
        gpu_count = torch.cuda.device_count()
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        logger.info(f"✅ 检测到 CUDA，GPU 数量: {gpu_count}")
        logger.info(f"🎮 GPU 名称: {gpu_name}")
        logger.info(f"💾 GPU 内存: {gpu_memory:.1f} GB")
    else:
        device = "cpu"
        logger.warning("⚠️ CUDA 不可用，使用 CPU")

    # 预加载 base 模型
    try:
        logger.info(f"🔄 正在预加载 OpenAI Whisper base (设备: {device})...")
        whisper_models["base"] = whisper.load_model("base", device=device)
        current_model_name = "base"
        logger.info(f"✅ OpenAI Whisper base 预加载成功 (设备: {device})")
        return True
    except Exception as e:
        logger.error(f"❌ OpenAI Whisper base 预加载失败: {e}")
        return False

def load_model(model_name: str) -> bool:
    """动态加载指定的模型"""
    global whisper_models, current_model_name, device

    # 如果模型已经加载，直接切换
    if model_name in whisper_models:
        current_model_name = model_name
        logger.info(f"✅ 切换到已加载的模型: {model_name}")
        return True

    # 加载新模型
    try:
        logger.info(f"🔄 正在加载 OpenAI Whisper {model_name} (设备: {device})...")
        whisper_models[model_name] = whisper.load_model(model_name, device=device)
        current_model_name = model_name
        logger.info(f"✅ OpenAI Whisper {model_name} 加载成功 (设备: {device})")
        return True
    except Exception as e:
        logger.error(f"❌ OpenAI Whisper {model_name} 加载失败: {e}")
        return False

def get_current_model():
    """获取当前模型"""
    global whisper_models, current_model_name
    return whisper_models.get(current_model_name)

def transcribe_audio(audio_data: bytes, transcription_language: str = "auto") -> Dict[str, Any]:
    """使用 OpenAI Whisper 进行语音转文字"""
    global whisper_models, current_model_name, device

    current_model = get_current_model()
    if current_model is None:
        raise HTTPException(status_code=500, detail="Whisper 模型未初始化")

    start_time = time.time()

    # 语言映射
    language_map = {
        "auto": None,      # 自动检测
        "chinese": "zh",   # 中文
        "english": "en",   # 英文
        "vietnamese": "vi" # 越南文
    }

    target_language = language_map.get(transcription_language, None)
    logger.info(f"🎯 转录语言设置: {transcription_language} -> {target_language or '自动检测'}")
    
    try:
        # 音频预处理
        audio_io = io.BytesIO(audio_data)
        audio_array, sample_rate = sf.read(audio_io, dtype='float32')
        
        logger.info(f"音频信息: 采样率={sample_rate}, 形状={audio_array.shape}, 时长={len(audio_array)/sample_rate:.2f}秒")
        
        # 确保单声道
        if len(audio_array.shape) > 1:
            audio_array = np.mean(audio_array, axis=1)
        
        # 创建临时文件进行转录
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
            sf.write(temp_file.name, audio_array, sample_rate)
            
            logger.info("🎯 开始 OpenAI Whisper 转录...")

            # 构建转录参数
            transcribe_params = {
                "temperature": 0.0,
                "beam_size": 5,
                "best_of": 5,
                "condition_on_previous_text": False,
                "fp16": device == "cuda"  # GPU 时使用 FP16
            }

            # 如果指定了语言，添加语言参数
            if target_language:
                transcribe_params["language"] = target_language
                logger.info(f"🌐 使用指定语言: {target_language}")
            else:
                logger.info("🔍 使用自动语言检测")

            # 进行转录
            result = current_model.transcribe(temp_file.name, **transcribe_params)
            
            # 清理临时文件
            os.unlink(temp_file.name)
        
        processing_time = time.time() - start_time
        
        # 处理 segments
        segments_list = []
        if "segments" in result:
            for segment in result["segments"]:
                segments_list.append({
                    "start": segment.get("start", 0),
                    "end": segment.get("end", 0),
                    "text": segment.get("text", "")
                })
        
        transcription_text = result["text"].strip()
        detected_language = result.get("language", "unknown")

        # 语言名称映射
        language_names = {
            "zh": "中文",
            "en": "英文",
            "vi": "越南文",
            "auto": "自动检测"
        }

        logger.info(f"转录完成，耗时: {processing_time:.2f}秒")
        logger.info(f"检测语言: {detected_language}")
        logger.info(f"设置语言: {transcription_language}")
        logger.info(f"转录结果: {transcription_text[:100]}...")

        return {
            "transcription": transcription_text,
            "language": language_names.get(detected_language, detected_language),
            "detected_language": detected_language,
            "segments": segments_list,
            "processing_time": processing_time,
            "model_name": f"OpenAI Whisper {current_model_name}",
            "device_used": device,
            "transcription_language": transcription_language
        }
        
    except Exception as e:
        logger.error(f"转录错误: {e}")
        raise HTTPException(status_code=500, detail=f"转录失败: {str(e)}")

@app.post("/api/translate", response_model=TranslationResponse)
async def translate_text(request: TranslationRequest):
    """使用 Ollama Gemma3 模型翻译文本"""
    start_time = time.time()

    try:
        logger.info(f"🌐 开始翻译: {request.text[:50]}... -> {request.target_language}")

        # 准备翻译提示词 (更详细和准确)
        language_prompts = {
            'english': '''You are a professional translator. Translate the following text to natural, fluent English.
Rules:
- Only return the translation, no explanations
- Maintain the original meaning and tone
- Use natural English expressions
- If the text is already in English, return it as is

Text to translate:''',
            'traditional_chinese': '''你是專業翻譯員。將以下文字翻譯成自然流暢的繁體中文。
規則：
- 只返回翻譯結果，不要任何解釋
- 保持原文的意思和語調
- 使用自然的中文表達
- 如果文字已經是繁體中文，直接返回原文

要翻譯的文字：''',
            'vietnamese': '''Bạn là một dịch giả chuyên nghiệp. Hãy dịch văn bản sau sang tiếng Việt tự nhiên và trôi chảy.
Quy tắc:
- Chỉ trả về bản dịch, không giải thích
- Giữ nguyên ý nghĩa và giọng điệu gốc
- Sử dụng cách diễn đạt tiếng Việt tự nhiên
- Nếu văn bản đã là tiếng Việt, trả về nguyên văn

Văn bản cần dịch:'''
        }

        if request.target_language not in language_prompts:
            raise HTTPException(status_code=400, detail=f"不支持的翻译语言: {request.target_language}")

        prompt = f"{language_prompts[request.target_language]}\n\n{request.text}"

        # 调用 Ollama API (支持容器环境)
        ollama_host = os.getenv('OLLAMA_HOST', 'localhost')
        ollama_port = os.getenv('OLLAMA_PORT', '11434')
        ollama_url = f'http://{ollama_host}:{ollama_port}/api/generate'

        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                ollama_url,
                json={
                    'model': 'gemma3:latest',
                    'prompt': prompt,
                    'stream': False,
                    'options': {
                        'temperature': 0.1,      # 降低温度提高准确性
                        'top_p': 0.8,           # 降低top_p提高一致性
                        'top_k': 20,            # 限制候选词数量
                        'repeat_penalty': 1.1,  # 避免重复
                        'num_predict': 200,     # 限制输出长度
                        'num_ctx': 2048         # 增加上下文长度
                    }
                }
            )

            if response.status_code != 200:
                raise HTTPException(status_code=500, detail=f"Ollama API 错误: {response.status_code}")

            data = response.json()
            translated_text = data.get('response', '').strip()

            if not translated_text:
                raise HTTPException(status_code=500, detail="翻译结果为空")

        processing_time = time.time() - start_time
        logger.info(f"✅ 翻译完成，耗时: {processing_time:.2f}秒")

        return TranslationResponse(
            original_text=request.text,
            translated_text=translated_text,
            target_language=request.target_language,
            processing_time=processing_time
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"翻译错误: {e}")
        raise HTTPException(status_code=500, detail=f"翻译失败: {str(e)}")

@app.on_event("startup")
async def startup_event():
    """应用启动时初始化模型"""
    logger.info("🚀 启动 OpenAI Whisper GPU 服务器...")
    logger.info("📋 配置信息:")
    logger.info("   - 框架: FastAPI")
    logger.info("   - 模型: OpenAI Whisper")
    logger.info("   - 设备: GPU (CUDA) 优先，CPU 回退")
    
    if not initialize_whisper_model():
        logger.error("❌ 模型初始化失败!")
        raise Exception("模型初始化失败")
    
    logger.info("✅ 服务器启动完成!")

@app.get("/", response_model=dict)
async def root():
    """根路径"""
    return {
        "message": "OpenAI Whisper GPU API 服务",
        "version": "3.0.0",
        "docs": "/docs",
        "health": "/health",
        "meeting_transcription": "/static/meeting_transcription.html"
    }

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查端点"""
    gpu_info = None
    if torch.cuda.is_available():
        gpu_info = f"{torch.cuda.get_device_name(0)} ({torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB)"

    current_model = get_current_model()
    model_name = f"OpenAI Whisper {current_model_name}" if current_model else "未加载"

    return HealthResponse(
        status="healthy",
        model_loaded=current_model is not None,
        timestamp="2025-07-18T00:00:00Z",
        device=device,
        model_name=model_name,
        gpu_info=gpu_info
    )

@app.get("/models", response_model=dict)
async def get_models():
    """获取可用模型列表"""
    available_models = ["tiny", "base", "small", "medium", "large"]
    loaded_models = list(whisper_models.keys())

    return {
        "available_models": available_models,
        "loaded_models": loaded_models,
        "current_model": current_model_name,
        "device": device
    }

@app.post("/models/{model_name}", response_model=ModelSwitchResponse)
async def switch_model(model_name: str):
    """切换模型"""
    available_models = ["tiny", "base", "small", "medium", "large"]

    if model_name not in available_models:
        raise HTTPException(
            status_code=400,
            detail=f"不支持的模型: {model_name}. 可用模型: {available_models}"
        )

    try:
        success = load_model(model_name)
        if success:
            return ModelSwitchResponse(
                success=True,
                message=f"成功切换到模型: {model_name}",
                current_model=current_model_name,
                available_models=available_models
            )
        else:
            return ModelSwitchResponse(
                success=False,
                message=f"切换到模型 {model_name} 失败",
                current_model=current_model_name,
                available_models=available_models
            )
    except Exception as e:
        logger.error(f"模型切换错误: {e}")
        raise HTTPException(status_code=500, detail=f"模型切换失败: {str(e)}")

@app.post("/api/transcribe", response_model=TranscriptionResponse)
async def transcribe_file(
    audio_file: UploadFile = File(...),
    transcription_language: str = "auto"
):
    """文件转录端点"""
    try:
        # 验证语言参数
        valid_languages = ["auto", "chinese", "english", "vietnamese"]
        if transcription_language not in valid_languages:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的转录语言: {transcription_language}. 支持的语言: {valid_languages}"
            )

        # 验证文件类型
        if not audio_file.content_type or not audio_file.content_type.startswith('audio/'):
            allowed_extensions = ['.wav', '.mp3', '.m4a', '.flac', '.ogg', '.webm']
            if not any(audio_file.filename.lower().endswith(ext) for ext in allowed_extensions):
                raise HTTPException(
                    status_code=400,
                    detail="不支持的文件类型。请上传音频文件 (wav, mp3, m4a, flac, ogg, webm)"
                )

        # 读取音频数据
        audio_data = await audio_file.read()

        if len(audio_data) == 0:
            raise HTTPException(status_code=400, detail="上传的文件为空")

        logger.info(f"收到音频文件: {audio_file.filename}, 大小: {len(audio_data)} bytes, 转录语言: {transcription_language}")

        # 进行转录
        result = transcribe_audio(audio_data, transcription_language)

        return TranscriptionResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件转录错误: {e}")
        raise HTTPException(status_code=500, detail=f"转录失败: {str(e)}")

@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """HTTP 异常处理器"""
    return JSONResponse(
        status_code=exc.status_code,
        content={"error": exc.detail}
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """通用异常处理器"""
    logger.error(f"未处理的异常: {exc}")
    return JSONResponse(
        status_code=500,
        content={"error": "内部服务器错误", "detail": str(exc)}
    )

def main():
    """启动服务器"""
    print("🚀 正在启动服务器...")
    try:
        uvicorn.run(
            app,  # 直接传递app对象而不是字符串
            host="0.0.0.0",
            port=9004,
            reload=False,
            log_level="info"
        )
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
