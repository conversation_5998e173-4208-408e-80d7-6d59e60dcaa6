#!/usr/bin/env python3
"""
OpenAI Whisper GPU FastAPI 服务器
专门使用 OpenAI Whisper 进行语音转文字
"""

import logging
import io
import numpy as np
import soundfile as sf
from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import whisper  # OpenAI Whisper
import torch
from typing import Optional, Dict, Any, List
from pydantic import BaseModel
import uvicorn
import time
import tempfile
import os

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 创建 FastAPI 应用
app = FastAPI(
    title="OpenAI Whisper GPU API",
    description="基于 OpenAI Whisper GPU 的高质量语音转文字服务",
    version="3.0.0"
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局变量
whisper_model: Optional[Any] = None
device: str = "cpu"

# 响应模型
class HealthResponse(BaseModel):
    status: str
    model_loaded: bool
    timestamp: str
    device: str
    model_name: str
    gpu_info: Optional[str] = None

class TranscriptionResponse(BaseModel):
    transcription: str
    language: str
    segments: List[dict]
    processing_time: float
    model_name: str
    device_used: str

def initialize_whisper_model() -> bool:
    """初始化 OpenAI Whisper 模型"""
    global whisper_model, device
    
    logger.info("🔍 检查 GPU 可用性...")
    
    # 检查 CUDA 可用性
    if torch.cuda.is_available():
        device = "cuda"
        gpu_count = torch.cuda.device_count()
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        logger.info(f"✅ 检测到 CUDA，GPU 数量: {gpu_count}")
        logger.info(f"🎮 GPU 名称: {gpu_name}")
        logger.info(f"💾 GPU 内存: {gpu_memory:.1f} GB")
    else:
        device = "cpu"
        logger.warning("⚠️ CUDA 不可用，使用 CPU")
    
    # 尝试加载不同大小的模型
    models_to_try = ["large", "medium", "base", "small"]
    
    for model_name in models_to_try:
        try:
            logger.info(f"🔄 正在加载 OpenAI Whisper {model_name} (设备: {device})...")
            whisper_model = whisper.load_model(model_name, device=device)
            logger.info(f"✅ OpenAI Whisper {model_name} 加载成功 (设备: {device})")
            return True
        except Exception as e:
            logger.error(f"❌ OpenAI Whisper {model_name} 加载失败: {e}")
            continue
    
    logger.error("❌ 所有模型都加载失败")
    return False

def transcribe_audio(audio_data: bytes) -> Dict[str, Any]:
    """使用 OpenAI Whisper 进行语音转文字"""
    global whisper_model, device
    
    if whisper_model is None:
        raise HTTPException(status_code=500, detail="Whisper 模型未初始化")
    
    start_time = time.time()
    
    try:
        # 音频预处理
        audio_io = io.BytesIO(audio_data)
        audio_array, sample_rate = sf.read(audio_io, dtype='float32')
        
        logger.info(f"音频信息: 采样率={sample_rate}, 形状={audio_array.shape}, 时长={len(audio_array)/sample_rate:.2f}秒")
        
        # 确保单声道
        if len(audio_array.shape) > 1:
            audio_array = np.mean(audio_array, axis=1)
        
        # 创建临时文件进行转录
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
            sf.write(temp_file.name, audio_array, sample_rate)
            
            logger.info("🎯 开始 OpenAI Whisper 转录...")
            
            # 进行转录
            result = whisper_model.transcribe(
                temp_file.name,
                language="zh",  # 强制中文
                temperature=0.0,
                beam_size=5,
                best_of=5,
                condition_on_previous_text=False,
                fp16=device == "cuda"  # GPU 时使用 FP16
            )
            
            # 清理临时文件
            os.unlink(temp_file.name)
        
        processing_time = time.time() - start_time
        
        # 处理 segments
        segments_list = []
        if "segments" in result:
            for segment in result["segments"]:
                segments_list.append({
                    "start": segment.get("start", 0),
                    "end": segment.get("end", 0),
                    "text": segment.get("text", "")
                })
        
        transcription_text = result["text"].strip()
        detected_language = result.get("language", "zh")
        
        logger.info(f"转录完成，耗时: {processing_time:.2f}秒")
        logger.info(f"检测语言: {detected_language}")
        logger.info(f"转录结果: {transcription_text[:100]}...")
        
        return {
            "transcription": transcription_text,
            "language": detected_language,
            "segments": segments_list,
            "processing_time": processing_time,
            "model_name": "OpenAI Whisper",
            "device_used": device
        }
        
    except Exception as e:
        logger.error(f"转录错误: {e}")
        raise HTTPException(status_code=500, detail=f"转录失败: {str(e)}")

@app.on_event("startup")
async def startup_event():
    """应用启动时初始化模型"""
    logger.info("🚀 启动 OpenAI Whisper GPU 服务器...")
    logger.info("📋 配置信息:")
    logger.info("   - 框架: FastAPI")
    logger.info("   - 模型: OpenAI Whisper")
    logger.info("   - 设备: GPU (CUDA) 优先，CPU 回退")
    
    if not initialize_whisper_model():
        logger.error("❌ 模型初始化失败!")
        raise Exception("模型初始化失败")
    
    logger.info("✅ 服务器启动完成!")

@app.get("/", response_model=dict)
async def root():
    """根路径"""
    return {
        "message": "OpenAI Whisper GPU API 服务",
        "version": "3.0.0",
        "docs": "/docs",
        "health": "/health"
    }

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查端点"""
    gpu_info = None
    if torch.cuda.is_available():
        gpu_info = f"{torch.cuda.get_device_name(0)} ({torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB)"
    
    model_name = "OpenAI Whisper"
    if whisper_model is not None:
        # 尝试获取模型信息
        try:
            if hasattr(whisper_model, 'dims'):
                model_name = f"OpenAI Whisper ({whisper_model.dims.n_vocab} vocab)"
        except:
            pass
    
    return HealthResponse(
        status="healthy",
        model_loaded=whisper_model is not None,
        timestamp="2025-07-18T00:00:00Z",
        device=device,
        model_name=model_name,
        gpu_info=gpu_info
    )

@app.post("/api/transcribe", response_model=TranscriptionResponse)
async def transcribe_file(audio_file: UploadFile = File(...)):
    """文件转录端点"""
    try:
        # 验证文件类型
        if not audio_file.content_type or not audio_file.content_type.startswith('audio/'):
            allowed_extensions = ['.wav', '.mp3', '.m4a', '.flac', '.ogg', '.webm']
            if not any(audio_file.filename.lower().endswith(ext) for ext in allowed_extensions):
                raise HTTPException(
                    status_code=400, 
                    detail="不支持的文件类型。请上传音频文件 (wav, mp3, m4a, flac, ogg, webm)"
                )
        
        # 读取音频数据
        audio_data = await audio_file.read()
        
        if len(audio_data) == 0:
            raise HTTPException(status_code=400, detail="上传的文件为空")
        
        logger.info(f"收到音频文件: {audio_file.filename}, 大小: {len(audio_data)} bytes")
        
        # 进行转录
        result = transcribe_audio(audio_data)
        
        return TranscriptionResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件转录错误: {e}")
        raise HTTPException(status_code=500, detail=f"转录失败: {str(e)}")

@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """HTTP 异常处理器"""
    return JSONResponse(
        status_code=exc.status_code,
        content={"error": exc.detail}
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """通用异常处理器"""
    logger.error(f"未处理的异常: {exc}")
    return JSONResponse(
        status_code=500,
        content={"error": "内部服务器错误", "detail": str(exc)}
    )

def main():
    """启动服务器"""
    uvicorn.run(
        "openai_whisper_server:app",
        host="0.0.0.0",
        port=9004,
        reload=False,
        log_level="info"
    )

if __name__ == "__main__":
    main()
