nohup: 忽略輸入
/home/<USER>/桌面/LLM_RAG/stt_project/app/openai_whisper_server.py:277: DeprecationWarning: 
        on_event is deprecated, use lifespan event handlers instead.

        Read more about it in the
        [FastAPI docs for Lifespan Events](https://fastapi.tiangolo.com/advanced/events/).
        
  @app.on_event("startup")
INFO:     Started server process [2611496]
INFO:     Waiting for application startup.
2025-07-18 14:42:45,845 - __main__ - INFO - 🚀 启动 OpenAI Whisper GPU 服务器...
2025-07-18 14:42:45,845 - __main__ - INFO - 📋 配置信息:
2025-07-18 14:42:45,845 - __main__ - INFO -    - 框架: FastAPI
2025-07-18 14:42:45,845 - __main__ - INFO -    - 模型: OpenAI Whisper
2025-07-18 14:42:45,845 - __main__ - INFO -    - 设备: GPU (CUDA) 优先，CPU 回退
2025-07-18 14:42:45,845 - __main__ - INFO - 🔍 检查 GPU 可用性...
2025-07-18 14:42:45,906 - __main__ - INFO - ✅ 检测到 CUDA，GPU 数量: 1
2025-07-18 14:42:45,906 - __main__ - INFO - 🎮 GPU 名称: NVIDIA GeForce RTX 4070 SUPER
2025-07-18 14:42:45,906 - __main__ - INFO - 💾 GPU 内存: 11.6 GB
2025-07-18 14:42:45,906 - __main__ - INFO - 🔄 正在预加载 OpenAI Whisper base (设备: cuda)...
2025-07-18 14:42:47,629 - __main__ - INFO - ✅ OpenAI Whisper base 预加载成功 (设备: cuda)
2025-07-18 14:42:47,630 - __main__ - INFO - ✅ 服务器启动完成!
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:9004 (Press CTRL+C to quit)
INFO:     127.0.0.1:57560 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
2025-07-18 14:42:50,784 - __main__ - INFO - 🌐 开始翻译: 你好世界... -> english
INFO:     127.0.0.1:47492 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
INFO:     127.0.0.1:47506 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
2025-07-18 14:43:10,784 - __main__ - INFO - 收到音频文件: adaptive_2.wav, 大小: 2785324 bytes
2025-07-18 14:43:10,794 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(1392640,), 时长=31.58秒
2025-07-18 14:43:21,119 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:43:31,340 - __main__ - INFO - 转录完成，耗时: 20.56秒
2025-07-18 14:43:31,340 - __main__ - INFO - 检测语言: zh
2025-07-18 14:43:31,340 - __main__ - INFO - 转录结果: Maxwell gathered for a birthday tribute album according to the Wall Street Journal pages from the al...
2025-07-18 14:43:31,343 - __main__ - ERROR - 翻译错误: 
🚀 正在启动服务器...
INFO:     127.0.0.1:57572 - "POST /api/translate HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:35594 - "POST /api/transcribe HTTP/1.1" 200 OK
INFO:     127.0.0.1:35596 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
2025-07-18 14:43:31,349 - __main__ - INFO - 收到音频文件: adaptive_3.wav, 大小: 393260 bytes
2025-07-18 14:43:31,350 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(196608,), 时长=4.46秒
2025-07-18 14:43:31,474 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:43:36,588 - __main__ - INFO - 转录完成，耗时: 5.24秒
2025-07-18 14:43:36,588 - __main__ - INFO - 检测语言: zh
2025-07-18 14:43:36,588 - __main__ - INFO - 转录结果: 团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团团...
INFO:     127.0.0.1:51070 - "POST /api/transcribe HTTP/1.1" 200 OK
INFO:     connection closed
2025-07-18 14:43:36,590 - __main__ - INFO - 🌐 开始翻译: Maxwell gathered for a birthday tribute album acco... -> traditional_chinese
INFO:     127.0.0.1:36684 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
2025-07-18 14:43:36,643 - __main__ - INFO - 收到音频文件: adaptive_4.wav, 大小: 294956 bytes
2025-07-18 14:43:36,644 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(147456,), 时长=3.34秒
2025-07-18 14:43:36,652 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:43:37,010 - __main__ - INFO - 转录完成，耗时: 0.37秒
2025-07-18 14:43:37,010 - __main__ - INFO - 检测语言: zh
2025-07-18 14:43:37,010 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:36682 - "POST /api/transcribe HTTP/1.1" 200 OK
INFO:     connection closed
INFO:     127.0.0.1:36694 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
2025-07-18 14:43:43,472 - __main__ - INFO - 收到音频文件: adaptive_5.wav, 大小: 303148 bytes
2025-07-18 14:43:43,473 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(151552,), 时长=3.44秒
2025-07-18 14:43:43,478 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:43:43,755 - __main__ - INFO - 转录完成，耗时: 0.28秒
2025-07-18 14:43:43,755 - __main__ - INFO - 检测语言: zh
2025-07-18 14:43:43,755 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:38548 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:43:46,233 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-07-18 14:43:46,234 - __main__ - INFO - ✅ 翻译完成，耗时: 9.64秒
INFO:     127.0.0.1:35594 - "POST /api/translate HTTP/1.1" 200 OK
INFO:     127.0.0.1:38550 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
2025-07-18 14:43:51,672 - __main__ - INFO - 收到音频文件: adaptive_6.wav, 大小: 352300 bytes
2025-07-18 14:43:51,673 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(176128,), 时长=3.99秒
2025-07-18 14:43:51,678 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:43:51,925 - __main__ - INFO - 转录完成，耗时: 0.25秒
2025-07-18 14:43:51,926 - __main__ - INFO - 检测语言: zh
2025-07-18 14:43:51,926 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:38552 - "POST /api/transcribe HTTP/1.1" 200 OK
INFO:     127.0.0.1:49514 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
INFO:     127.0.0.1:49526 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
2025-07-18 14:44:00,633 - __main__ - INFO - 收到音频文件: adaptive_7.wav, 大小: 294956 bytes
2025-07-18 14:44:00,635 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(147456,), 时长=3.34秒
2025-07-18 14:44:00,642 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:44:01,063 - __main__ - INFO - 转录完成，耗时: 0.43秒
2025-07-18 14:44:01,064 - __main__ - INFO - 检测语言: zh
2025-07-18 14:44:01,064 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:49530 - "POST /api/transcribe HTTP/1.1" 200 OK
INFO:     127.0.0.1:52400 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
INFO:     127.0.0.1:56138 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
2025-07-18 14:44:15,381 - __main__ - INFO - 收到音频文件: adaptive_8.wav, 大小: 278572 bytes
2025-07-18 14:44:15,382 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(139264,), 时长=3.16秒
2025-07-18 14:44:15,387 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:44:17,443 - __main__ - INFO - 转录完成，耗时: 2.06秒
2025-07-18 14:44:17,443 - __main__ - INFO - 检测语言: zh
2025-07-18 14:44:17,443 - __main__ - INFO - 转录结果: 我我我我我我我我我我我我我我我我我我我我我我我我我我我我我...
INFO:     127.0.0.1:56140 - "POST /api/transcribe HTTP/1.1" 200 OK
INFO:     127.0.0.1:56144 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
INFO:     127.0.0.1:56158 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
2025-07-18 14:44:22,826 - __main__ - INFO - 🌐 开始翻译: 你好... -> english
2025-07-18 14:44:23,103 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-07-18 14:44:23,104 - __main__ - INFO - ✅ 翻译完成，耗时: 0.28秒
INFO:     127.0.0.1:53932 - "POST /api/translate HTTP/1.1" 200 OK
2025-07-18 14:44:23,833 - __main__ - INFO - 收到音频文件: adaptive_9.wav, 大小: 311340 bytes
2025-07-18 14:44:23,834 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(155648,), 时长=3.53秒
2025-07-18 14:44:23,839 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:44:24,196 - __main__ - INFO - 转录完成，耗时: 0.36秒
2025-07-18 14:44:24,197 - __main__ - INFO - 检测语言: zh
2025-07-18 14:44:24,197 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:53938 - "POST /api/transcribe HTTP/1.1" 200 OK
INFO:     127.0.0.1:53940 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
INFO:     127.0.0.1:53942 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
INFO:     127.0.0.1:41908 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
INFO:     127.0.0.1:41916 - "GET /static/adaptive_transcription.html HTTP/1.1" 304 Not Modified
2025-07-18 14:44:33,755 - __main__ - INFO - 收到音频文件: adaptive_10.wav, 大小: 303148 bytes
2025-07-18 14:44:33,756 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(151552,), 时长=3.44秒
2025-07-18 14:44:33,762 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:44:34,060 - __main__ - INFO - 转录完成，耗时: 0.30秒
2025-07-18 14:44:34,060 - __main__ - INFO - 检测语言: zh
2025-07-18 14:44:34,060 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:41916 - "POST /api/transcribe HTTP/1.1" 200 OK
INFO:     127.0.0.1:41944 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
INFO:     127.0.0.1:41960 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
2025-07-18 14:44:42,713 - __main__ - INFO - 收到音频文件: adaptive_11.wav, 大小: 294956 bytes
2025-07-18 14:44:42,715 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(147456,), 时长=3.34秒
2025-07-18 14:44:42,723 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:44:43,089 - __main__ - INFO - 转录完成，耗时: 0.38秒
2025-07-18 14:44:43,089 - __main__ - INFO - 检测语言: zh
2025-07-18 14:44:43,089 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:41930 - "POST /api/transcribe HTTP/1.1" 200 OK
INFO:     127.0.0.1:47674 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
INFO:     127.0.0.1:47686 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
2025-07-18 14:44:51,840 - __main__ - INFO - 收到音频文件: adaptive_12.wav, 大小: 278572 bytes
2025-07-18 14:44:51,842 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(139264,), 时长=3.16秒
2025-07-18 14:44:51,905 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:44:52,444 - __main__ - INFO - 转录完成，耗时: 0.60秒
2025-07-18 14:44:52,444 - __main__ - INFO - 检测语言: zh
2025-07-18 14:44:52,444 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:47694 - "POST /api/transcribe HTTP/1.1" 200 OK
INFO:     127.0.0.1:46870 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
INFO:     127.0.0.1:46878 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
INFO:     127.0.0.1:46892 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
2025-07-18 14:45:04,630 - __main__ - INFO - 收到音频文件: adaptive_13.wav, 大小: 344108 bytes
2025-07-18 14:45:04,632 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(172032,), 时长=3.90秒
2025-07-18 14:45:04,641 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:45:05,027 - __main__ - INFO - 转录完成，耗时: 0.40秒
2025-07-18 14:45:05,027 - __main__ - INFO - 检测语言: zh
2025-07-18 14:45:05,027 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:42492 - "POST /api/transcribe HTTP/1.1" 200 OK
INFO:     127.0.0.1:42504 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
INFO:     127.0.0.1:42518 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
INFO:     127.0.0.1:35074 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
INFO:     127.0.0.1:35080 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
INFO:     127.0.0.1:57518 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
2025-07-18 14:45:35,367 - __main__ - INFO - 收到音频文件: adaptive_14.wav, 大小: 2891820 bytes
2025-07-18 14:45:35,374 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(1445888,), 时长=32.79秒
2025-07-18 14:45:35,401 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:45:43,780 - __main__ - INFO - 转录完成，耗时: 8.41秒
2025-07-18 14:45:43,780 - __main__ - INFO - 检测语言: zh
2025-07-18 14:45:43,780 - __main__ - INFO - 转录结果: 阿爾克, denotes the woman's breasts and the future president's signature is a squiggly, donald, blow he...
INFO:     127.0.0.1:53444 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     127.0.0.1:53432 - "POST /api/transcribe HTTP/1.1" 200 OK
INFO:     connection closed
2025-07-18 14:45:43,792 - __main__ - INFO - 🌐 开始翻译: 阿爾克, denotes the woman's breasts and the future pr... -> traditional_chinese
2025-07-18 14:45:45,639 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-07-18 14:45:45,640 - __main__ - INFO - ✅ 翻译完成，耗时: 1.85秒
INFO:     127.0.0.1:53432 - "POST /api/translate HTTP/1.1" 200 OK
2025-07-18 14:45:46,214 - __main__ - INFO - 收到音频文件: adaptive_1.wav, 大小: 2727980 bytes
2025-07-18 14:45:46,222 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(1363968,), 时长=30.93秒
2025-07-18 14:45:46,252 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:45:49,670 - __main__ - INFO - 转录完成，耗时: 3.46秒
2025-07-18 14:45:49,670 - __main__ - INFO - 检测语言: zh
2025-07-18 14:45:49,670 - __main__ - INFO - 转录结果: ay and may every day be another wonderful secret now the president's denial quoting again from the W...
INFO:     127.0.0.1:53432 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:45:49,683 - __main__ - INFO - 🌐 开始翻译: ay and may every day be another wonderful secret n... -> traditional_chinese
INFO:     127.0.0.1:38642 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
2025-07-18 14:45:51,505 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-07-18 14:45:51,506 - __main__ - INFO - ✅ 翻译完成，耗时: 1.82秒
INFO:     127.0.0.1:53432 - "POST /api/translate HTTP/1.1" 200 OK
INFO:     127.0.0.1:40696 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
2025-07-18 14:45:59,475 - __main__ - INFO - 收到音频文件: adaptive_2.wav, 大小: 1114156 bytes
2025-07-18 14:45:59,478 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(557056,), 时长=12.63秒
2025-07-18 14:45:59,490 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:46:02,876 - __main__ - INFO - 转录完成，耗时: 3.40秒
2025-07-18 14:46:02,876 - __main__ - INFO - 检测语言: zh
2025-07-18 14:46:02,876 - __main__ - INFO - 转录结果: 有提供的提供的提供的提供的提供的提供的提供的提供的提供的提供的提供的提供的提供的提供的提供的提供的提供的提供的提供的提供的提供的提供的提供的提供的提供的提供的提供的提供的提供的提供的提供的提供的提供的...
INFO:     127.0.0.1:40712 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:46:02,885 - __main__ - INFO - 🌐 开始翻译: 有提供的提供的提供的提供的提供的提供的提供的提供的提供的提供的提供的提供的提供的提供的提供的提供的提... -> traditional_chinese
2025-07-18 14:46:03,499 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-07-18 14:46:03,500 - __main__ - INFO - ✅ 翻译完成，耗时: 0.62秒
INFO:     127.0.0.1:40712 - "POST /api/translate HTTP/1.1" 200 OK
INFO:     127.0.0.1:45180 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
2025-07-18 14:46:05,463 - __main__ - INFO - 收到音频文件: adaptive_15.wav, 大小: 2826284 bytes
2025-07-18 14:46:05,469 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(1413120,), 时长=32.04秒
2025-07-18 14:46:05,516 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:46:11,198 - __main__ - INFO - 转录完成，耗时: 5.74秒
2025-07-18 14:46:11,198 - __main__ - INFO - 检测语言: zh
2025-07-18 14:46:11,198 - __main__ - INFO - 转录结果: 說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說說...
INFO:     127.0.0.1:40712 - "POST /api/transcribe HTTP/1.1" 200 OK
INFO:     127.0.0.1:45182 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
INFO:     127.0.0.1:43512 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
INFO:     127.0.0.1:43522 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
2025-07-18 14:46:29,470 - __main__ - INFO - 收到音频文件: adaptive_3.wav, 大小: 2809900 bytes
2025-07-18 14:46:29,476 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(1404928,), 时长=31.86秒
2025-07-18 14:46:29,513 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:46:37,108 - __main__ - INFO - 转录完成，耗时: 7.64秒
2025-07-18 14:46:37,108 - __main__ - INFO - 检测语言: zh
2025-07-18 14:46:37,108 - __main__ - INFO - 转录结果: 我認為這是一回事這是一回事我認為有更多的事情我認為如果他不出現這個事情人們會開始出現人們會開始出現人們會開始出現人們會開始出現人們會開始出現人們會開始出現人們會開始出現人們會開始出現人們會開始出現人們...
INFO:     127.0.0.1:36744 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     127.0.0.1:36742 - "POST /api/transcribe HTTP/1.1" 200 OK
INFO:     connection closed
2025-07-18 14:46:37,118 - __main__ - INFO - 🌐 开始翻译: 我認為這是一回事這是一回事我認為有更多的事情我認為如果他不出現這個事情人們會開始出現人們會開始出現人... -> traditional_chinese
2025-07-18 14:46:37,174 - __main__ - INFO - 收到音频文件: adaptive_16.wav, 大小: 2777132 bytes
2025-07-18 14:46:37,183 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(1388544,), 时长=31.49秒
2025-07-18 14:46:37,216 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:46:43,992 - __main__ - INFO - 转录完成，耗时: 6.82秒
2025-07-18 14:46:43,993 - __main__ - INFO - 检测语言: zh
2025-07-18 14:46:43,993 - __main__ - INFO - 转录结果: 當他不做這件事人們會開始給他做的事人們會開始做的而 none of it looks good for Trump including this story even if he denies it ...
INFO:     127.0.0.1:56026 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:46:44,000 - __main__ - INFO - 🌐 开始翻译: 當他不做這件事人們會開始給他做的事人們會開始做的而 none of it looks good fo... -> traditional_chinese
INFO:     127.0.0.1:54512 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
2025-07-18 14:46:45,775 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-07-18 14:46:45,776 - __main__ - INFO - ✅ 翻译完成，耗时: 1.78秒
INFO:     127.0.0.1:56026 - "POST /api/translate HTTP/1.1" 200 OK
INFO:     127.0.0.1:54522 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
2025-07-18 14:46:54,623 - __main__ - INFO - 收到音频文件: adaptive_4.wav, 大小: 2162732 bytes
2025-07-18 14:46:54,633 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(1081344,), 时长=24.52秒
2025-07-18 14:46:54,672 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:47:13,650 - __main__ - INFO - 转录完成，耗时: 19.03秒
2025-07-18 14:47:13,651 - __main__ - INFO - 检测语言: zh
2025-07-18 14:47:13,651 - __main__ - INFO - 转录结果: 她的肌膀是否可以做的而是可以做的例如她的肌膀她的肌膀是否可以做的例如她的肌膀是否可以做的例如她的肌膀是否可以做的例如她的肌膀是否可以做的例如她的肌膀是否可以做的例如她的肌膀是否可以做的例如她的肌膀是否...
2025-07-18 14:47:13,652 - __main__ - ERROR - 翻译错误: 
INFO:     127.0.0.1:36742 - "POST /api/translate HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:56656 - "POST /api/transcribe HTTP/1.1" 200 OK
INFO:     127.0.0.1:56670 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
2025-07-18 14:47:13,659 - __main__ - INFO - 收到音频文件: adaptive_5.wav, 大小: 581676 bytes
2025-07-18 14:47:13,661 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(290816,), 时长=6.59秒
2025-07-18 14:47:13,669 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:47:14,541 - __main__ - INFO - 转录完成，耗时: 0.88秒
2025-07-18 14:47:14,541 - __main__ - INFO - 检测语言: zh
2025-07-18 14:47:14,541 - __main__ - INFO - 转录结果: 非常地下降到這個訊息然後再次大家會做得更多的更多的...
INFO:     127.0.0.1:56676 - "POST /api/transcribe HTTP/1.1" 200 OK
INFO:     connection closed
2025-07-18 14:47:14,552 - __main__ - INFO - 收到音频文件: adaptive_17.wav, 大小: 2777132 bytes
2025-07-18 14:47:14,559 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(1388544,), 时长=31.49秒
2025-07-18 14:47:14,589 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:47:24,458 - __main__ - INFO - 转录完成，耗时: 9.91秒
2025-07-18 14:47:24,458 - __main__ - INFO - 检测语言: zh
2025-07-18 14:47:24,458 - __main__ - INFO - 转录结果: 我認為他會把鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪�他在那裡,他在那裡,他在那裡,他在那裡,他...
INFO:     127.0.0.1:38794 - "POST /api/transcribe HTTP/1.1" 200 OK
INFO:     127.0.0.1:33942 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
2025-07-18 14:47:24,471 - __main__ - INFO - 收到音频文件: adaptive_1.wav, 大小: 1163308 bytes
2025-07-18 14:47:24,475 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(581632,), 时长=13.19秒
2025-07-18 14:47:24,487 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:47:28,504 - __main__ - INFO - 转录完成，耗时: 4.03秒
2025-07-18 14:47:28,504 - __main__ - INFO - 检测语言: zh
2025-07-18 14:47:28,504 - __main__ - INFO - 转录结果: 他回家了,他回家了他回家了他回家了他回家了他回家了他回家了他回家了他回家了他回家了他回家了他回家了他回家了他回家了他回家了他回家了他回家了他回家了他回家了他回家了他回家了他回家了他回家了他回家了他回家...
2025-07-18 14:47:28,506 - __main__ - INFO - 🌐 开始翻译: 我認為他會把鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪鋪... -> traditional_chinese
INFO:     127.0.0.1:56676 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:47:28,571 - __main__ - INFO - 🌐 开始翻译: 他回家了,他回家了他回家了他回家了他回家了他回家了他回家了他回家了他回家了他回家了他回家了他回家了他... -> traditional_chinese
2025-07-18 14:47:29,378 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-07-18 14:47:29,379 - __main__ - INFO - ✅ 翻译完成，耗时: 0.87秒
INFO:     127.0.0.1:38794 - "POST /api/translate HTTP/1.1" 200 OK
INFO:     127.0.0.1:58944 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
2025-07-18 14:47:33,535 - __main__ - INFO - 收到音频文件: adaptive_18.wav, 大小: 2678828 bytes
2025-07-18 14:47:33,542 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(1339392,), 时长=30.37秒
2025-07-18 14:47:33,579 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:47:40,408 - __main__ - INFO - 转录完成，耗时: 6.87秒
2025-07-18 14:47:40,408 - __main__ - INFO - 检测语言: zh
2025-07-18 14:47:40,408 - __main__ - INFO - 转录结果: 跟埃克斯坦说的最后一段时间对不对?我说了一段时间我说了一段时间然后他们很厉害然后那时候那时候那时候那时候那时候那时候那时候那时候那时候那时候那时候那时候那时候那时候那时候那时候那时候那时候那时候那时候...
INFO:     127.0.0.1:38794 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:47:40,419 - __main__ - INFO - 🌐 开始翻译: 跟埃克斯坦说的最后一段时间对不对?我说了一段时间我说了一段时间然后他们很厉害然后那时候那时候那时候那... -> traditional_chinese
INFO:     127.0.0.1:58946 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
2025-07-18 14:47:42,017 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-07-18 14:47:42,018 - __main__ - INFO - ✅ 翻译完成，耗时: 1.60秒
INFO:     127.0.0.1:38794 - "POST /api/translate HTTP/1.1" 200 OK
INFO:     127.0.0.1:55152 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
INFO:     127.0.0.1:41886 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
2025-07-18 14:47:58,625 - __main__ - ERROR - 翻译错误: 
2025-07-18 14:48:03,711 - __main__ - INFO - 收到音频文件: adaptive_19.wav, 大小: 2801708 bytes
2025-07-18 14:48:03,720 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(1400832,), 时长=31.76秒
2025-07-18 14:48:03,748 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:48:09,127 - __main__ - INFO - 转录完成，耗时: 5.42秒
2025-07-18 14:48:09,127 - __main__ - INFO - 检测语言: zh
2025-07-18 14:48:09,127 - __main__ - INFO - 转录结果: 有些人的感染,不太驚喜,不太驚喜不太驚喜,不太驚喜,不太驚喜不太驚喜,不太驚喜不太驚喜,不太驚喜不太驚喜不太驚喜不太驚喜不太驚喜不太驚喜不太驚喜不太驚喜不太驚喜不太驚喜不太驚喜不太驚喜不太驚喜不太驚喜...
INFO:     127.0.0.1:53730 - "POST /api/transcribe HTTP/1.1" 200 OK
INFO:     127.0.0.1:53740 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
2025-07-18 14:48:09,133 - __main__ - INFO - 收到音频文件: meeting_audio.wav, 大小: 360490 bytes
2025-07-18 14:48:09,135 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(180223,), 时长=4.09秒
2025-07-18 14:48:09,140 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:48:09,728 - __main__ - INFO - 转录完成，耗时: 0.59秒
2025-07-18 14:48:09,728 - __main__ - INFO - 检测语言: zh
2025-07-18 14:48:09,728 - __main__ - INFO - 转录结果: 還有第二個Agent和建議寫出最終的代碼...
INFO:     127.0.0.1:53736 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:48:09,730 - __main__ - INFO - 收到音频文件: meeting_audio.wav, 大小: 385066 bytes
2025-07-18 14:48:09,731 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(192511,), 时长=4.37秒
2025-07-18 14:48:09,736 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:48:10,078 - __main__ - INFO - 转录完成，耗时: 0.35秒
2025-07-18 14:48:10,078 - __main__ - INFO - 检测语言: zh
2025-07-18 14:48:10,078 - __main__ - INFO - 转录结果: 出最重要的代碼...
INFO:     127.0.0.1:53746 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:48:10,080 - __main__ - INFO - 🌐 开始翻译: 有些人的感染,不太驚喜,不太驚喜不太驚喜,不太驚喜,不太驚喜不太驚喜,不太驚喜不太驚喜,不太驚喜不太... -> traditional_chinese
INFO:     connection closed
2025-07-18 14:48:10,135 - __main__ - INFO - 收到音频文件: meeting_audio.wav, 大小: 391210 bytes
2025-07-18 14:48:10,136 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(195583,), 时长=4.43秒
2025-07-18 14:48:10,143 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:48:10,704 - __main__ - INFO - 转录完成，耗时: 0.57秒
2025-07-18 14:48:10,704 - __main__ - INFO - 检测语言: zh
2025-07-18 14:48:10,704 - __main__ - INFO - 转录结果: 它是做的,放在手上这就让我...
INFO:     127.0.0.1:53736 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:48:13,047 - __main__ - INFO - 收到音频文件: meeting_audio.wav, 大小: 392746 bytes
2025-07-18 14:48:13,048 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(196351,), 时长=4.45秒
2025-07-18 14:48:13,056 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:48:14,206 - __main__ - INFO - 转录完成，耗时: 1.16秒
2025-07-18 14:48:14,206 - __main__ - INFO - 检测语言: zh
2025-07-18 14:48:14,206 - __main__ - INFO - 转录结果: 这让我去尼尔巴i Kagawa和在家里...
INFO:     127.0.0.1:53736 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:48:16,125 - __main__ - INFO - 收到音频文件: meeting_audio.wav, 大小: 393130 bytes
2025-07-18 14:48:16,126 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(196543,), 时长=4.46秒
2025-07-18 14:48:16,131 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:48:16,783 - __main__ - INFO - 转录完成，耗时: 0.66秒
2025-07-18 14:48:16,783 - __main__ - INFO - 检测语言: zh
2025-07-18 14:48:16,783 - __main__ - INFO - 转录结果: 在外面 we learn to make udon...
INFO:     127.0.0.1:53736 - "POST /api/transcribe HTTP/1.1" 200 OK
INFO:     127.0.0.1:52392 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
2025-07-18 14:48:19,192 - __main__ - INFO - 收到音频文件: meeting_audio.wav, 大小: 393226 bytes
2025-07-18 14:48:19,193 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(196591,), 时长=4.46秒
2025-07-18 14:48:19,201 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:48:19,760 - __main__ - INFO - 转录完成，耗时: 0.57秒
2025-07-18 14:48:19,760 - __main__ - INFO - 检测语言: zh
2025-07-18 14:48:19,760 - __main__ - INFO - 转录结果: 謝謝謝謝謝謝...
INFO:     127.0.0.1:53736 - "POST /api/transcribe HTTP/1.1" 200 OK
INFO:     127.0.0.1:52406 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
2025-07-18 14:48:22,263 - __main__ - INFO - 收到音频文件: meeting_audio.wav, 大小: 393250 bytes
2025-07-18 14:48:22,264 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(196603,), 时长=4.46秒
2025-07-18 14:48:22,270 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:48:29,839 - __main__ - INFO - 转录完成，耗时: 7.58秒
2025-07-18 14:48:29,840 - __main__ - INFO - 检测语言: zh
2025-07-18 14:48:29,840 - __main__ - INFO - 转录结果: 我们能带到我们的家庭我们能带到我们的家庭能带到我们的家庭能带到我们的家庭能带到我们的家庭能带到我们的家庭能带到我们的家庭能带到我们的家庭能带到我们的家庭能带到我们的家庭能带到我们的家庭能带到我们的家庭...
INFO:     127.0.0.1:53736 - "POST /api/transcribe HTTP/1.1" 200 OK
INFO:     127.0.0.1:32980 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
2025-07-18 14:48:29,846 - __main__ - INFO - 收到音频文件: meeting_audio.wav, 大小: 393256 bytes
2025-07-18 14:48:29,847 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(196606,), 时长=4.46秒
2025-07-18 14:48:29,854 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:48:30,601 - __main__ - INFO - 转录完成，耗时: 0.76秒
2025-07-18 14:48:30,601 - __main__ - INFO - 检测语言: zh
2025-07-18 14:48:30,601 - __main__ - INFO - 转录结果: 我的 next trip to Tokushima took me to...
INFO:     127.0.0.1:32976 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:48:30,602 - __main__ - INFO - 收到音频文件: meeting_audio.wav, 大小: 393258 bytes
2025-07-18 14:48:30,604 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(196607,), 时长=4.46秒
2025-07-18 14:48:30,609 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:48:31,605 - __main__ - INFO - 转录完成，耗时: 1.00秒
2025-07-18 14:48:31,605 - __main__ - INFO - 检测语言: zh
2025-07-18 14:48:31,605 - __main__ - INFO - 转录结果: Kushima took me deep into the mountains to a summarized old home...
INFO:     127.0.0.1:32994 - "POST /api/transcribe HTTP/1.1" 200 OK
INFO:     connection closed
2025-07-18 14:48:33,782 - __main__ - INFO - 收到音频文件: adaptive_20.wav, 大小: 2891820 bytes
2025-07-18 14:48:33,789 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(1445888,), 时长=32.79秒
2025-07-18 14:48:33,815 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:48:44,228 - __main__ - INFO - 转录完成，耗时: 10.45秒
2025-07-18 14:48:44,228 - __main__ - INFO - 检测语言: zh
2025-07-18 14:48:44,228 - __main__ - INFO - 转录结果: 我在家中吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭吃饭...
2025-07-18 14:48:44,236 - __main__ - ERROR - 翻译错误: 
2025-07-18 14:48:44,236 - __main__ - INFO - 收到音频文件: adaptive_21.wav, 大小: 466988 bytes
2025-07-18 14:48:44,239 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(233472,), 时长=5.29秒
2025-07-18 14:48:44,251 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:48:48,282 - __main__ - INFO - 转录完成，耗时: 4.05秒
2025-07-18 14:48:48,282 - __main__ - INFO - 检测语言: zh
2025-07-18 14:48:48,282 - __main__ - INFO - 转录结果: 他曾經和尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼尼...
INFO:     127.0.0.1:53736 - "POST /api/transcribe HTTP/1.1" 200 OK
INFO:     127.0.0.1:32976 - "POST /api/transcribe HTTP/1.1" 200 OK
INFO:     127.0.0.1:43404 - "WebSocket /ws/realtime" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
INFO:     127.0.0.1:48936 - "GET /static/adaptive_transcription.html HTTP/1.1" 304 Not Modified
2025-07-18 14:51:13,300 - __main__ - INFO - 收到音频文件: adaptive_1.wav, 大小: 2408492 bytes
2025-07-18 14:51:13,307 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(1204224,), 时长=27.31秒
2025-07-18 14:51:13,383 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:51:15,874 - __main__ - INFO - 转录完成，耗时: 2.57秒
2025-07-18 14:51:15,874 - __main__ - INFO - 检测语言: zh
2025-07-18 14:51:15,874 - __main__ - INFO - 转录结果: 1. was from Donald Trump2. their friendship, you'll recall, went back decades2. the article details ...
INFO:     127.0.0.1:58174 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:51:15,886 - __main__ - INFO - 🌐 开始翻译: 1. was from Donald Trump2. their friendship, you'l... -> traditional_chinese
2025-07-18 14:51:17,637 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-07-18 14:51:17,638 - __main__ - INFO - ✅ 翻译完成，耗时: 1.75秒
INFO:     127.0.0.1:58174 - "POST /api/translate HTTP/1.1" 200 OK
2025-07-18 14:51:23,374 - __main__ - INFO - 收到音频文件: adaptive_2.wav, 大小: 966700 bytes
2025-07-18 14:51:23,377 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(483328,), 时长=10.96秒
2025-07-18 14:51:23,388 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:51:24,692 - __main__ - INFO - 转录完成，耗时: 1.32秒
2025-07-18 14:51:24,693 - __main__ - INFO - 检测语言: zh
2025-07-18 14:51:24,693 - __main__ - INFO - 转录结果: Here is the passage about then citizen Trump's letter. Quote, this is from the Wall Street Journal. ...
INFO:     127.0.0.1:56070 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:51:24,699 - __main__ - INFO - 🌐 开始翻译: Here is the passage about then citizen Trump's let... -> traditional_chinese
2025-07-18 14:51:25,566 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-07-18 14:51:25,567 - __main__ - INFO - ✅ 翻译完成，耗时: 0.87秒
INFO:     127.0.0.1:56070 - "POST /api/translate HTTP/1.1" 200 OK
2025-07-18 14:51:33,437 - __main__ - INFO - 收到音频文件: adaptive_3.wav, 大小: 958508 bytes
2025-07-18 14:51:33,442 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(479232,), 时长=10.87秒
2025-07-18 14:51:33,453 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:51:35,697 - __main__ - INFO - 转录完成，耗时: 2.26秒
2025-07-18 14:51:35,697 - __main__ - INFO - 检测语言: zh
2025-07-18 14:51:35,697 - __main__ - INFO - 转录结果: 中文字幕製作作曲作曲作曲作曲作曲作曲作曲作作作作作作作作作作作作作作作作作作作作...
INFO:     127.0.0.1:52812 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:51:35,705 - __main__ - INFO - 🌐 开始翻译: 中文字幕製作作曲作曲作曲作曲作曲作曲作曲作作作作作作作作作作作作作作作作作作作作... -> traditional_chinese
2025-07-18 14:51:36,322 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-07-18 14:51:36,323 - __main__ - INFO - ✅ 翻译完成，耗时: 0.62秒
INFO:     127.0.0.1:52812 - "POST /api/translate HTTP/1.1" 200 OK
2025-07-18 14:51:43,487 - __main__ - INFO - 收到音频文件: adaptive_1.wav, 大小: 966700 bytes
2025-07-18 14:51:43,490 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(483328,), 时长=10.96秒
2025-07-18 14:51:43,502 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:51:46,218 - __main__ - INFO - 转录完成，耗时: 2.73秒
2025-07-18 14:51:46,218 - __main__ - INFO - 检测语言: zh
2025-07-18 14:51:46,218 - __main__ - INFO - 转录结果: 在今年的新冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠...
INFO:     127.0.0.1:33336 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:51:46,226 - __main__ - INFO - 🌐 开始翻译: 在今年的新冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠冠... -> traditional_chinese
2025-07-18 14:51:46,616 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-07-18 14:51:46,617 - __main__ - INFO - ✅ 翻译完成，耗时: 0.39秒
INFO:     127.0.0.1:33336 - "POST /api/translate HTTP/1.1" 200 OK
2025-07-18 14:51:53,562 - __main__ - INFO - 收到音频文件: adaptive_2.wav, 大小: 942124 bytes
2025-07-18 14:51:53,566 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(471040,), 时长=10.68秒
2025-07-18 14:51:53,585 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:51:54,839 - __main__ - INFO - 转录完成，耗时: 1.28秒
2025-07-18 14:51:54,839 - __main__ - INFO - 检测语言: zh
2025-07-18 14:51:54,839 - __main__ - INFO - 转录结果: 也會有更多美麗的美麗現在, the president's denial quoting again from the Wall Street Journal in an interview with...
INFO:     127.0.0.1:40904 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:51:54,844 - __main__ - INFO - 🌐 开始翻译: 也會有更多美麗的美麗現在, the president's denial quoting again... -> traditional_chinese
2025-07-18 14:51:55,521 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-07-18 14:51:55,522 - __main__ - INFO - ✅ 翻译完成，耗时: 0.68秒
INFO:     127.0.0.1:40904 - "POST /api/translate HTTP/1.1" 200 OK
2025-07-18 14:52:03,630 - __main__ - INFO - 收到音频文件: adaptive_3.wav, 大小: 966700 bytes
2025-07-18 14:52:03,634 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(483328,), 时长=10.96秒
2025-07-18 14:52:03,648 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:52:05,655 - __main__ - INFO - 转录完成，耗时: 2.02秒
2025-07-18 14:52:05,655 - __main__ - INFO - 检测语言: zh
2025-07-18 14:52:05,655 - __main__ - INFO - 转录结果: 這是不是我這是一輩子是一輩子我從來沒有寫了一篇文章我沒有寫了一篇文章我沒有寫了一篇文章...
INFO:     127.0.0.1:35986 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:52:05,663 - __main__ - INFO - 🌐 开始翻译: 這是不是我這是一輩子是一輩子我從來沒有寫了一篇文章我沒有寫了一篇文章我沒有寫了一篇文章... -> traditional_chinese
2025-07-18 14:52:06,243 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-07-18 14:52:06,244 - __main__ - INFO - ✅ 翻译完成，耗时: 0.58秒
INFO:     127.0.0.1:35986 - "POST /api/translate HTTP/1.1" 200 OK
2025-07-18 14:52:13,716 - __main__ - INFO - 收到音频文件: adaptive_4.wav, 大小: 966700 bytes
2025-07-18 14:52:13,720 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(483328,), 时长=10.96秒
2025-07-18 14:52:13,731 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:52:15,948 - __main__ - INFO - 转录完成，耗时: 2.23秒
2025-07-18 14:52:15,948 - __main__ - INFO - 检测语言: zh
2025-07-18 14:52:15,948 - __main__ - INFO - 转录结果: 我的意思是不是我的話特朗普特特特特特特特特特特特特特特特特特特特特特特特特特特特特特特特特特特特特特特特...
INFO:     127.0.0.1:59876 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:52:15,957 - __main__ - INFO - 🌐 开始翻译: 我的意思是不是我的話特朗普特特特特特特特特特特特特特特特特特特特特特特特特特特特特特特特特特特特特特... -> traditional_chinese
2025-07-18 14:52:16,634 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-07-18 14:52:16,634 - __main__ - INFO - ✅ 翻译完成，耗时: 0.68秒
INFO:     127.0.0.1:59876 - "POST /api/translate HTTP/1.1" 200 OK
2025-07-18 14:52:23,778 - __main__ - INFO - 收到音频文件: adaptive_5.wav, 大小: 966700 bytes
2025-07-18 14:52:23,783 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(483328,), 时长=10.96秒
2025-07-18 14:52:23,802 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:52:24,910 - __main__ - INFO - 转录完成，耗时: 1.13秒
2025-07-18 14:52:24,910 - __main__ - INFO - 检测语言: zh
2025-07-18 14:52:24,910 - __main__ - INFO - 转录结果: 主持人和副副副副副副副副副副副副副副副副副副副...
INFO:     127.0.0.1:48710 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:52:24,917 - __main__ - INFO - 🌐 开始翻译: 主持人和副副副副副副副副副副副副副副副副副副副... -> traditional_chinese
2025-07-18 14:52:25,219 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-07-18 14:52:25,220 - __main__ - INFO - ✅ 翻译完成，耗时: 0.30秒
INFO:     127.0.0.1:48710 - "POST /api/translate HTTP/1.1" 200 OK
2025-07-18 14:52:33,834 - __main__ - INFO - 收到音频文件: adaptive_6.wav, 大小: 925740 bytes
2025-07-18 14:52:33,837 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(462848,), 时长=10.50秒
2025-07-18 14:52:33,854 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:52:36,617 - __main__ - INFO - 转录完成，耗时: 2.78秒
2025-07-18 14:52:36,618 - __main__ - INFO - 检测语言: zh
2025-07-18 14:52:36,618 - __main__ - INFO - 转录结果: 是您的主持人们的主持人们的主持人们的主持人们的主持人们的主持人们的主持人们的主持人们的主持人们的主持人们的主持人们的主持人们的主持人们的主持人们的主持人们的主持人们的主持人们的主持人们的主持人们的主持...
INFO:     127.0.0.1:39682 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:52:36,626 - __main__ - INFO - 🌐 开始翻译: 是您的主持人们的主持人们的主持人们的主持人们的主持人们的主持人们的主持人们的主持人们的主持人们的主持... -> traditional_chinese
2025-07-18 14:52:37,657 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-07-18 14:52:37,658 - __main__ - INFO - ✅ 翻译完成，耗时: 1.03秒
INFO:     127.0.0.1:39682 - "POST /api/translate HTTP/1.1" 200 OK
2025-07-18 14:52:43,907 - __main__ - INFO - 收到音频文件: adaptive_7.wav, 大小: 958508 bytes
2025-07-18 14:52:43,911 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(479232,), 时长=10.87秒
2025-07-18 14:52:43,927 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:52:45,755 - __main__ - INFO - 转录完成，耗时: 1.85秒
2025-07-18 14:52:45,755 - __main__ - INFO - 检测语言: zh
2025-07-18 14:52:45,755 - __main__ - INFO - 转录结果: 这些人会开始给她的判断判断她的判断然后她的判断的判断结果也认识了如果她认识的判断说我们有些人的判断...
INFO:     127.0.0.1:33126 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:52:45,759 - __main__ - INFO - 🌐 开始翻译: 这些人会开始给她的判断判断她的判断然后她的判断的判断结果也认识了如果她认识的判断说我们有些人的判断... -> traditional_chinese
2025-07-18 14:52:46,486 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-07-18 14:52:46,486 - __main__ - INFO - ✅ 翻译完成，耗时: 0.73秒
INFO:     127.0.0.1:33126 - "POST /api/translate HTTP/1.1" 200 OK
2025-07-18 14:52:53,972 - __main__ - INFO - 收到音频文件: adaptive_8.wav, 大小: 958508 bytes
2025-07-18 14:52:53,977 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(479232,), 时长=10.87秒
2025-07-18 14:52:53,993 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:52:58,834 - __main__ - INFO - 转录完成，耗时: 4.86秒
2025-07-18 14:52:58,835 - __main__ - INFO - 检测语言: zh
2025-07-18 14:52:58,835 - __main__ - INFO - 转录结果: 非常有趣的感觉非常有趣的感觉非常有趣的感觉非常有趣的感觉非常有趣的感觉非常有趣的感觉非常有趣的感觉非常有趣的感觉非常有趣的感觉非常有趣的感觉非常有趣的感觉非常有趣的感觉非常有趣的感觉非常有趣的感觉非常...
INFO:     127.0.0.1:34038 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:53:04,052 - __main__ - INFO - 收到音频文件: adaptive_9.wav, 大小: 950316 bytes
2025-07-18 14:53:04,055 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(475136,), 时长=10.77秒
2025-07-18 14:53:04,065 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:53:12,200 - __main__ - INFO - 转录完成，耗时: 8.15秒
2025-07-18 14:53:12,200 - __main__ - INFO - 检测语言: zh
2025-07-18 14:53:12,200 - __main__ - INFO - 转录结果: 有些被捕捉的方式她的性格是最重要的能够为这些人的感觉是非常好我认为他会被抓到他在这儿...
INFO:     127.0.0.1:47508 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:53:12,208 - __main__ - INFO - 🌐 开始翻译: 有些被捕捉的方式她的性格是最重要的能够为这些人的感觉是非常好我认为他会被抓到他在这儿... -> traditional_chinese
2025-07-18 14:53:12,746 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-07-18 14:53:12,747 - __main__ - INFO - ✅ 翻译完成，耗时: 0.54秒
INFO:     127.0.0.1:47508 - "POST /api/translate HTTP/1.1" 200 OK
2025-07-18 14:53:14,126 - __main__ - INFO - 收到音频文件: adaptive_10.wav, 大小: 892972 bytes
2025-07-18 14:53:14,128 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(446464,), 时长=10.12秒
2025-07-18 14:53:14,140 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:53:15,725 - __main__ - INFO - 转录完成，耗时: 1.60秒
2025-07-18 14:53:15,725 - __main__ - INFO - 检测语言: zh
2025-07-18 14:53:15,725 - __main__ - INFO - 转录结果: 他在拍攝?對他就在這兒我認為他在這兒在這兒我認為他在這兒在這兒我認為他在這兒在這兒在這兒在這兒...
INFO:     127.0.0.1:47508 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:53:15,734 - __main__ - INFO - 🌐 开始翻译: 他在拍攝?對他就在這兒我認為他在這兒在這兒我認為他在這兒在這兒我認為他在這兒在這兒在這兒在這兒... -> traditional_chinese
2025-07-18 14:53:16,418 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-07-18 14:53:16,419 - __main__ - INFO - ✅ 翻译完成，耗时: 0.69秒
INFO:     127.0.0.1:47508 - "POST /api/translate HTTP/1.1" 200 OK
2025-07-18 14:53:24,209 - __main__ - INFO - 收到音频文件: adaptive_11.wav, 大小: 958508 bytes
2025-07-18 14:53:24,215 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(479232,), 时长=10.87秒
2025-07-18 14:53:24,303 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:53:28,133 - __main__ - INFO - 转录完成，耗时: 3.92秒
2025-07-18 14:53:28,133 - __main__ - INFO - 检测语言: zh
2025-07-18 14:53:28,133 - __main__ - INFO - 转录结果: 我剛才有提到的有提到的有提到的有提到的有提到的有提到的有提到的有提到的有提到的有提到的有提到的有提到的有提到的有提到的有提到的有提到的有提到的有提到的有提到的有提到的有提到的有提到的有提到的有提到的有...
INFO:     127.0.0.1:47384 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:53:28,138 - __main__ - INFO - 🌐 开始翻译: 我剛才有提到的有提到的有提到的有提到的有提到的有提到的有提到的有提到的有提到的有提到的有提到的有提到... -> traditional_chinese
2025-07-18 14:53:29,085 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-07-18 14:53:29,086 - __main__ - INFO - ✅ 翻译完成，耗时: 0.95秒
INFO:     127.0.0.1:47384 - "POST /api/translate HTTP/1.1" 200 OK
2025-07-18 14:53:34,249 - __main__ - INFO - 收到音频文件: adaptive_12.wav, 大小: 966700 bytes
2025-07-18 14:53:34,252 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(483328,), 时长=10.96秒
2025-07-18 14:53:34,263 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:53:35,617 - __main__ - INFO - 转录完成，耗时: 1.37秒
2025-07-18 14:53:35,618 - __main__ - INFO - 检测语言: zh
2025-07-18 14:53:35,618 - __main__ - INFO - 转录结果: 所有人都會在做更多的事那是一件事這件事是一件事這件事是一件事他有朋友跟 Epstein在他的最愛的遺憾...
INFO:     127.0.0.1:38946 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:53:35,626 - __main__ - INFO - 🌐 开始翻译: 所有人都會在做更多的事那是一件事這件事是一件事這件事是一件事他有朋友跟 Epstein在他的最愛的遺... -> traditional_chinese
2025-07-18 14:53:36,274 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-07-18 14:53:36,275 - __main__ - INFO - ✅ 翻译完成，耗时: 0.65秒
INFO:     127.0.0.1:38946 - "POST /api/translate HTTP/1.1" 200 OK
2025-07-18 14:53:42,370 - __main__ - INFO - 收到音频文件: adaptive_13.wav, 大小: 688172 bytes
2025-07-18 14:53:42,373 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(344064,), 时长=7.80秒
2025-07-18 14:53:42,387 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:53:43,524 - __main__ - INFO - 转录完成，耗时: 1.15秒
2025-07-18 14:53:43,524 - __main__ - INFO - 检测语言: zh
2025-07-18 14:53:43,524 - __main__ - INFO - 转录结果: 所谓的表情过去他回到最初的时刻然后他很快然后那时有几个都是在在哪儿...
INFO:     127.0.0.1:38948 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:53:43,531 - __main__ - INFO - 🌐 开始翻译: 所谓的表情过去他回到最初的时刻然后他很快然后那时有几个都是在在哪儿... -> traditional_chinese
2025-07-18 14:53:44,018 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-07-18 14:53:44,019 - __main__ - INFO - ✅ 翻译完成，耗时: 0.49秒
INFO:     127.0.0.1:38948 - "POST /api/translate HTTP/1.1" 200 OK
2025-07-18 14:53:50,874 - __main__ - INFO - 收到音频文件: adaptive_14.wav, 大小: 196652 bytes
2025-07-18 14:53:50,876 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(98304,), 时长=2.23秒
2025-07-18 14:53:50,882 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:53:51,255 - __main__ - INFO - 转录完成，耗时: 0.38秒
2025-07-18 14:53:51,255 - __main__ - INFO - 检测语言: zh
2025-07-18 14:53:51,255 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:37280 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:54:02,897 - __main__ - INFO - 收到音频文件: adaptive_15.wav, 大小: 122924 bytes
2025-07-18 14:54:02,898 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(61440,), 时长=1.39秒
2025-07-18 14:54:02,902 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:54:03,172 - __main__ - INFO - 转录完成，耗时: 0.27秒
2025-07-18 14:54:03,172 - __main__ - INFO - 检测语言: zh
2025-07-18 14:54:03,172 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:51354 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:54:11,447 - __main__ - INFO - 收到音频文件: adaptive_16.wav, 大小: 213036 bytes
2025-07-18 14:54:11,448 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(106496,), 时长=2.41秒
2025-07-18 14:54:11,455 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:54:11,804 - __main__ - INFO - 转录完成，耗时: 0.36秒
2025-07-18 14:54:11,804 - __main__ - INFO - 检测语言: zh
2025-07-18 14:54:11,804 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:51368 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:54:22,528 - __main__ - INFO - 收到音频文件: adaptive_17.wav, 大小: 221228 bytes
2025-07-18 14:54:22,529 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(110592,), 时长=2.51秒
2025-07-18 14:54:22,533 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:54:22,774 - __main__ - INFO - 转录完成，耗时: 0.25秒
2025-07-18 14:54:22,774 - __main__ - INFO - 检测语言: zh
2025-07-18 14:54:22,774 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:59350 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:54:34,307 - __main__ - INFO - 收到音频文件: adaptive_18.wav, 大小: 139308 bytes
2025-07-18 14:54:34,308 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(69632,), 时长=1.58秒
2025-07-18 14:54:34,311 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:54:34,793 - __main__ - INFO - 转录完成，耗时: 0.49秒
2025-07-18 14:54:34,793 - __main__ - INFO - 检测语言: zh
2025-07-18 14:54:34,793 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:44460 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:54:37,542 - __main__ - INFO - 收到音频文件: adaptive_19.wav, 大小: 180268 bytes
2025-07-18 14:54:37,544 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(90112,), 时长=2.04秒
2025-07-18 14:54:37,549 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:54:38,068 - __main__ - INFO - 转录完成，耗时: 0.53秒
2025-07-18 14:54:38,068 - __main__ - INFO - 检测语言: zh
2025-07-18 14:54:38,068 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:44460 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:54:48,314 - __main__ - INFO - 收到音频文件: adaptive_20.wav, 大小: 221228 bytes
2025-07-18 14:54:48,317 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(110592,), 时长=2.51秒
2025-07-18 14:54:48,326 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:54:48,643 - __main__ - INFO - 转录完成，耗时: 0.33秒
2025-07-18 14:54:48,643 - __main__ - INFO - 检测语言: zh
2025-07-18 14:54:48,643 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:50898 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:55:03,325 - __main__ - INFO - 收到音频文件: adaptive_21.wav, 大小: 180268 bytes
2025-07-18 14:55:03,326 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(90112,), 时长=2.04秒
2025-07-18 14:55:03,330 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:55:03,825 - __main__ - INFO - 转录完成，耗时: 0.50秒
2025-07-18 14:55:03,825 - __main__ - INFO - 检测语言: zh
2025-07-18 14:55:03,825 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:55698 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:55:11,174 - __main__ - INFO - 收到音频文件: adaptive_22.wav, 大小: 253996 bytes
2025-07-18 14:55:11,175 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(126976,), 时长=2.88秒
2025-07-18 14:55:11,180 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:55:11,688 - __main__ - INFO - 转录完成，耗时: 0.51秒
2025-07-18 14:55:11,688 - __main__ - INFO - 检测语言: zh
2025-07-18 14:55:11,688 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:55712 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:55:23,126 - __main__ - INFO - 收到音频文件: adaptive_23.wav, 大小: 221228 bytes
2025-07-18 14:55:23,127 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(110592,), 时长=2.51秒
2025-07-18 14:55:23,130 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:55:23,458 - __main__ - INFO - 转录完成，耗时: 0.33秒
2025-07-18 14:55:23,458 - __main__ - INFO - 检测语言: zh
2025-07-18 14:55:23,458 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:33008 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:55:32,344 - __main__ - INFO - 收到音频文件: adaptive_24.wav, 大小: 221228 bytes
2025-07-18 14:55:32,344 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(110592,), 时长=2.51秒
2025-07-18 14:55:32,352 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:55:34,926 - __main__ - INFO - 转录完成，耗时: 2.58秒
2025-07-18 14:55:34,927 - __main__ - INFO - 检测语言: zh
2025-07-18 14:55:34,927 - __main__ - INFO - 转录结果: 这些人是这些人是这些人是这些人是这些人是这些人是这些人是这些人是这些人是这些人是这些人是这些人是这些人是这些人是这些人...
INFO:     127.0.0.1:33016 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:55:41,473 - __main__ - INFO - 收到音频文件: adaptive_25.wav, 大小: 245804 bytes
2025-07-18 14:55:41,474 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(122880,), 时长=2.79秒
2025-07-18 14:55:41,478 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:55:41,795 - __main__ - INFO - 转录完成，耗时: 0.32秒
2025-07-18 14:55:41,795 - __main__ - INFO - 检测语言: zh
2025-07-18 14:55:41,796 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:51756 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:55:50,855 - __main__ - INFO - 收到音频文件: adaptive_26.wav, 大小: 319532 bytes
2025-07-18 14:55:50,856 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(159744,), 时长=3.62秒
2025-07-18 14:55:50,865 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:55:51,092 - __main__ - INFO - 转录完成，耗时: 0.24秒
2025-07-18 14:55:51,092 - __main__ - INFO - 检测语言: zh
2025-07-18 14:55:51,092 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:51370 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:55:59,235 - __main__ - INFO - 收到音频文件: adaptive_27.wav, 大小: 196652 bytes
2025-07-18 14:55:59,236 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(98304,), 时长=2.23秒
2025-07-18 14:55:59,239 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:55:59,468 - __main__ - INFO - 转录完成，耗时: 0.23秒
2025-07-18 14:55:59,468 - __main__ - INFO - 检测语言: zh
2025-07-18 14:55:59,468 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:33656 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:56:05,280 - __main__ - INFO - 收到音频文件: adaptive_28.wav, 大小: 188460 bytes
2025-07-18 14:56:05,280 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(94208,), 时长=2.14秒
2025-07-18 14:56:05,284 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:56:05,707 - __main__ - INFO - 转录完成，耗时: 0.43秒
2025-07-18 14:56:05,707 - __main__ - INFO - 检测语言: zh
2025-07-18 14:56:05,707 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:52350 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:56:13,990 - __main__ - INFO - 收到音频文件: adaptive_29.wav, 大小: 278572 bytes
2025-07-18 14:56:13,991 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(139264,), 时长=3.16秒
2025-07-18 14:56:13,996 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:56:16,465 - __main__ - INFO - 转录完成，耗时: 2.47秒
2025-07-18 14:56:16,465 - __main__ - INFO - 检测语言: zh
2025-07-18 14:56:16,465 - __main__ - INFO - 转录结果: 再次再次再次再次再次再次再次再次再次再次再次再次再次再次再次再次再次再次再次再次再次再次再次再次再次再次再次再次...
INFO:     127.0.0.1:41794 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:56:23,557 - __main__ - INFO - 收到音频文件: adaptive_30.wav, 大小: 180268 bytes
2025-07-18 14:56:23,559 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(90112,), 时长=2.04秒
2025-07-18 14:56:23,567 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:56:24,061 - __main__ - INFO - 转录完成，耗时: 0.50秒
2025-07-18 14:56:24,062 - __main__ - INFO - 检测语言: zh
2025-07-18 14:56:24,062 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:46448 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:56:31,734 - __main__ - INFO - 收到音频文件: adaptive_31.wav, 大小: 245804 bytes
2025-07-18 14:56:31,735 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(122880,), 时长=2.79秒
2025-07-18 14:56:31,738 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:56:32,058 - __main__ - INFO - 转录完成，耗时: 0.32秒
2025-07-18 14:56:32,058 - __main__ - INFO - 检测语言: zh
2025-07-18 14:56:32,059 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:46450 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:56:42,224 - __main__ - INFO - 收到音频文件: adaptive_32.wav, 大小: 237612 bytes
2025-07-18 14:56:42,225 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(118784,), 时长=2.69秒
2025-07-18 14:56:42,229 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:56:42,748 - __main__ - INFO - 转录完成，耗时: 0.52秒
2025-07-18 14:56:42,749 - __main__ - INFO - 检测语言: zh
2025-07-18 14:56:42,749 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:57524 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:56:49,567 - __main__ - INFO - 收到音频文件: adaptive_33.wav, 大小: 196652 bytes
2025-07-18 14:56:49,569 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(98304,), 时长=2.23秒
2025-07-18 14:56:49,593 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:56:49,825 - __main__ - INFO - 转录完成，耗时: 0.26秒
2025-07-18 14:56:49,825 - __main__ - INFO - 检测语言: zh
2025-07-18 14:56:49,825 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:34860 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:56:56,991 - __main__ - INFO - 收到音频文件: adaptive_34.wav, 大小: 188460 bytes
2025-07-18 14:56:56,991 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(94208,), 时长=2.14秒
2025-07-18 14:56:56,995 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:56:57,235 - __main__ - INFO - 转录完成，耗时: 0.24秒
2025-07-18 14:56:57,235 - __main__ - INFO - 检测语言: zh
2025-07-18 14:56:57,235 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:38132 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:57:05,438 - __main__ - INFO - 收到音频文件: adaptive_35.wav, 大小: 196652 bytes
2025-07-18 14:57:05,438 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(98304,), 时长=2.23秒
2025-07-18 14:57:05,443 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:57:05,687 - __main__ - INFO - 转录完成，耗时: 0.25秒
2025-07-18 14:57:05,687 - __main__ - INFO - 检测语言: zh
2025-07-18 14:57:05,687 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:49160 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:57:15,528 - __main__ - INFO - 收到音频文件: adaptive_36.wav, 大小: 196652 bytes
2025-07-18 14:57:15,530 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(98304,), 时长=2.23秒
2025-07-18 14:57:15,537 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:57:15,833 - __main__ - INFO - 转录完成，耗时: 0.30秒
2025-07-18 14:57:15,833 - __main__ - INFO - 检测语言: zh
2025-07-18 14:57:15,833 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:52690 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:57:21,816 - __main__ - INFO - 收到音频文件: adaptive_37.wav, 大小: 180268 bytes
2025-07-18 14:57:21,817 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(90112,), 时长=2.04秒
2025-07-18 14:57:21,821 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:57:22,328 - __main__ - INFO - 转录完成，耗时: 0.51秒
2025-07-18 14:57:22,329 - __main__ - INFO - 检测语言: zh
2025-07-18 14:57:22,329 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:52696 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:57:34,381 - __main__ - INFO - 收到音频文件: adaptive_38.wav, 大小: 180268 bytes
2025-07-18 14:57:34,382 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(90112,), 时长=2.04秒
2025-07-18 14:57:34,386 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:57:34,685 - __main__ - INFO - 转录完成，耗时: 0.30秒
2025-07-18 14:57:34,685 - __main__ - INFO - 检测语言: zh
2025-07-18 14:57:34,685 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:49594 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:57:40,683 - __main__ - INFO - 收到音频文件: adaptive_39.wav, 大小: 188460 bytes
2025-07-18 14:57:40,684 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(94208,), 时长=2.14秒
2025-07-18 14:57:40,687 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:57:41,059 - __main__ - INFO - 转录完成，耗时: 0.38秒
2025-07-18 14:57:41,059 - __main__ - INFO - 检测语言: zh
2025-07-18 14:57:41,060 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:49602 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:57:50,582 - __main__ - INFO - 收到音频文件: adaptive_40.wav, 大小: 196652 bytes
2025-07-18 14:57:50,583 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(98304,), 时长=2.23秒
2025-07-18 14:57:50,587 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:57:50,947 - __main__ - INFO - 转录完成，耗时: 0.37秒
2025-07-18 14:57:50,948 - __main__ - INFO - 检测语言: zh
2025-07-18 14:57:50,948 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:57214 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:57:58,112 - __main__ - INFO - 收到音频文件: adaptive_41.wav, 大小: 344108 bytes
2025-07-18 14:57:58,114 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(172032,), 时长=3.90秒
2025-07-18 14:57:58,121 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:57:58,422 - __main__ - INFO - 转录完成，耗时: 0.31秒
2025-07-18 14:57:58,422 - __main__ - INFO - 检测语言: zh
2025-07-18 14:57:58,422 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:38246 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:58:01,923 - __main__ - INFO - 收到音频文件: adaptive_42.wav, 大小: 180268 bytes
2025-07-18 14:58:01,924 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(90112,), 时长=2.04秒
2025-07-18 14:58:01,928 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:58:02,403 - __main__ - INFO - 转录完成，耗时: 0.48秒
2025-07-18 14:58:02,404 - __main__ - INFO - 检测语言: zh
2025-07-18 14:58:02,404 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:38246 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 14:58:13,893 - __main__ - INFO - 收到音频文件: adaptive_43.wav, 大小: 180268 bytes
2025-07-18 14:58:13,894 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(90112,), 时长=2.04秒
2025-07-18 14:58:13,901 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 14:58:14,234 - __main__ - INFO - 转录完成，耗时: 0.34秒
2025-07-18 14:58:14,234 - __main__ - INFO - 检测语言: zh
2025-07-18 14:58:14,234 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:39704 - "POST /api/transcribe HTTP/1.1" 200 OK
