nohup: 忽略輸入
/home/<USER>/桌面/LLM_RAG/stt_project/app/openai_whisper_server.py:336: DeprecationWarning: 
        on_event is deprecated, use lifespan event handlers instead.

        Read more about it in the
        [FastAPI docs for Lifespan Events](https://fastapi.tiangolo.com/advanced/events/).
        
  @app.on_event("startup")
INFO:     Started server process [3091415]
INFO:     Waiting for application startup.
2025-07-18 15:15:50,363 - __main__ - INFO - 🚀 启动 OpenAI Whisper GPU 服务器...
2025-07-18 15:15:50,363 - __main__ - INFO - 📋 配置信息:
2025-07-18 15:15:50,363 - __main__ - INFO -    - 框架: FastAPI
2025-07-18 15:15:50,363 - __main__ - INFO -    - 模型: OpenAI Whisper
2025-07-18 15:15:50,363 - __main__ - INFO -    - 设备: GPU (CUDA) 优先，CPU 回退
2025-07-18 15:15:50,363 - __main__ - INFO - 🔍 检查 GPU 可用性...
2025-07-18 15:15:50,428 - __main__ - INFO - ✅ 检测到 CUDA，GPU 数量: 1
2025-07-18 15:15:50,428 - __main__ - INFO - 🎮 GPU 名称: NVIDIA GeForce RTX 4070 SUPER
2025-07-18 15:15:50,428 - __main__ - INFO - 💾 GPU 内存: 11.6 GB
2025-07-18 15:15:50,428 - __main__ - INFO - 🔄 正在预加载 OpenAI Whisper base (设备: cuda)...
2025-07-18 15:15:52,090 - __main__ - INFO - ✅ OpenAI Whisper base 预加载成功 (设备: cuda)
2025-07-18 15:15:52,090 - __main__ - INFO - ✅ 服务器启动完成!
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:9004 (Press CTRL+C to quit)
2025-07-18 15:16:02,331 - __main__ - INFO - 🌐 开始翻译: Hello world, this is a test.... -> traditional_chinese
2025-07-18 15:16:06,560 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-07-18 15:16:06,560 - __main__ - INFO - ✅ 翻译完成，耗时: 4.23秒
🚀 正在启动服务器...
INFO:     127.0.0.1:47662 - "POST /api/translate HTTP/1.1" 200 OK
2025-07-18 15:16:12,845 - __main__ - INFO - 收到音频文件: test.wav, 大小: 88244 bytes, 转录语言: auto
2025-07-18 15:16:12,845 - __main__ - INFO - 🎯 转录语言设置: auto -> 自动检测
2025-07-18 15:16:12,846 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(44100,), 时长=1.00秒
2025-07-18 15:16:12,850 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 15:16:12,850 - __main__ - INFO - 🔍 使用自动语言检测
2025-07-18 15:16:13,956 - __main__ - INFO - 转录完成，耗时: 1.11秒
2025-07-18 15:16:13,956 - __main__ - INFO - 检测语言: en
2025-07-18 15:16:13,956 - __main__ - INFO - 设置语言: auto
2025-07-18 15:16:13,956 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:36278 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 15:16:16,088 - __main__ - INFO - 收到音频文件: test.wav, 大小: 88244 bytes, 转录语言: auto
2025-07-18 15:16:16,089 - __main__ - INFO - 🎯 转录语言设置: auto -> 自动检测
2025-07-18 15:16:16,090 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(44100,), 时长=1.00秒
2025-07-18 15:16:16,095 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 15:16:16,095 - __main__ - INFO - 🔍 使用自动语言检测
2025-07-18 15:16:16,469 - __main__ - INFO - 转录完成，耗时: 0.38秒
2025-07-18 15:16:16,469 - __main__ - INFO - 检测语言: en
2025-07-18 15:16:16,469 - __main__ - INFO - 设置语言: auto
2025-07-18 15:16:16,469 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:36278 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 15:16:17,304 - __main__ - INFO - 收到音频文件: test.wav, 大小: 88244 bytes, 转录语言: auto
2025-07-18 15:16:17,304 - __main__ - INFO - 🎯 转录语言设置: auto -> 自动检测
2025-07-18 15:16:17,305 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(44100,), 时长=1.00秒
2025-07-18 15:16:17,307 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 15:16:17,307 - __main__ - INFO - 🔍 使用自动语言检测
2025-07-18 15:16:17,667 - __main__ - INFO - 转录完成，耗时: 0.36秒
2025-07-18 15:16:17,667 - __main__ - INFO - 检测语言: en
2025-07-18 15:16:17,668 - __main__ - INFO - 设置语言: auto
2025-07-18 15:16:17,668 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:36278 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 15:16:36,479 - __main__ - INFO - 收到音频文件: adaptive_16.wav, 大小: 8236 bytes, 转录语言: auto
2025-07-18 15:16:36,479 - __main__ - INFO - 🎯 转录语言设置: auto -> 自动检测
2025-07-18 15:16:36,479 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(4096,), 时长=0.09秒
2025-07-18 15:16:36,481 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 15:16:36,481 - __main__ - INFO - 🔍 使用自动语言检测
2025-07-18 15:16:41,675 - __main__ - INFO - 转录完成，耗时: 5.20秒
2025-07-18 15:16:41,675 - __main__ - INFO - 检测语言: en
2025-07-18 15:16:41,675 - __main__ - INFO - 设置语言: auto
2025-07-18 15:16:41,675 - __main__ - INFO - 转录结果: I'm sorry, I'm sorry, I'm sorry, I'm sorry, I'm sorry, I'm sorry, I'm sorry, I'm sorry, I'm sorry, I...
INFO:     127.0.0.1:39714 - "POST /api/transcribe HTTP/1.1" 200 OK
INFO:     127.0.0.1:39720 - "GET /static/adaptive_transcription.html HTTP/1.1" 200 OK
2025-07-18 15:16:46,588 - __main__ - INFO - 收到音频文件: adaptive_17.wav, 大小: 966700 bytes, 转录语言: auto
2025-07-18 15:16:46,588 - __main__ - INFO - 🎯 转录语言设置: auto -> 自动检测
2025-07-18 15:16:46,590 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(483328,), 时长=10.96秒
2025-07-18 15:16:46,601 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 15:16:46,601 - __main__ - INFO - 🔍 使用自动语言检测
2025-07-18 15:16:48,202 - __main__ - INFO - 转录完成，耗时: 1.61秒
2025-07-18 15:16:48,202 - __main__ - INFO - 检测语言: zh
2025-07-18 15:16:48,202 - __main__ - INFO - 设置语言: auto
2025-07-18 15:16:48,202 - __main__ - INFO - 转录结果: 都沒有張忙我取一個最簡單的例子每個人都過過生日過生日的時候都吃生日蛋糕可是從來沒有一個人吃生日蛋糕之後吃過生日蛋糕裡面...
INFO:     127.0.0.1:39720 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 15:16:48,209 - __main__ - INFO - 🌐 开始翻译: 都沒有張忙我取一個最簡單的例子每個人都過過生日過生日的時候都吃生日蛋糕可是從來沒有一個人吃生日蛋糕之... -> english
2025-07-18 15:16:48,862 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-07-18 15:16:48,863 - __main__ - INFO - ✅ 翻译完成，耗时: 0.65秒
INFO:     127.0.0.1:39720 - "POST /api/translate HTTP/1.1" 200 OK
2025-07-18 15:16:56,660 - __main__ - INFO - 收到音频文件: adaptive_18.wav, 大小: 966700 bytes, 转录语言: auto
2025-07-18 15:16:56,660 - __main__ - INFO - 🎯 转录语言设置: auto -> 自动检测
2025-07-18 15:16:56,663 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(483328,), 时长=10.96秒
2025-07-18 15:16:56,674 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 15:16:56,675 - __main__ - INFO - 🔍 使用自动语言检测
2025-07-18 15:16:58,185 - __main__ - INFO - 转录完成，耗时: 1.52秒
2025-07-18 15:16:58,185 - __main__ - INFO - 检测语言: zh
2025-07-18 15:16:58,185 - __main__ - INFO - 设置语言: auto
2025-07-18 15:16:58,185 - __main__ - INFO - 转录结果: 有一隻蟑螂的所以就告訴我們蟑螂不吃生日蛋糕對你說蟑螂不吃生日蛋糕是因為蟑螂不喜歡...
INFO:     127.0.0.1:49930 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 15:16:58,191 - __main__ - INFO - 🌐 开始翻译: 有一隻蟑螂的所以就告訴我們蟑螂不吃生日蛋糕對你說蟑螂不吃生日蛋糕是因為蟑螂不喜歡... -> english
2025-07-18 15:16:58,873 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-07-18 15:16:58,874 - __main__ - INFO - ✅ 翻译完成，耗时: 0.68秒
INFO:     127.0.0.1:49930 - "POST /api/translate HTTP/1.1" 200 OK
2025-07-18 15:17:06,728 - __main__ - INFO - 收到音频文件: adaptive_19.wav, 大小: 966700 bytes, 转录语言: auto
2025-07-18 15:17:06,728 - __main__ - INFO - 🎯 转录语言设置: auto -> 自动检测
2025-07-18 15:17:06,731 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(483328,), 时长=10.96秒
2025-07-18 15:17:06,745 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 15:17:06,746 - __main__ - INFO - 🔍 使用自动语言检测
2025-07-18 15:17:08,351 - __main__ - INFO - 转录完成，耗时: 1.62秒
2025-07-18 15:17:08,351 - __main__ - INFO - 检测语言: zh
2025-07-18 15:17:08,351 - __main__ - INFO - 设置语言: auto
2025-07-18 15:17:08,351 - __main__ - INFO - 转录结果: 還是更可怕的是因為生日蛋糕裡邊有一些東西讓障狼根本就不會去接近它是就是有一些東西障狼根本不會去接近它障狼本身來講的話...
INFO:     127.0.0.1:44836 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 15:17:08,356 - __main__ - INFO - 🌐 开始翻译: 還是更可怕的是因為生日蛋糕裡邊有一些東西讓障狼根本就不會去接近它是就是有一些東西障狼根本不會去接近它... -> english
2025-07-18 15:17:09,038 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-07-18 15:17:09,038 - __main__ - INFO - ✅ 翻译完成，耗时: 0.68秒
INFO:     127.0.0.1:44836 - "POST /api/translate HTTP/1.1" 200 OK
2025-07-18 15:18:09,880 - __main__ - INFO - 收到音频文件: adaptive_1.wav, 大小: 1450028 bytes, 转录语言: auto
2025-07-18 15:18:09,880 - __main__ - INFO - 🎯 转录语言设置: auto -> 自动检测
2025-07-18 15:18:09,887 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(724992,), 时长=16.44秒
2025-07-18 15:18:10,014 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 15:18:10,014 - __main__ - INFO - 🔍 使用自动语言检测
2025-07-18 15:18:12,250 - __main__ - INFO - 转录完成，耗时: 2.37秒
2025-07-18 15:18:12,250 - __main__ - INFO - 检测语言: zh
2025-07-18 15:18:12,250 - __main__ - INFO - 设置语言: auto
2025-07-18 15:18:12,250 - __main__ - INFO - 转录结果: 不會來但剛剛裡面是有乳化劑也有劍他的劍主要就是泡大粉像我們剛剛過了端午節劍重 張長就不會碰然後呢你疏打餅乾有加劍他也不會碰的OK所以你會看到插燒包張長...
INFO:     127.0.0.1:56018 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 15:18:14,260 - __main__ - INFO - 🌐 开始翻译: 不會來但剛剛裡面是有乳化劑也有劍他的劍主要就是泡大粉像我們剛剛過了端午節劍重 張長就不會碰然後呢你疏... -> english
2025-07-18 15:18:15,262 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-07-18 15:18:15,263 - __main__ - INFO - ✅ 翻译完成，耗时: 1.00秒
INFO:     127.0.0.1:56018 - "POST /api/translate HTTP/1.1" 200 OK
2025-07-18 15:18:24,933 - __main__ - INFO - 收到音频文件: adaptive_2.wav, 大小: 1450028 bytes, 转录语言: auto
2025-07-18 15:18:24,933 - __main__ - INFO - 🎯 转录语言设置: auto -> 自动检测
2025-07-18 15:18:24,937 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(724992,), 时长=16.44秒
2025-07-18 15:18:24,952 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 15:18:24,952 - __main__ - INFO - 🔍 使用自动语言检测
2025-07-18 15:18:27,230 - __main__ - INFO - 转录完成，耗时: 2.30秒
2025-07-18 15:18:27,230 - __main__ - INFO - 检测语言: zh
2025-07-18 15:18:27,230 - __main__ - INFO - 设置语言: auto
2025-07-18 15:18:27,231 - __main__ - INFO - 转录结果: 因為他有泡大粉油條髒籃不碰雙胞胎髒籃不碰因為對他的健康對他的生命有非常大的方案能夠破壞油脂類的物質非常的多大部分的香料都可以破壞油脂所以呢...
INFO:     127.0.0.1:35902 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 15:18:29,239 - __main__ - INFO - 🌐 开始翻译: 因為他有泡大粉油條髒籃不碰雙胞胎髒籃不碰因為對他的健康對他的生命有非常大的方案能夠破壞油脂類的物質非... -> english
2025-07-18 15:18:30,165 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-07-18 15:18:30,166 - __main__ - INFO - ✅ 翻译完成，耗时: 0.93秒
INFO:     127.0.0.1:35902 - "POST /api/translate HTTP/1.1" 200 OK
2025-07-18 15:18:40,064 - __main__ - INFO - 收到音频文件: adaptive_3.wav, 大小: 1450028 bytes, 转录语言: auto
2025-07-18 15:18:40,064 - __main__ - INFO - 🎯 转录语言设置: auto -> 自动检测
2025-07-18 15:18:40,067 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(724992,), 时长=16.44秒
2025-07-18 15:18:40,086 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 15:18:40,086 - __main__ - INFO - 🔍 使用自动语言检测
2025-07-18 15:18:42,183 - __main__ - INFO - 转录完成，耗时: 2.12秒
2025-07-18 15:18:42,184 - __main__ - INFO - 检测语言: zh
2025-07-18 15:18:42,184 - __main__ - INFO - 设置语言: auto
2025-07-18 15:18:42,184 - __main__ - INFO - 转录结果: 加了很多的化学宣料章南大家都不會來好 我們講會破壞章狼的對人體也會有傷害嗎你那個殺章南大概也可以殺掉維生物所以吃的飾量是沒有問題的可是吃了過量的話我們的長道的維生...
INFO:     127.0.0.1:37966 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 15:18:44,193 - __main__ - INFO - 🌐 开始翻译: 加了很多的化学宣料章南大家都不會來好 我們講會破壞章狼的對人體也會有傷害嗎你那個殺章南大概也可以殺掉... -> english
2025-07-18 15:18:45,232 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-07-18 15:18:45,233 - __main__ - INFO - ✅ 翻译完成，耗时: 1.04秒
INFO:     127.0.0.1:37966 - "POST /api/translate HTTP/1.1" 200 OK
2025-07-18 15:18:51,475 - __main__ - INFO - 收到音频文件: adaptive_4.wav, 大小: 1097772 bytes, 转录语言: auto
2025-07-18 15:18:51,475 - __main__ - INFO - 🎯 转录语言设置: auto -> 自动检测
2025-07-18 15:18:51,479 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(548864,), 时长=12.45秒
2025-07-18 15:18:51,491 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 15:18:51,491 - __main__ - INFO - 🔍 使用自动语言检测
2025-07-18 15:18:53,034 - __main__ - INFO - 转录完成，耗时: 1.56秒
2025-07-18 15:18:53,034 - __main__ - INFO - 检测语言: zh
2025-07-18 15:18:53,035 - __main__ - INFO - 设置语言: auto
2025-07-18 15:18:53,035 - __main__ - INFO - 转录结果: 就會少了很多那常到沒有維生物就沒有醫生去嗎所以你那意思是說如果吃比較多的加工食品裡面含有很多添加劑的也許毒性還不至於...
INFO:     127.0.0.1:50336 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 15:18:55,041 - __main__ - INFO - 🌐 开始翻译: 就會少了很多那常到沒有維生物就沒有醫生去嗎所以你那意思是說如果吃比較多的加工食品裡面含有很多添加劑的... -> english
2025-07-18 15:18:55,516 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-07-18 15:18:55,517 - __main__ - INFO - ✅ 翻译完成，耗时: 0.48秒
INFO:     127.0.0.1:50336 - "POST /api/translate HTTP/1.1" 200 OK
2025-07-18 15:19:17,216 - __main__ - INFO - 收到音频文件: adaptive_5.wav, 大小: 8236 bytes, 转录语言: auto
2025-07-18 15:19:17,216 - __main__ - INFO - 🎯 转录语言设置: auto -> 自动检测
2025-07-18 15:19:17,217 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(4096,), 时长=0.09秒
2025-07-18 15:19:17,219 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 15:19:17,219 - __main__ - INFO - 🔍 使用自动语言检测
2025-07-18 15:19:17,630 - __main__ - INFO - 转录完成，耗时: 0.41秒
2025-07-18 15:19:17,630 - __main__ - INFO - 检测语言: en
2025-07-18 15:19:17,630 - __main__ - INFO - 设置语言: auto
2025-07-18 15:19:17,631 - __main__ - INFO - 转录结果: ...
INFO:     127.0.0.1:33648 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 15:19:27,341 - __main__ - INFO - 收到音频文件: adaptive_1.wav, 大小: 966700 bytes, 转录语言: auto
2025-07-18 15:19:27,341 - __main__ - INFO - 🎯 转录语言设置: auto -> 自动检测
2025-07-18 15:19:27,344 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(483328,), 时长=10.96秒
2025-07-18 15:19:27,355 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 15:19:27,355 - __main__ - INFO - 🔍 使用自动语言检测
2025-07-18 15:19:28,648 - __main__ - INFO - 转录完成，耗时: 1.31秒
2025-07-18 15:19:28,648 - __main__ - INFO - 检测语言: zh
2025-07-18 15:19:28,648 - __main__ - INFO - 设置语言: auto
2025-07-18 15:19:28,648 - __main__ - INFO - 转录结果: 並向特別深刻的現象回頭看您覺得台灣的失併安全狀況有進步嗎其實以前的實安狀況是比較少以前的天交物...
INFO:     127.0.0.1:45406 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 15:19:30,658 - __main__ - INFO - 🌐 开始翻译: 並向特別深刻的現象回頭看您覺得台灣的失併安全狀況有進步嗎其實以前的實安狀況是比較少以前的天交物... -> english
2025-07-18 15:19:31,272 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-07-18 15:19:31,273 - __main__ - INFO - ✅ 翻译完成，耗时: 0.61秒
INFO:     127.0.0.1:45406 - "POST /api/translate HTTP/1.1" 200 OK
2025-07-18 15:19:37,501 - __main__ - INFO - 收到音频文件: adaptive_2.wav, 大小: 974892 bytes, 转录语言: auto
2025-07-18 15:19:37,501 - __main__ - INFO - 🎯 转录语言设置: auto -> 自动检测
2025-07-18 15:19:37,504 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(487424,), 时长=11.05秒
2025-07-18 15:19:37,515 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 15:19:37,515 - __main__ - INFO - 🔍 使用自动语言检测
2025-07-18 15:19:38,922 - __main__ - INFO - 转录完成，耗时: 1.42秒
2025-07-18 15:19:38,922 - __main__ - INFO - 检测语言: zh
2025-07-18 15:19:38,922 - __main__ - INFO - 设置语言: auto
2025-07-18 15:19:38,922 - __main__ - INFO - 转录结果: 當房付房都必須要辦理財應登記所以他是受到管制的當房就是這個添加物只有一個付房就是有很多個添加物組成的叫付房...
INFO:     127.0.0.1:55282 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 15:19:40,928 - __main__ - INFO - 🌐 开始翻译: 當房付房都必須要辦理財應登記所以他是受到管制的當房就是這個添加物只有一個付房就是有很多個添加物組成的... -> english
2025-07-18 15:19:41,447 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-07-18 15:19:41,448 - __main__ - INFO - ✅ 翻译完成，耗时: 0.52秒
INFO:     127.0.0.1:55282 - "POST /api/translate HTTP/1.1" 200 OK
2025-07-18 15:19:47,630 - __main__ - INFO - 收到音频文件: adaptive_3.wav, 大小: 974892 bytes, 转录语言: auto
2025-07-18 15:19:47,630 - __main__ - INFO - 🎯 转录语言设置: auto -> 自动检测
2025-07-18 15:19:47,633 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(487424,), 时长=11.05秒
2025-07-18 15:19:47,644 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 15:19:47,644 - __main__ - INFO - 🔍 使用自动语言检测
2025-07-18 15:19:49,264 - __main__ - INFO - 转录完成，耗时: 1.63秒
2025-07-18 15:19:49,264 - __main__ - INFO - 检测语言: zh
2025-07-18 15:19:49,264 - __main__ - INFO - 设置语言: auto
2025-07-18 15:19:49,264 - __main__ - INFO - 转录结果: 可是民國八十九年九月二十八號的時候富方市品天家務他經過公告不需要辦理財驗登記他沒有一句話就講說當方市品天家務都經過財...
INFO:     127.0.0.1:42684 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 15:19:51,275 - __main__ - INFO - 🌐 开始翻译: 可是民國八十九年九月二十八號的時候富方市品天家務他經過公告不需要辦理財驗登記他沒有一句話就講說當方市... -> english
2025-07-18 15:19:52,254 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-07-18 15:19:52,255 - __main__ - INFO - ✅ 翻译完成，耗时: 0.98秒
INFO:     127.0.0.1:42684 - "POST /api/translate HTTP/1.1" 200 OK
2025-07-18 15:19:57,699 - __main__ - INFO - 收到音频文件: adaptive_4.wav, 大小: 966700 bytes, 转录语言: auto
2025-07-18 15:19:57,699 - __main__ - INFO - 🎯 转录语言设置: auto -> 自动检测
2025-07-18 15:19:57,702 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(483328,), 时长=10.96秒
2025-07-18 15:19:57,713 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 15:19:57,713 - __main__ - INFO - 🔍 使用自动语言检测
2025-07-18 15:19:59,107 - __main__ - INFO - 转录完成，耗时: 1.41秒
2025-07-18 15:19:59,107 - __main__ - INFO - 检测语言: zh
2025-07-18 15:19:59,107 - __main__ - INFO - 设置语言: auto
2025-07-18 15:19:59,107 - __main__ - INFO - 转录结果: 因為登記所以他都在安全的範圍之內那副方的食品店家是有當方組成當時的法規規定說副方的添加劑不需要...
INFO:     127.0.0.1:35228 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 15:20:01,113 - __main__ - INFO - 🌐 开始翻译: 因為登記所以他都在安全的範圍之內那副方的食品店家是有當方組成當時的法規規定說副方的添加劑不需要... -> english
2025-07-18 15:20:01,686 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-07-18 15:20:01,687 - __main__ - INFO - ✅ 翻译完成，耗时: 0.57秒
INFO:     127.0.0.1:35228 - "POST /api/translate HTTP/1.1" 200 OK
2025-07-18 15:20:07,768 - __main__ - INFO - 收到音频文件: adaptive_5.wav, 大小: 966700 bytes, 转录语言: auto
2025-07-18 15:20:07,769 - __main__ - INFO - 🎯 转录语言设置: auto -> 自动检测
2025-07-18 15:20:07,776 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(483328,), 时长=10.96秒
2025-07-18 15:20:07,796 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 15:20:07,796 - __main__ - INFO - 🔍 使用自动语言检测
2025-07-18 15:20:09,263 - __main__ - INFO - 转录完成，耗时: 1.49秒
2025-07-18 15:20:09,263 - __main__ - INFO - 检测语言: zh
2025-07-18 15:20:09,263 - __main__ - INFO - 设置语言: auto
2025-07-18 15:20:09,263 - __main__ - INFO - 转录结果: 需要查驗登記是單方的需要查驗登記是可是這個邏輯有問題我常常舉一個例子這個例子不太適合但是這個例子很容易...
INFO:     127.0.0.1:59960 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 15:20:11,269 - __main__ - INFO - 🌐 开始翻译: 需要查驗登記是單方的需要查驗登記是可是這個邏輯有問題我常常舉一個例子這個例子不太適合但是這個例子很容... -> english
2025-07-18 15:20:11,955 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-07-18 15:20:11,956 - __main__ - INFO - ✅ 翻译完成，耗时: 0.69秒
INFO:     127.0.0.1:59960 - "POST /api/translate HTTP/1.1" 200 OK
2025-07-18 15:20:17,833 - __main__ - INFO - 收到音频文件: adaptive_6.wav, 大小: 966700 bytes, 转录语言: auto
2025-07-18 15:20:17,834 - __main__ - INFO - 🎯 转录语言设置: auto -> 自动检测
2025-07-18 15:20:17,839 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(483328,), 时长=10.96秒
2025-07-18 15:20:17,950 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 15:20:17,950 - __main__ - INFO - 🔍 使用自动语言检测
2025-07-18 15:20:19,236 - __main__ - INFO - 转录完成，耗时: 1.40秒
2025-07-18 15:20:19,237 - __main__ - INFO - 检测语言: zh
2025-07-18 15:20:19,237 - __main__ - INFO - 设置语言: auto
2025-07-18 15:20:19,237 - __main__ - INFO - 转录结果: 我教一個女朋友這個女朋友呢我經過安全的檢驗過不會有問題可是我禮拜一教一個禮拜二教一個禮拜三教一個...
INFO:     127.0.0.1:49242 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 15:20:21,244 - __main__ - INFO - 🌐 开始翻译: 我教一個女朋友這個女朋友呢我經過安全的檢驗過不會有問題可是我禮拜一教一個禮拜二教一個禮拜三教一個... -> english
2025-07-18 15:20:21,953 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-07-18 15:20:21,954 - __main__ - INFO - ✅ 翻译完成，耗时: 0.71秒
INFO:     127.0.0.1:49242 - "POST /api/translate HTTP/1.1" 200 OK
2025-07-18 15:20:27,907 - __main__ - INFO - 收到音频文件: adaptive_7.wav, 大小: 966700 bytes, 转录语言: auto
2025-07-18 15:20:27,907 - __main__ - INFO - 🎯 转录语言设置: auto -> 自动检测
2025-07-18 15:20:27,910 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(483328,), 时长=10.96秒
2025-07-18 15:20:27,928 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 15:20:27,928 - __main__ - INFO - 🔍 使用自动语言检测
2025-07-18 15:20:28,928 - __main__ - INFO - 转录完成，耗时: 1.02秒
2025-07-18 15:20:28,928 - __main__ - INFO - 检测语言: zh
2025-07-18 15:20:28,928 - __main__ - INFO - 设置语言: auto
2025-07-18 15:20:28,928 - __main__ - INFO - 转录结果: 一個都接受過安全檢驗過都不會有問題可是七個合在一起的時候他可能就會出現大問題了...
INFO:     127.0.0.1:57142 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 15:20:31,195 - __main__ - INFO - 收到音频文件: adaptive_8.wav, 大小: 204844 bytes, 转录语言: auto
2025-07-18 15:20:31,195 - __main__ - INFO - 🎯 转录语言设置: auto -> 自动检测
2025-07-18 15:20:31,196 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(102400,), 时长=2.32秒
2025-07-18 15:20:31,200 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 15:20:31,200 - __main__ - INFO - 🔍 使用自动语言检测
2025-07-18 15:20:31,708 - __main__ - INFO - 转录完成，耗时: 0.51秒
2025-07-18 15:20:31,709 - __main__ - INFO - 检测语言: zh
2025-07-18 15:20:31,709 - __main__ - INFO - 设置语言: auto
2025-07-18 15:20:31,709 - __main__ - INFO - 转录结果: 比喻还蛮有趣的随您...
INFO:     127.0.0.1:57142 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 15:20:31,710 - __main__ - INFO - 🌐 开始翻译: 一個都接受過安全檢驗過都不會有問題可是七個合在一起的時候他可能就會出現大問題了... -> english
2025-07-18 15:20:32,234 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-07-18 15:20:32,235 - __main__ - INFO - ✅ 翻译完成，耗时: 0.52秒
INFO:     127.0.0.1:57158 - "POST /api/translate HTTP/1.1" 200 OK
2025-07-18 15:20:33,716 - __main__ - INFO - 🌐 开始翻译: 比喻还蛮有趣的随您... -> english
2025-07-18 15:20:34,044 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-07-18 15:20:34,045 - __main__ - INFO - ✅ 翻译完成，耗时: 0.33秒
INFO:     127.0.0.1:57158 - "POST /api/translate HTTP/1.1" 200 OK
INFO:     127.0.0.1:49876 - "GET /static/meeting_transcription.html HTTP/1.1" 304 Not Modified
INFO:     127.0.0.1:42626 - "GET /static/meeting_transcription.html HTTP/1.1" 304 Not Modified
INFO:     127.0.0.1:42628 - "GET /static/adjustable_transcription.html HTTP/1.1" 304 Not Modified
2025-07-18 15:24:02,223 - __main__ - INFO - 收到音频文件: adjustable_1.wav, 大小: 3317804 bytes, 转录语言: auto
2025-07-18 15:24:02,223 - __main__ - INFO - 🎯 转录语言设置: auto -> 自动检测
2025-07-18 15:24:02,232 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(1658880,), 时长=37.62秒
2025-07-18 15:24:02,263 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 15:24:02,264 - __main__ - INFO - 🔍 使用自动语言检测
2025-07-18 15:24:06,473 - __main__ - INFO - 转录完成，耗时: 4.25秒
2025-07-18 15:24:06,474 - __main__ - INFO - 检测语言: zh
2025-07-18 15:24:06,474 - __main__ - INFO - 设置语言: auto
2025-07-18 15:24:06,474 - __main__ - INFO - 转录结果: 什麼樣印象特別深刻的現象回頭看您覺得台灣的食品安全狀況有進步嗎其實以前的實安狀況是比較少以前的添加物當房付房都必須要辦理財應登記所以它是受到管制的當房就是這個添加物只有一個付房就是有很多個添加物組成的...
INFO:     127.0.0.1:40642 - "POST /api/transcribe HTTP/1.1" 200 OK
INFO:     127.0.0.1:50690 - "GET /static/234.html HTTP/1.1" 200 OK
2025-07-18 15:25:21,843 - __main__ - INFO - 收到音频文件: adaptive_1.wav, 大小: 401452 bytes, 转录语言: auto
2025-07-18 15:25:21,844 - __main__ - INFO - 🎯 转录语言设置: auto -> 自动检测
2025-07-18 15:25:21,846 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(200704,), 时长=4.55秒
2025-07-18 15:25:21,854 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 15:25:21,854 - __main__ - INFO - 🔍 使用自动语言检测
2025-07-18 15:25:22,526 - __main__ - INFO - 转录完成，耗时: 0.68秒
2025-07-18 15:25:22,527 - __main__ - INFO - 检测语言: zh
2025-07-18 15:25:22,527 - __main__ - INFO - 设置语言: auto
2025-07-18 15:25:22,527 - __main__ - INFO - 转录结果: 可以這樣說那我們來問下一個題目好了也是大家很在意的就是...
INFO:     127.0.0.1:50706 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 15:25:26,949 - __main__ - INFO - 收到音频文件: adaptive_2.wav, 大小: 491564 bytes, 转录语言: auto
2025-07-18 15:25:26,949 - __main__ - INFO - 🎯 转录语言设置: auto -> 自动检测
2025-07-18 15:25:26,951 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(245760,), 时长=5.57秒
2025-07-18 15:25:26,958 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 15:25:26,958 - __main__ - INFO - 🔍 使用自动语言检测
2025-07-18 15:25:27,709 - __main__ - INFO - 转录完成，耗时: 0.76秒
2025-07-18 15:25:27,709 - __main__ - INFO - 检测语言: zh
2025-07-18 15:25:27,709 - __main__ - INFO - 设置语言: auto
2025-07-18 15:25:27,709 - __main__ - INFO - 转录结果: 反食之防对政府禁止之后现在在天家屋里面都写灵...
INFO:     127.0.0.1:50706 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 15:25:30,186 - __main__ - INFO - 收到音频文件: adaptive_3.wav, 大小: 311340 bytes, 转录语言: auto
2025-07-18 15:25:30,186 - __main__ - INFO - 🎯 转录语言设置: auto -> 自动检测
2025-07-18 15:25:30,187 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(155648,), 时长=3.53秒
2025-07-18 15:25:30,191 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 15:25:30,191 - __main__ - INFO - 🔍 使用自动语言检测
2025-07-18 15:25:30,728 - __main__ - INFO - 转录完成，耗时: 0.54秒
2025-07-18 15:25:30,728 - __main__ - INFO - 检测语言: zh
2025-07-18 15:25:30,728 - __main__ - INFO - 设置语言: auto
2025-07-18 15:25:30,728 - __main__ - INFO - 转录结果: 我其實聽到很多傳聞說其實只是換個名字...
INFO:     127.0.0.1:50706 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 15:25:36,428 - __main__ - INFO - 收到音频文件: adaptive_4.wav, 大小: 598060 bytes, 转录语言: auto
2025-07-18 15:25:36,428 - __main__ - INFO - 🎯 转录语言设置: auto -> 自动检测
2025-07-18 15:25:36,430 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(299008,), 时长=6.78秒
2025-07-18 15:25:36,437 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 15:25:36,437 - __main__ - INFO - 🔍 使用自动语言检测
2025-07-18 15:25:37,118 - __main__ - INFO - 转录完成，耗时: 0.69秒
2025-07-18 15:25:37,118 - __main__ - INFO - 检测语言: zh
2025-07-18 15:25:37,118 - __main__ - INFO - 设置语言: auto
2025-07-18 15:25:37,118 - __main__ - INFO - 转录结果: 事情到底如何一百年七年七月一號開始公告所有的實用油...
INFO:     127.0.0.1:38628 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 15:25:42,137 - __main__ - INFO - 收到音频文件: adaptive_1.wav, 大小: 548908 bytes, 转录语言: auto
2025-07-18 15:25:42,137 - __main__ - INFO - 🎯 转录语言设置: auto -> 自动检测
2025-07-18 15:25:42,138 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(274432,), 时长=6.22秒
2025-07-18 15:25:42,146 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 15:25:42,146 - __main__ - INFO - 🔍 使用自动语言检测
2025-07-18 15:25:43,030 - __main__ - INFO - 转录完成，耗时: 0.89秒
2025-07-18 15:25:43,030 - __main__ - INFO - 检测语言: zh
2025-07-18 15:25:43,030 - __main__ - INFO - 设置语言: auto
2025-07-18 15:25:43,030 - __main__ - INFO - 转录结果: 都不可以有反试释放油它叫做三酸干油脂三酸干油脂...
INFO:     127.0.0.1:38642 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 15:25:47,602 - __main__ - INFO - 收到音频文件: adaptive_2.wav, 大小: 524332 bytes, 转录语言: auto
2025-07-18 15:25:47,602 - __main__ - INFO - 🎯 转录语言设置: auto -> 自动检测
2025-07-18 15:25:47,603 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(262144,), 时长=5.94秒
2025-07-18 15:25:47,610 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 15:25:47,610 - __main__ - INFO - 🔍 使用自动语言检测
2025-07-18 15:25:48,341 - __main__ - INFO - 转录完成，耗时: 0.74秒
2025-07-18 15:25:48,341 - __main__ - INFO - 检测语言: zh
2025-07-18 15:25:48,341 - __main__ - INFO - 设置语言: auto
2025-07-18 15:25:48,341 - __main__ - INFO - 转录结果: 不可以有反飾釋防可是他並沒有說二酸乾優質不可以...
INFO:     127.0.0.1:38642 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 15:25:56,411 - __main__ - INFO - 收到音频文件: adaptive_3.wav, 大小: 843820 bytes, 转录语言: auto
2025-07-18 15:25:56,411 - __main__ - INFO - 🎯 转录语言设置: auto -> 自动检测
2025-07-18 15:25:56,413 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(421888,), 时长=9.57秒
2025-07-18 15:25:56,425 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 15:25:56,425 - __main__ - INFO - 🔍 使用自动语言检测
2025-07-18 15:25:57,698 - __main__ - INFO - 转录完成，耗时: 1.29秒
2025-07-18 15:25:57,698 - __main__ - INFO - 检测语言: zh
2025-07-18 15:25:57,698 - __main__ - INFO - 设置语言: auto
2025-07-18 15:25:57,698 - __main__ - INFO - 转录结果: 一酸乾油脂不可以所以一酸乾油脂一個二酸乾油脂會被單獨成化出來當作添加我去滿身是...
INFO:     127.0.0.1:41924 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 15:26:01,326 - __main__ - INFO - 收到音频文件: adaptive_4.wav, 大小: 475180 bytes, 转录语言: auto
2025-07-18 15:26:01,327 - __main__ - INFO - 🎯 转录语言设置: auto -> 自动检测
2025-07-18 15:26:01,328 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(237568,), 时长=5.39秒
2025-07-18 15:26:01,333 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 15:26:01,334 - __main__ - INFO - 🔍 使用自动语言检测
2025-07-18 15:26:02,381 - __main__ - INFO - 转录完成，耗时: 1.05秒
2025-07-18 15:26:02,381 - __main__ - INFO - 检测语言: zh
2025-07-18 15:26:02,381 - __main__ - INFO - 设置语言: auto
2025-07-18 15:26:02,381 - __main__ - INFO - 转录结果: 這個溢酸乾燥水二酸乾燥水我們就把一盒冰給名稱叫做吃防酸乾油脂...
INFO:     127.0.0.1:41924 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 15:26:08,069 - __main__ - INFO - 收到音频文件: adaptive_5.wav, 大小: 647212 bytes, 转录语言: auto
2025-07-18 15:26:08,069 - __main__ - INFO - 🎯 转录语言设置: auto -> 自动检测
2025-07-18 15:26:08,071 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(323584,), 时长=7.34秒
2025-07-18 15:26:08,079 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 15:26:08,079 - __main__ - INFO - 🔍 使用自动语言检测
2025-07-18 15:26:08,928 - __main__ - INFO - 转录完成，耗时: 0.86秒
2025-07-18 15:26:08,928 - __main__ - INFO - 检测语言: zh
2025-07-18 15:26:08,928 - __main__ - INFO - 设置语言: auto
2025-07-18 15:26:08,928 - __main__ - INFO - 转录结果: 那我們的食物當中這個東西作為添加物的時候它不會被標示為反思之防雖然...
INFO:     127.0.0.1:47478 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 15:26:16,290 - __main__ - INFO - 收到音频文件: adaptive_6.wav, 大小: 786476 bytes, 转录语言: auto
2025-07-18 15:26:16,290 - __main__ - INFO - 🎯 转录语言设置: auto -> 自动检测
2025-07-18 15:26:16,292 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(393216,), 时长=8.92秒
2025-07-18 15:26:16,304 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 15:26:16,304 - __main__ - INFO - 🔍 使用自动语言检测
2025-07-18 15:26:17,291 - __main__ - INFO - 转录完成，耗时: 1.00秒
2025-07-18 15:26:17,291 - __main__ - INFO - 检测语言: zh
2025-07-18 15:26:17,292 - __main__ - INFO - 设置语言: auto
2025-07-18 15:26:17,292 - __main__ - INFO - 转录结果: 現在公告的是實用油你一定要標示也沒有繁殖是完善可是添加物沒有規定...
INFO:     127.0.0.1:60260 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 15:26:20,008 - __main__ - INFO - 收到音频文件: adaptive_7.wav, 大小: 360492 bytes, 转录语言: auto
2025-07-18 15:26:20,008 - __main__ - INFO - 🎯 转录语言设置: auto -> 自动检测
2025-07-18 15:26:20,009 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(180224,), 时长=4.09秒
2025-07-18 15:26:20,014 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 15:26:20,014 - __main__ - INFO - 🔍 使用自动语言检测
2025-07-18 15:26:20,459 - __main__ - INFO - 转录完成，耗时: 0.45秒
2025-07-18 15:26:20,459 - __main__ - INFO - 检测语言: zh
2025-07-18 15:26:20,459 - __main__ - INFO - 设置语言: auto
2025-07-18 15:26:20,459 - __main__ - INFO - 转录结果: 就让我们的世界变得这个样子...
INFO:     127.0.0.1:60260 - "POST /api/transcribe HTTP/1.1" 200 OK
2025-07-18 15:26:22,824 - __main__ - INFO - 收到音频文件: adaptive_8.wav, 大小: 270380 bytes, 转录语言: auto
2025-07-18 15:26:22,824 - __main__ - INFO - 🎯 转录语言设置: auto -> 自动检测
2025-07-18 15:26:22,826 - __main__ - INFO - 音频信息: 采样率=44100, 形状=(135168,), 时长=3.07秒
2025-07-18 15:26:22,831 - __main__ - INFO - 🎯 开始 OpenAI Whisper 转录...
2025-07-18 15:26:22,831 - __main__ - INFO - 🔍 使用自动语言检测
2025-07-18 15:26:23,165 - __main__ - INFO - 转录完成，耗时: 0.34秒
2025-07-18 15:26:23,166 - __main__ - INFO - 检测语言: zh
2025-07-18 15:26:23,166 - __main__ - INFO - 设置语言: auto
2025-07-18 15:26:23,166 - __main__ - INFO - 转录结果: 天家我沒有過...
INFO:     127.0.0.1:60260 - "POST /api/transcribe HTTP/1.1" 200 OK
