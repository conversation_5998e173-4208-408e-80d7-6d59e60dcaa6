#!/usr/bin/env python3
"""
直接测试 faster-whisper 转录
"""

import soundfile as sf
import numpy as np
from faster_whisper import WhisperModel
import torch

def test_whisper_direct():
    """直接测试 Whisper 转录"""
    
    audio_file = "/home/<USER>/下載/ponsunlun_1.wav"
    
    print("🔍 检查 GPU 状态...")
    if torch.cuda.is_available():
        print(f"✅ CUDA 可用，GPU: {torch.cuda.get_device_name(0)}")
        device = "cuda"
        compute_type = "float16"
    else:
        print("⚠️ 使用 CPU")
        device = "cpu"
        compute_type = "int8"
    
    print(f"\n🔄 加载 Whisper 模型...")
    
    # 测试不同模型
    models_to_test = ["base", "large-v3"]
    
    for model_name in models_to_test:
        print(f"\n{'='*50}")
        print(f"测试模型: {model_name}")
        print(f"{'='*50}")
        
        try:
            # 加载模型
            model = WhisperModel(model_name, device=device, compute_type=compute_type)
            print(f"✅ {model_name} 模型加载成功")
            
            # 读取音频
            print(f"📁 读取音频文件: {audio_file}")
            audio_array, sample_rate = sf.read(audio_file, dtype='float32')
            
            print(f"📊 音频信息:")
            print(f"   采样率: {sample_rate} Hz")
            print(f"   时长: {len(audio_array)/sample_rate:.2f} 秒")
            print(f"   声道: {audio_array.shape}")
            
            # 转为单声道
            if len(audio_array.shape) > 1:
                audio_array = np.mean(audio_array, axis=1)
            
            # 正规化
            audio_array = audio_array.astype(np.float32)
            max_val = np.max(np.abs(audio_array))
            if max_val > 0:
                audio_array = audio_array / max_val
            
            print(f"\n🎯 开始转录...")
            
            # 测试不同参数组合
            test_configs = [
                {
                    "name": "自动检测语言",
                    "params": {
                        "language": None,
                        "beam_size": 5,
                        "best_of": 5,
                        "temperature": 0.0,
                        "condition_on_previous_text": False,
                        "vad_filter": False
                    }
                },
                {
                    "name": "强制中文",
                    "params": {
                        "language": "zh",
                        "beam_size": 5,
                        "best_of": 5,
                        "temperature": 0.0,
                        "condition_on_previous_text": False,
                        "vad_filter": False
                    }
                },
                {
                    "name": "保守参数",
                    "params": {
                        "language": "zh",
                        "beam_size": 1,
                        "best_of": 1,
                        "temperature": 0.0,
                        "condition_on_previous_text": False,
                        "vad_filter": False
                    }
                }
            ]
            
            for config in test_configs:
                print(f"\n--- {config['name']} ---")
                try:
                    segments, info = model.transcribe(audio_array, **config['params'])
                    
                    print(f"检测语言: {info.language} (概率: {info.language_probability:.3f})")
                    
                    # 收集转录结果
                    transcription = ""
                    for segment in segments:
                        transcription += segment.text
                    
                    print(f"转录结果: {transcription}")
                    print(f"结果长度: {len(transcription)} 字符")
                    
                    # 检查是否接近目标文本
                    target_keywords = ["财务", "体质", "调整", "健康", "结构"]
                    found_keywords = [kw for kw in target_keywords if kw in transcription]
                    
                    print(f"匹配关键词: {found_keywords}")
                    
                    if len(found_keywords) >= 2:
                        print("🎉 可能找到正确转录！")
                    
                except Exception as e:
                    print(f"❌ 转录失败: {e}")
            
        except Exception as e:
            print(f"❌ {model_name} 模型加载失败: {e}")
    
    print(f"\n{'='*50}")
    print("测试完成")

if __name__ == "__main__":
    test_whisper_direct()
