#!/usr/bin/env python3
"""
创建测试音频并验证 Whisper
"""

import numpy as np
import soundfile as sf
from faster_whisper import WhisperModel
import torch

def create_simple_test():
    """创建一个简单的测试音频"""
    
    print("🎵 创建测试音频...")
    
    # 创建一个简单的正弦波音频（模拟语音）
    sample_rate = 16000
    duration = 3  # 3秒
    frequency = 440  # A4音符
    
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    # 创建一个变化的音调来模拟语音
    audio = np.sin(frequency * 2 * np.pi * t) * 0.3
    
    # 添加一些变化来模拟语音特征
    for i in range(0, len(audio), sample_rate // 4):
        end_idx = min(i + sample_rate // 4, len(audio))
        audio[i:end_idx] *= np.sin(np.linspace(0, np.pi, end_idx - i))
    
    # 保存测试音频
    test_file = "test_audio.wav"
    sf.write(test_file, audio, sample_rate)
    print(f"✅ 创建测试音频: {test_file}")
    
    return test_file

def test_known_good_audio():
    """测试一个已知的好音频"""
    
    print("🔍 检查 GPU 状态...")
    if torch.cuda.is_available():
        print(f"✅ CUDA 可用，GPU: {torch.cuda.get_device_name(0)}")
        device = "cuda"
        compute_type = "float16"
    else:
        print("⚠️ 使用 CPU")
        device = "cpu"
        compute_type = "int8"
    
    # 加载 base 模型进行快速测试
    print(f"🔄 加载 Whisper base 模型...")
    model = WhisperModel("base", device=device, compute_type=compute_type)
    
    # 创建测试音频
    test_file = create_simple_test()
    
    print(f"\n🎯 测试转录...")
    try:
        audio_array, sample_rate = sf.read(test_file, dtype='float32')
        
        segments, info = model.transcribe(
            audio_array,
            language=None,
            beam_size=1,
            best_of=1,
            temperature=0.0,
            condition_on_previous_text=False,
            vad_filter=False
        )
        
        transcription = ""
        for segment in segments:
            transcription += segment.text
        
        print(f"检测语言: {info.language}")
        print(f"转录结果: '{transcription}'")
        print(f"结果长度: {len(transcription)}")
        
        if len(transcription.strip()) == 0:
            print("⚠️ 转录结果为空 - 这是正常的，因为这是纯音调")
        else:
            print("✅ 模型工作正常")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print(f"\n{'='*50}")
    print("🔍 现在测试您的音频文件...")
    
    # 测试用户的音频文件
    user_files = [
        "/home/<USER>/下載/ponsunlun_1.wav",
        "/home/<USER>/下載/ponsunlun_6.wav"
    ]
    
    for audio_file in user_files:
        print(f"\n--- 测试: {audio_file} ---")
        try:
            audio_array, sample_rate = sf.read(audio_file, dtype='float32')
            
            print(f"音频信息: {audio_array.shape}, {sample_rate}Hz, {len(audio_array)/sample_rate:.1f}s")
            
            # 转为单声道
            if len(audio_array.shape) > 1:
                audio_array = np.mean(audio_array, axis=1)
            
            # 检查音频特征
            max_amplitude = np.max(np.abs(audio_array))
            mean_amplitude = np.mean(np.abs(audio_array))
            
            print(f"最大振幅: {max_amplitude:.4f}")
            print(f"平均振幅: {mean_amplitude:.4f}")
            
            # 检查是否主要是静音
            silence_threshold = 0.01
            silence_ratio = np.sum(np.abs(audio_array) < silence_threshold) / len(audio_array)
            print(f"静音比例: {silence_ratio:.1%}")
            
            if silence_ratio > 0.8:
                print("⚠️ 音频主要是静音")
            elif max_amplitude < 0.1:
                print("⚠️ 音频音量很低")
            else:
                print("✅ 音频看起来正常")
            
            # 尝试转录
            print("开始转录...")
            segments, info = model.transcribe(
                audio_array,
                language="zh",
                beam_size=1,
                best_of=1,
                temperature=0.0,
                condition_on_previous_text=False,
                vad_filter=False
            )
            
            transcription = ""
            for segment in segments:
                transcription += segment.text
            
            print(f"检测语言: {info.language} (概率: {info.language_probability:.3f})")
            print(f"转录结果: '{transcription[:100]}{'...' if len(transcription) > 100 else ''}'")
            
            # 检查是否有重复
            if len(transcription) > 20:
                words = transcription.split()
                if len(words) > 0:
                    most_common_word = max(set(words), key=words.count)
                    repeat_count = words.count(most_common_word)
                    repeat_ratio = repeat_count / len(words)
                    
                    if repeat_ratio > 0.5:
                        print(f"⚠️ 检测到重复: '{most_common_word}' 重复 {repeat_count} 次 ({repeat_ratio:.1%})")
                    else:
                        print("✅ 没有明显重复")
            
        except Exception as e:
            print(f"❌ 处理失败: {e}")

if __name__ == "__main__":
    test_known_good_audio()
