<!DOCTYPE html>
<html lang="zh-TW">
<head>
  <meta charset="UTF-8">
  <title>實時語音轉文字系統 - 後端服務</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
      color: #333;
    }

    .container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    h1 {
      color: #1976d2;
      text-align: center;
      margin-bottom: 30px;
    }

    .status {
      padding: 15px;
      border-radius: 5px;
      margin: 20px 0;
      text-align: center;
    }

    .status.success {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }

    .status.error {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }

    .info-section {
      margin: 20px 0;
      padding: 15px;
      background-color: #f8f9fa;
      border-radius: 5px;
    }

    .endpoint {
      font-family: monospace;
      background-color: #e9ecef;
      padding: 5px 10px;
      border-radius: 3px;
      margin: 5px 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>實時語音轉文字系統 - 後端服務</h1>

    <div id="status" class="status">
      檢查服務狀態中...
    </div>

    <div class="info-section">
      <h3>服務信息</h3>
      <p><strong>服務端口:</strong> 9004</p>
      <p><strong>WebSocket 端點:</strong> <span class="endpoint">ws://localhost:9004/socket.io/</span></p>
      <p><strong>文件上傳 API:</strong> <span class="endpoint">POST /api/transcribe</span></p>
      <p><strong>健康檢查:</strong> <span class="endpoint">GET /health</span></p>
    </div>

    <div class="info-section">
      <h3>前端應用</h3>
      <p>React 前端應用運行在: <a href="http://localhost:3000" target="_blank">http://localhost:3000</a></p>
      <p>請確保前端應用已啟動並正確配置。</p>
    </div>

    <div class="info-section">
      <h3>使用說明</h3>
      <ul>
        <li>此頁面顯示後端服務狀態</li>
        <li>實際的語音轉文字功能請使用前端應用</li>
        <li>支持實時語音轉文字和文件上傳轉錄</li>
        <li>基於 Faster Whisper 模型提供高質量轉錄</li>
      </ul>
    </div>
  </div>

  <script>
    // 檢查服務狀態
    async function checkServiceStatus() {
      const statusDiv = document.getElementById('status');

      try {
        const response = await fetch('/health');
        const data = await response.json();

        if (data.status === 'healthy') {
          statusDiv.className = 'status success';
          statusDiv.innerHTML = `
            ✅ 服務運行正常<br>
            模型已載入: ${data.model_loaded ? '是' : '否'}<br>
            最後更新: ${new Date(data.timestamp).toLocaleString()}
          `;
        } else {
          throw new Error('Service not healthy');
        }
      } catch (error) {
        statusDiv.className = 'status error';
        statusDiv.innerHTML = `
          ❌ 服務連接失敗<br>
          錯誤: ${error.message}<br>
          請檢查後端服務是否正常運行
        `;
      }
    }

    // 頁面載入時檢查狀態
    document.addEventListener('DOMContentLoaded', checkServiceStatus);

    // 每30秒檢查一次狀態
    setInterval(checkServiceStatus, 30000);
  </script>
</body>
</html>

