<!DOCTYPE html>
<html lang="zh-TW">
<head>
  <meta charset="UTF-8">
  <title>即時會議轉錄系統 (系統聲音 + 麥克風)</title>
  <style>
    body {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 20px;
      min-height: 100vh;
      color: white;
    }
    
    .container {
      max-width: 1400px;
      margin: 0 auto;
      background: rgba(255, 255, 255, 0.95);
      border-radius: 20px;
      padding: 30px;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
      color: #333;
    }
    
    h1 {
      text-align: center;
      color: #2c3e50;
      margin-bottom: 30px;
      font-size: 2.5em;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    }
    
    .control-panel {
      background: #f8f9fa;
      border-radius: 15px;
      padding: 25px;
      margin-bottom: 25px;
      border: 2px solid #e9ecef;
    }
    
    .control-row {
      display: flex;
      align-items: center;
      margin: 15px 0;
      flex-wrap: wrap;
      gap: 15px;
    }
    
    button {
      padding: 12px 24px;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
      border: none;
      border-radius: 8px;
      transition: all 0.3s ease;
      min-width: 120px;
    }
    
    button.primary {
      background: linear-gradient(45deg, #28a745, #20c997);
      color: white;
    }
    
    button.danger {
      background: linear-gradient(45deg, #dc3545, #fd7e14);
      color: white;
    }
    
    button.secondary {
      background: linear-gradient(45deg, #6c757d, #495057);
      color: white;
    }
    
    button:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    
    button:disabled {
      background: #6c757d;
      cursor: not-allowed;
      opacity: 0.6;
    }
    
    button.recording {
      animation: pulse 1.5s infinite;
    }
    
    @keyframes pulse {
      0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
      70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
      100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
    }
    
    select {
      padding: 10px 15px;
      font-size: 16px;
      border: 2px solid #dee2e6;
      border-radius: 8px;
      background: white;
      min-width: 200px;
    }
    
    .status-panel {
      background: #e3f2fd;
      border: 2px solid #2196f3;
      border-radius: 15px;
      padding: 20px;
      margin: 20px 0;
    }
    
    .status-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 10px 0;
      padding: 10px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .transcription-panel {
      background: #f8f9fa;
      border-radius: 15px;
      padding: 25px;
      margin: 25px 0;
      max-height: 600px;
      overflow-y: auto;
    }
    
    .transcription-item {
      background: white;
      margin: 10px 0;
      padding: 15px;
      border-radius: 10px;
      border-left: 5px solid #007bff;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      transition: transform 0.2s ease;
    }
    
    .transcription-item:hover {
      transform: translateX(5px);
    }
    
    .transcription-item.system {
      border-left-color: #28a745;
    }
    
    .transcription-item.microphone {
      border-left-color: #ffc107;
    }
    
    .timestamp {
      font-size: 12px;
      color: #6c757d;
      margin-bottom: 5px;
    }
    
    .source-tag {
      display: inline-block;
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: bold;
      margin-right: 10px;
    }
    
    .source-tag.system {
      background: #28a745;
      color: white;
    }
    
    .source-tag.microphone {
      background: #ffc107;
      color: #212529;
    }
    
    .source-tag.mixed {
      background: #6f42c1;
      color: white;
    }
    
    .text-content {
      font-size: 16px;
      line-height: 1.5;
      margin-top: 8px;
    }
    
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin: 20px 0;
    }
    
    .stat-card {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 20px;
      border-radius: 15px;
      text-align: center;
      box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    
    .stat-value {
      font-size: 2em;
      font-weight: bold;
      margin-bottom: 5px;
    }
    
    .stat-label {
      font-size: 14px;
      opacity: 0.9;
    }
    
    .model-selector {
      background: #fff3cd;
      border: 2px solid #ffc107;
      border-radius: 15px;
      padding: 20px;
      margin: 20px 0;
    }
    
    .model-buttons {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
      margin-top: 15px;
    }
    
    .model-buttons button {
      background: #ffc107;
      color: #212529;
      border: 2px solid #ffc107;
    }
    
    .model-buttons button.active {
      background: #28a745;
      color: white;
      border-color: #28a745;
    }
    
    .warning-box {
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      padding: 15px;
      border-radius: 10px;
      margin: 15px 0;
      color: #856404;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🎤 即時會議轉錄系統</h1>
    
    <!-- 使用說明 -->
    <div class="warning-box">
      <h3>📋 使用說明</h3>
      <p><strong>1. WebEx/Teams 會議：</strong>開始混合音頻捕獲，選擇包含會議的瀏覽器標籤</p>
      <p><strong>2. Zoom 會議：</strong>選擇整個螢幕來捕獲 Zoom 桌面應用的聲音</p>
      <p><strong>3. 系統 + 麥克風：</strong>同時捕獲系統聲音和您的麥克風輸入</p>
    </div>
    
    <!-- 模型選擇 -->
    <div class="model-selector">
      <h3>🔧 Whisper 模型選擇</h3>
      <p>選擇轉錄模型（質量越高速度越慢）：</p>
      <div class="model-buttons">
        <button onclick="switchModel('tiny')">Tiny (最快)</button>
        <button onclick="switchModel('base')" class="active">Base (推薦)</button>
        <button onclick="switchModel('small')">Small (平衡)</button>
        <button onclick="switchModel('medium')">Medium (高質量)</button>
        <button onclick="switchModel('large')">Large (最高質量)</button>
      </div>
      <div id="modelStatus">當前模型: Base</div>
    </div>
    
    <!-- 控制面板 -->
    <div class="control-panel">
      <h3>🎛️ 音頻捕獲控制</h3>
      
      <div class="control-row">
        <label>音頻品質：</label>
        <select id="audioQuality">
          <option value="44100,1">44.1 kHz 單聲道</option>
          <option value="44100,2">44.1 kHz 立體聲</option>
          <option value="48000,1">48 kHz 單聲道</option>
          <option value="48000,2">48 kHz 立體聲</option>
        </select>
      </div>
      
      <div class="control-row">
        <button id="startMixedCapture" class="primary">🎯 開始混合音頻捕獲 (系統+麥克風)</button>
        <button id="stopMixedCapture" class="danger" disabled>⏹️ 停止混合捕獲</button>
      </div>
      
      <div class="control-row">
        <button id="startSystemOnly" class="secondary">🖥️ 僅系統音頻</button>
        <button id="startMicOnly" class="secondary">🎙️ 僅麥克風</button>
        <button id="stopAll" class="danger" disabled>🛑 停止全部</button>
      </div>
    </div>
    
    <!-- 狀態面板 -->
    <div class="status-panel">
      <h3>📊 系統狀態</h3>
      <div class="status-item">
        <span>後端連接：</span>
        <span id="backendStatus">檢查中...</span>
      </div>
      <div class="status-item">
        <span>音頻捕獲：</span>
        <span id="audioStatus">待機中</span>
      </div>
      <div class="status-item">
        <span>轉錄狀態：</span>
        <span id="transcriptionStatus">待機中</span>
      </div>
    </div>
    
    <!-- 統計信息 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-value" id="totalTranscriptions">0</div>
        <div class="stat-label">總轉錄次數</div>
      </div>
      <div class="stat-card">
        <div class="stat-value" id="systemCount">0</div>
        <div class="stat-label">系統音頻</div>
      </div>
      <div class="stat-card">
        <div class="stat-value" id="micCount">0</div>
        <div class="stat-label">麥克風</div>
      </div>
      <div class="stat-card">
        <div class="stat-value" id="mixedCount">0</div>
        <div class="stat-label">混合音頻</div>
      </div>
    </div>
    
    <!-- 轉錄結果 -->
    <div class="transcription-panel">
      <h3>📝 即時轉錄結果</h3>
      <div style="margin-bottom: 15px;">
        <button onclick="clearTranscriptions()" class="secondary">🗑️ 清空</button>
        <button onclick="exportTranscriptions()" class="secondary">💾 匯出</button>
        <button onclick="copyAllText()" class="secondary">📋 複製全部</button>
      </div>
      <div id="transcriptionDisplay">
        <div class="transcription-item">
          <div class="timestamp">等待中...</div>
          <span class="source-tag mixed">混合音頻</span>
          <div class="text-content">轉錄結果將在此顯示，請開始音頻捕獲</div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 全局變量
    let currentModel = 'base';
    let audioContext;
    let mediaRecorder;
    let capturedStream; // 系統音頻流
    let micStream;      // 麥克風流
    let isRecording = false;
    let audioBuffer = [];
    let lastProcessTime = 0;
    let transcriptionCount = {
      total: 0,
      system: 0,
      microphone: 0,
      mixed: 0
    };

    // 頁面載入時初始化
    window.onload = function() {
      checkBackendStatus();
    };

    // 檢查後端狀態
    async function checkBackendStatus() {
      try {
        const response = await fetch('http://localhost:9004/health');
        const data = await response.json();
        
        if (data.model_loaded) {
          document.getElementById('backendStatus').innerHTML = 
            `<span style="color: #28a745;">✅ 正常 (${data.model_name})</span>`;
        } else {
          document.getElementById('backendStatus').innerHTML = 
            '<span style="color: #dc3545;">❌ 模型未加載</span>';
        }
      } catch (error) {
        document.getElementById('backendStatus').innerHTML = 
          '<span style="color: #dc3545;">❌ 連接失敗</span>';
      }
    }

    // 切換模型
    async function switchModel(modelName) {
      try {
        const response = await fetch(`http://localhost:9004/models/${modelName}`, {
          method: 'POST'
        });
        
        const data = await response.json();
        
        if (data.success) {
          updateModelButtons(modelName);
          currentModel = modelName;
          document.getElementById('modelStatus').textContent = `當前模型: ${modelName}`;
        } else {
          alert(`切換失敗: ${data.message}`);
        }
      } catch (error) {
        alert(`切換失敗: ${error.message}`);
      }
    }

    // 更新模型按鈕狀態
    function updateModelButtons(activeModel) {
      const buttons = document.querySelectorAll('.model-buttons button');
      buttons.forEach(button => {
        button.classList.remove('active');
        if (button.textContent.toLowerCase().includes(activeModel)) {
          button.classList.add('active');
        }
      });
    }

    // 開始混合音頻捕獲 (系統 + 麥克風)
    document.getElementById('startMixedCapture').addEventListener('click', async () => {
      try {
        const [sampleRate, channelCount] = document.getElementById('audioQuality').value.split(',').map(Number);

        // 1. 捕獲系統音頻 (螢幕分享)
        capturedStream = await navigator.mediaDevices.getDisplayMedia({
          video: false,
          audio: {
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false,
            sampleRate: sampleRate,
            channelCount: channelCount
          }
        });

        // 2. 捕獲麥克風音頻
        micStream = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false,
            sampleRate: sampleRate,
            channelCount: 1
          }
        });

        // 3. 建立 AudioContext 混合音頻
        audioContext = new AudioContext({ sampleRate: sampleRate });
        const destination = audioContext.createMediaStreamDestination();

        // 連接系統音頻
        if (capturedStream.getAudioTracks().length > 0) {
          const systemSource = audioContext.createMediaStreamSource(capturedStream);
          systemSource.connect(destination);
        }

        // 連接麥克風音頻
        if (micStream.getAudioTracks().length > 0) {
          const micSource = audioContext.createMediaStreamSource(micStream);
          micSource.connect(destination);
        }

        // 4. 設置音頻處理
        setupAudioProcessing(destination.stream, 'mixed');

        // 5. 更新 UI
        document.getElementById('startMixedCapture').disabled = true;
        document.getElementById('stopMixedCapture').disabled = false;
        document.getElementById('stopAll').disabled = false;
        document.getElementById('startMixedCapture').classList.add('recording');
        
        document.getElementById('audioStatus').innerHTML = 
          '<span style="color: #28a745;">🎵 混合音頻捕獲中 (系統+麥克風)</span>';
        
        isRecording = true;

      } catch (error) {
        console.error('混合音頻捕獲失敗:', error);
        document.getElementById('audioStatus').innerHTML = 
          `<span style="color: #dc3545;">❌ 捕獲失敗: ${error.message}</span>`;
      }
    });

    // 僅系統音頻
    document.getElementById('startSystemOnly').addEventListener('click', async () => {
      try {
        const [sampleRate, channelCount] = document.getElementById('audioQuality').value.split(',').map(Number);

        capturedStream = await navigator.mediaDevices.getDisplayMedia({
          video: false,
          audio: {
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false,
            sampleRate: sampleRate,
            channelCount: channelCount
          }
        });

        setupAudioProcessing(capturedStream, 'system');
        
        document.getElementById('stopAll').disabled = false;
        document.getElementById('audioStatus').innerHTML = 
          '<span style="color: #007bff;">🖥️ 系統音頻捕獲中</span>';
        
        isRecording = true;

      } catch (error) {
        console.error('系統音頻捕獲失敗:', error);
        document.getElementById('audioStatus').innerHTML = 
          `<span style="color: #dc3545;">❌ 系統音頻失敗: ${error.message}</span>`;
      }
    });

    // 僅麥克風
    document.getElementById('startMicOnly').addEventListener('click', async () => {
      try {
        const [sampleRate] = document.getElementById('audioQuality').value.split(',').map(Number);

        micStream = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: false,
            sampleRate: sampleRate
          }
        });

        setupAudioProcessing(micStream, 'microphone');
        
        document.getElementById('stopAll').disabled = false;
        document.getElementById('audioStatus').innerHTML = 
          '<span style="color: #ffc107;">🎙️ 麥克風捕獲中</span>';
        
        isRecording = true;

      } catch (error) {
        console.error('麥克風捕獲失敗:', error);
        document.getElementById('audioStatus').innerHTML = 
          `<span style="color: #dc3545;">❌ 麥克風失敗: ${error.message}</span>`;
      }
    });

    // 設置音頻處理
    function setupAudioProcessing(stream, sourceType) {
      if (!audioContext) {
        audioContext = new AudioContext();
      }

      const source = audioContext.createMediaStreamSource(stream);
      const processor = audioContext.createScriptProcessor(4096, 1, 1);

      processor.onaudioprocess = function(event) {
        if (!isRecording) return;
        
        const audioData = event.inputBuffer.getChannelData(0);
        processAudioChunk(audioData, sourceType);
      };

      source.connect(processor);
      processor.connect(audioContext.destination);
    }

    // 處理音頻片段
    function processAudioChunk(audioData, sourceType) {
      // 累積音頻數據
      audioBuffer.push(...audioData);
      
      // 每3秒處理一次
      const now = Date.now();
      if (now - lastProcessTime > 3000 && audioBuffer.length > 0) {
        lastProcessTime = now;
        
        // 轉換為 WAV 並發送轉錄
        const audioBlob = createWavBlob(new Float32Array(audioBuffer), 44100);
        sendAudioForTranscription(audioBlob, sourceType);
        
        // 保留重疊
        audioBuffer = audioBuffer.slice(-4096);
      }
    }

    // 創建 WAV Blob
    function createWavBlob(audioData, sampleRate) {
      const length = audioData.length;
      const buffer = new ArrayBuffer(44 + length * 2);
      const view = new DataView(buffer);
      
      // WAV 文件頭
      const writeString = (offset, string) => {
        for (let i = 0; i < string.length; i++) {
          view.setUint8(offset + i, string.charCodeAt(i));
        }
      };
      
      writeString(0, 'RIFF');
      view.setUint32(4, 36 + length * 2, true);
      writeString(8, 'WAVE');
      writeString(12, 'fmt ');
      view.setUint32(16, 16, true);
      view.setUint16(20, 1, true);
      view.setUint16(22, 1, true);
      view.setUint32(24, sampleRate, true);
      view.setUint32(28, sampleRate * 2, true);
      view.setUint16(32, 2, true);
      view.setUint16(34, 16, true);
      writeString(36, 'data');
      view.setUint32(40, length * 2, true);
      
      // 音頻數據
      let offset = 44;
      for (let i = 0; i < length; i++) {
        const sample = Math.max(-1, Math.min(1, audioData[i]));
        view.setInt16(offset, sample * 0x7FFF, true);
        offset += 2;
      }
      
      return new Blob([buffer], { type: 'audio/wav' });
    }

    // 發送音頻進行轉錄
    async function sendAudioForTranscription(audioBlob, sourceType) {
      try {
        document.getElementById('transcriptionStatus').innerHTML = 
          '<span style="color: #007bff;">🔄 轉錄中...</span>';

        const formData = new FormData();
        formData.append('audio_file', audioBlob, 'audio.wav');

        const response = await fetch('http://localhost:9004/api/transcribe', {
          method: 'POST',
          body: formData
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`);
        }

        const data = await response.json();
        
        if (data.transcription && data.transcription.trim()) {
          addTranscription(data.transcription, sourceType, data.model_name);
          document.getElementById('transcriptionStatus').innerHTML = 
            '<span style="color: #28a745;">✅ 轉錄完成</span>';
        } else {
          document.getElementById('transcriptionStatus').innerHTML = 
            '<span style="color: #6c757d;">⏸️ 無語音內容</span>';
        }
      } catch (error) {
        console.error('轉錄錯誤:', error);
        document.getElementById('transcriptionStatus').innerHTML = 
          '<span style="color: #dc3545;">❌ 轉錄失敗</span>';
      }
    }

    // 添加轉錄結果
    function addTranscription(text, sourceType, modelName) {
      const display = document.getElementById('transcriptionDisplay');
      const timestamp = new Date().toLocaleTimeString();
      
      const sourceNames = {
        'system': '系統音頻',
        'microphone': '麥克風',
        'mixed': '混合音頻'
      };

      const item = document.createElement('div');
      item.className = `transcription-item ${sourceType}`;
      item.innerHTML = `
        <div class="timestamp">${timestamp}</div>
        <span class="source-tag ${sourceType}">${sourceNames[sourceType]}</span>
        <span style="font-size: 12px; color: #666;">[${modelName || currentModel}]</span>
        <div class="text-content">${text}</div>
      `;

      display.appendChild(item);
      display.scrollTop = display.scrollHeight;

      // 更新統計
      transcriptionCount.total++;
      transcriptionCount[sourceType]++;
      updateStats();
    }

    // 更新統計
    function updateStats() {
      document.getElementById('totalTranscriptions').textContent = transcriptionCount.total;
      document.getElementById('systemCount').textContent = transcriptionCount.system;
      document.getElementById('micCount').textContent = transcriptionCount.microphone;
      document.getElementById('mixedCount').textContent = transcriptionCount.mixed;
    }

    // 停止所有捕獲
    function stopAllCapture() {
      isRecording = false;
      
      if (capturedStream) {
        capturedStream.getTracks().forEach(track => track.stop());
        capturedStream = null;
      }
      
      if (micStream) {
        micStream.getTracks().forEach(track => track.stop());
        micStream = null;
      }
      
      if (audioContext) {
        audioContext.close();
        audioContext = null;
      }
      
      // 重置 UI
      document.getElementById('startMixedCapture').disabled = false;
      document.getElementById('stopMixedCapture').disabled = true;
      document.getElementById('stopAll').disabled = true;
      document.getElementById('startMixedCapture').classList.remove('recording');
      
      document.getElementById('audioStatus').innerHTML = 
        '<span style="color: #6c757d;">⏹️ 已停止</span>';
      document.getElementById('transcriptionStatus').innerHTML = 
        '<span style="color: #6c757d;">待機中</span>';
    }

    // 事件監聽器
    document.getElementById('stopMixedCapture').addEventListener('click', stopAllCapture);
    document.getElementById('stopAll').addEventListener('click', stopAllCapture);

    // 清空轉錄
    function clearTranscriptions() {
      document.getElementById('transcriptionDisplay').innerHTML = '';
      transcriptionCount = { total: 0, system: 0, microphone: 0, mixed: 0 };
      updateStats();
    }

    // 匯出轉錄
    function exportTranscriptions() {
      const display = document.getElementById('transcriptionDisplay');
      const items = display.querySelectorAll('.transcription-item');

      let exportText = '會議轉錄記錄\n';
      exportText += '================\n\n';

      items.forEach(item => {
        const timestamp = item.querySelector('.timestamp').textContent;
        const source = item.querySelector('.source-tag').textContent;
        const textContent = item.querySelector('.text-content').textContent;
        exportText += `${timestamp} [${source}] ${textContent}\n\n`;
      });

      const blob = new Blob([exportText], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `meeting_transcription_${new Date().toISOString().slice(0, 19)}.txt`;
      a.click();
      URL.revokeObjectURL(url);
    }

    // 複製全部文字
    function copyAllText() {
      const display = document.getElementById('transcriptionDisplay');
      const items = display.querySelectorAll('.transcription-item');

      let copyText = '';
      items.forEach(item => {
        const textContent = item.querySelector('.text-content').textContent;
        if (textContent) {
          copyText += textContent + '\n';
        }
      });

      navigator.clipboard.writeText(copyText).then(() => {
        alert('轉錄內容已複製到剪貼板');
      });
    }
  </script>
</body>
</html>
