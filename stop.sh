#!/bin/bash

# 智能语音转录系统停止脚本
# 作者: AI Assistant
# 日期: 2025-07-18

set -e

echo "🛑 停止智能语音转录系统..."

# 停止并删除容器
echo "📦 停止并删除容器..."
docker-compose down --remove-orphans

# 清理未使用的镜像（可选）
read -p "是否清理未使用的Docker镜像？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🧹 清理未使用的镜像..."
    docker image prune -f
fi

# 清理未使用的卷（可选）
read -p "是否清理未使用的Docker卷？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🧹 清理未使用的卷..."
    docker volume prune -f
fi

echo "✅ 系统已停止！"
