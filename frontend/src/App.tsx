import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  <PERSON>,
  ThemeProvider,
  createTheme,
  CssBaseline,
  Alert,
  Tabs,
  Tab,
  Paper
} from '@mui/material';
import {
  Mic,
  CloudUpload,
  Settings
} from '@mui/icons-material';

import { RecordingControls } from './components/RecordingControls';
import { TranscriptionDisplay } from './components/TranscriptionDisplay';
import { FileUpload } from './components/FileUpload';
import { useSTTService } from './hooks/useSTTService';
import { checkBrowserSupport } from './utils/audioProcessor';

// 創建主題
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", "Noto Sans TC", sans-serif',
  },
});

// WebSocket URL - 根據您的後端配置調整
const WEBSOCKET_URL = process.env.REACT_APP_WEBSOCKET_URL || 'ws://localhost:9004/ws';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`tabpanel-${index}`}
      aria-labelledby={`tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
}

function App() {
  const [tabValue, setTabValue] = useState(0);
  const [includeSystemAudio, setIncludeSystemAudio] = useState(false);
  const [browserSupport, setBrowserSupport] = useState<{
    supported: boolean;
    missingFeatures: string[];
  }>({ supported: true, missingFeatures: [] });

  // 使用 STT 服務
  const {
    isActive,
    isConnected,
    isRecording,
    recordingState,
    transcriptions,
    error,
    status,
    startSTT,
    stopSTT,
    clearTranscriptions,
    reconnect
  } = useSTTService({
    websocketUrl: WEBSOCKET_URL,
    includeSystemAudio
  });

  // 檢查瀏覽器支援
  useEffect(() => {
    const support = checkBrowserSupport();
    setBrowserSupport(support);
  }, []);

  // 處理標籤切換
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // 處理文件上傳
  const handleFileUpload = async (file: File): Promise<string> => {
    const formData = new FormData();
    formData.append('audio_file', file);

    try {
      const response = await fetch('/api/transcribe', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.error) {
        throw new Error(result.error);
      }

      return result.transcription || result.text || '轉錄失敗';
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : '上傳失敗');
    }
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* 標題 */}
        <Box sx={{ textAlign: 'center', mb: 4 }}>
          <Typography variant="h3" component="h1" gutterBottom>
            實時語音轉文字系統
          </Typography>
          <Typography variant="h6" color="text.secondary">
            支持 YouTube 影片和線上會議字幕 • 基於 Faster Whisper
          </Typography>
        </Box>

        {/* 瀏覽器支援檢查 */}
        {!browserSupport.supported && (
          <Alert severity="error" sx={{ mb: 3 }}>
            <Typography variant="body1" gutterBottom>
              您的瀏覽器不支援以下功能：
            </Typography>
            <ul>
              {browserSupport.missingFeatures.map((feature, index) => (
                <li key={index}>{feature}</li>
              ))}
            </ul>
            <Typography variant="body2">
              請使用最新版本的 Chrome、Firefox 或 Edge 瀏覽器。
            </Typography>
          </Alert>
        )}

        {/* 主要內容區域 */}
        <Paper sx={{ mt: 3 }}>
          {/* 標籤導航 */}
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs value={tabValue} onChange={handleTabChange} aria-label="功能標籤">
              <Tab
                icon={<Mic />}
                label="實時語音轉文字"
                id="tab-0"
                aria-controls="tabpanel-0"
              />
              <Tab
                icon={<CloudUpload />}
                label="文件上傳轉錄"
                id="tab-1"
                aria-controls="tabpanel-1"
              />
            </Tabs>
          </Box>

          {/* 實時語音轉文字面板 */}
          <TabPanel value={tabValue} index={0}>
            <RecordingControls
              isActive={isActive}
              isConnected={isConnected}
              isRecording={isRecording}
              recordingState={recordingState}
              includeSystemAudio={includeSystemAudio}
              error={error}
              status={status}
              onStart={startSTT}
              onStop={stopSTT}
              onToggleSystemAudio={setIncludeSystemAudio}
              onReconnect={reconnect}
            />

            <TranscriptionDisplay
              transcriptions={transcriptions}
              onClear={clearTranscriptions}
              isActive={isActive}
            />
          </TabPanel>

          {/* 文件上傳面板 */}
          <TabPanel value={tabValue} index={1}>
            <FileUpload onUpload={handleFileUpload} />
          </TabPanel>
        </Paper>

        {/* 頁腳信息 */}
        <Box sx={{ mt: 4, textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            基於 React + TypeScript + Faster Whisper 構建
          </Typography>
        </Box>
      </Container>
    </ThemeProvider>
  );
}

export default App;
