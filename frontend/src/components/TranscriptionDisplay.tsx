import React, { useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  IconButton,
  Tooltip,
  Divider,
  Chip
} from '@mui/material';
import {
  Clear,
  ContentCopy,
  Download,
  VolumeUp
} from '@mui/icons-material';
import { TranscriptionResult } from '../types';

interface TranscriptionDisplayProps {
  transcriptions: TranscriptionResult[];
  onClear: () => void;
  isActive: boolean;
}

export const TranscriptionDisplay: React.FC<TranscriptionDisplayProps> = ({
  transcriptions,
  onClear,
  isActive
}) => {
  const scrollRef = useRef<HTMLDivElement>(null);

  // 自動滾動到最新內容
  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [transcriptions]);

  // 複製所有文字到剪貼板
  const handleCopyAll = async () => {
    const allText = transcriptions.map(t => t.text).join('\n');
    try {
      await navigator.clipboard.writeText(allText);
      // 可以添加成功提示
    } catch (error) {
      console.error('Failed to copy text:', error);
    }
  };

  // 下載轉錄結果為文字檔
  const handleDownload = () => {
    const allText = transcriptions.map((t, index) => {
      const timestamp = new Date(t.timestamp).toLocaleTimeString();
      return `[${timestamp}] ${t.text}`;
    }).join('\n');

    const blob = new Blob([allText], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `transcription_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 格式化時間戳
  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString('zh-TW', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  // 獲取信心度顏色
  const getConfidenceColor = (confidence?: number) => {
    if (!confidence) return 'default';
    if (confidence >= 0.8) return 'success';
    if (confidence >= 0.6) return 'warning';
    return 'error';
  };

  return (
    <Box sx={{ mt: 3 }}>
      {/* 標題和控制按鈕 */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">
          轉錄結果
          {isActive && (
            <Chip
              label="即時轉錄中"
              color="success"
              size="small"
              sx={{ ml: 2 }}
              icon={<VolumeUp />}
            />
          )}
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Tooltip title="複製全部文字">
            <IconButton
              onClick={handleCopyAll}
              disabled={transcriptions.length === 0}
              size="small"
            >
              <ContentCopy />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="下載為文字檔">
            <IconButton
              onClick={handleDownload}
              disabled={transcriptions.length === 0}
              size="small"
            >
              <Download />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="清除所有內容">
            <IconButton
              onClick={onClear}
              disabled={transcriptions.length === 0}
              size="small"
              color="error"
            >
              <Clear />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* 轉錄內容顯示區域 */}
      <Paper
        ref={scrollRef}
        sx={{
          height: 400,
          overflow: 'auto',
          p: 2,
          bgcolor: '#fafafa',
          border: '1px solid #e0e0e0'
        }}
      >
        {transcriptions.length === 0 ? (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              height: '100%',
              color: 'text.secondary'
            }}
          >
            <Typography variant="body1">
              {isActive ? '等待語音輸入...' : '轉錄結果將顯示在此處'}
            </Typography>
          </Box>
        ) : (
          <Box>
            {transcriptions.map((transcription, index) => (
              <Box key={transcription.id} sx={{ mb: 2 }}>
                {/* 時間戳和信心度 */}
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                  <Chip
                    label={formatTimestamp(transcription.timestamp)}
                    size="small"
                    variant="outlined"
                  />
                  {transcription.confidence && (
                    <Chip
                      label={`信心度: ${(transcription.confidence * 100).toFixed(1)}%`}
                      size="small"
                      color={getConfidenceColor(transcription.confidence) as any}
                      variant="outlined"
                    />
                  )}
                </Box>
                
                {/* 轉錄文字 */}
                <Typography
                  variant="body1"
                  sx={{
                    lineHeight: 1.6,
                    p: 1.5,
                    bgcolor: 'white',
                    borderRadius: 1,
                    border: '1px solid #e0e0e0',
                    wordBreak: 'break-word'
                  }}
                >
                  {transcription.text}
                </Typography>
                
                {index < transcriptions.length - 1 && (
                  <Divider sx={{ mt: 2 }} />
                )}
              </Box>
            ))}
            
            {/* 即時錄製指示器 */}
            {isActive && (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 2 }}>
                <Box
                  sx={{
                    width: 8,
                    height: 8,
                    borderRadius: '50%',
                    bgcolor: 'success.main',
                    animation: 'pulse 1.5s infinite'
                  }}
                />
                <Typography variant="body2" color="text.secondary">
                  正在監聽...
                </Typography>
              </Box>
            )}
          </Box>
        )}
      </Paper>

      {/* 統計信息 */}
      {transcriptions.length > 0 && (
        <Box sx={{ mt: 2, display: 'flex', gap: 2 }}>
          <Chip
            label={`共 ${transcriptions.length} 段轉錄`}
            size="small"
            variant="outlined"
          />
          <Chip
            label={`總字數: ${transcriptions.reduce((sum, t) => sum + t.text.length, 0)}`}
            size="small"
            variant="outlined"
          />
        </Box>
      )}

      {/* CSS 動畫 */}
      <style>
        {`
          @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
          }
        `}
      </style>
    </Box>
  );
};
