import React from 'react';
import {
  Box,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Ty<PERSON>graphy,
  Alert,
  Chip
} from '@mui/material';
import {
  <PERSON><PERSON>,
  MicOff,
  Stop,
  PlayArrow,
  DesktopWindows,
  Refresh
} from '@mui/icons-material';
import { RecordingState } from '../types';

interface RecordingControlsProps {
  isActive: boolean;
  isConnected: boolean;
  isRecording: boolean;
  recordingState: RecordingState;
  includeSystemAudio: boolean;
  error: string | null;
  status: string;
  onStart: () => void;
  onStop: () => void;
  onToggleSystemAudio: (enabled: boolean) => void;
  onReconnect: () => void;
}

export const RecordingControls: React.FC<RecordingControlsProps> = ({
  isActive,
  isConnected,
  isRecording,
  recordingState,
  includeSystemAudio,
  error,
  status,
  onStart,
  onStop,
  onToggleSystemAudio,
  onReconnect
}) => {
  const getStatusColor = () => {
    if (error) return 'error';
    if (isActive && isRecording) return 'success';
    if (isConnected) return 'info';
    return 'default';
  };

  const getStatusText = () => {
    if (error) return '錯誤';
    if (isActive && isRecording) return '錄製中';
    if (isActive) return '處理中';
    if (isConnected) return '已連接';
    return '未連接';
  };

  const getRecordingStateText = () => {
    switch (recordingState) {
      case RecordingState.IDLE:
        return '待機';
      case RecordingState.RECORDING:
        return '錄製中';
      case RecordingState.PROCESSING:
        return '處理中';
      case RecordingState.ERROR:
        return '錯誤';
      default:
        return '未知';
    }
  };

  return (
    <Box sx={{ p: 3, border: '1px solid #e0e0e0', borderRadius: 2, mb: 3 }}>
      <Typography variant="h6" gutterBottom>
        語音轉文字控制
      </Typography>

      {/* 狀態顯示 */}
      <Box sx={{ mb: 2, display: 'flex', gap: 1, alignItems: 'center' }}>
        <Chip
          label={getStatusText()}
          color={getStatusColor() as any}
          size="small"
          icon={isRecording ? <Mic /> : <MicOff />}
        />
        <Chip
          label={`錄製狀態: ${getRecordingStateText()}`}
          variant="outlined"
          size="small"
        />
        {status && (
          <Chip
            label={status}
            variant="outlined"
            size="small"
            color="info"
          />
        )}
      </Box>

      {/* 錯誤提示 */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* 系統音頻開關 */}
      <FormControlLabel
        control={
          <Switch
            checked={includeSystemAudio}
            onChange={(e) => onToggleSystemAudio(e.target.checked)}
            disabled={isActive}
          />
        }
        label={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <DesktopWindows fontSize="small" />
            包含系統音頻 (用於 YouTube 影片或線上會議)
          </Box>
        }
        sx={{ mb: 2 }}
      />

      {/* 控制按鈕 */}
      <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
        {!isActive ? (
          <Button
            variant="contained"
            color="primary"
            startIcon={<PlayArrow />}
            onClick={onStart}
            disabled={!isConnected}
            size="large"
          >
            開始語音轉文字
          </Button>
        ) : (
          <Button
            variant="contained"
            color="error"
            startIcon={<Stop />}
            onClick={onStop}
            size="large"
          >
            停止錄製
          </Button>
        )}

        {!isConnected && (
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={onReconnect}
            disabled={isActive}
          >
            重新連接
          </Button>
        )}
      </Box>

      {/* 使用說明 */}
      <Box sx={{ mt: 2, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
        <Typography variant="body2" color="text.secondary">
          <strong>使用說明：</strong>
          <br />
          • 開啟「包含系統音頻」可以捕捉 YouTube 影片或線上會議的聲音
          • 系統會每 2 秒處理一次音頻並進行語音轉文字
          • 確保瀏覽器已授權麥克風和螢幕錄製權限
        </Typography>
      </Box>
    </Box>
  );
};
