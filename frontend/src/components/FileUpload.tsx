import React, { useState, useRef } from 'react';
import {
  Box,
  Button,
  Typography,
  Paper,
  LinearProgress,
  Alert,
  Chip,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  CloudUpload,
  AudioFile,
  Clear,
  ContentCopy
} from '@mui/icons-material';

interface FileUploadProps {
  onUpload: (file: File) => Promise<string>;
}

export const FileUpload: React.FC<FileUploadProps> = ({ onUpload }) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [result, setResult] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 支持的音頻格式
  const supportedFormats = [
    '.wav', '.mp3', '.m4a', '.ogg', '.flac', '.aac', '.wma'
  ];

  // 處理文件選擇
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setResult('');
      setError(null);
    }
  };

  // 處理文件上傳
  const handleUpload = async () => {
    if (!selectedFile) return;

    setIsUploading(true);
    setError(null);

    try {
      const transcription = await onUpload(selectedFile);
      setResult(transcription);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '上傳失敗';
      setError(errorMessage);
    } finally {
      setIsUploading(false);
    }
  };

  // 清除選擇的文件
  const handleClear = () => {
    setSelectedFile(null);
    setResult('');
    setError(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // 複製結果到剪貼板
  const handleCopyResult = async () => {
    try {
      await navigator.clipboard.writeText(result);
      // 可以添加成功提示
    } catch (error) {
      console.error('Failed to copy text:', error);
    }
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 檢查文件格式是否支持
  const isFileSupported = (file: File): boolean => {
    const extension = '.' + file.name.split('.').pop()?.toLowerCase();
    return supportedFormats.includes(extension);
  };

  return (
    <Box sx={{ mt: 4 }}>
      <Typography variant="h6" gutterBottom>
        上傳音頻文件進行轉錄
      </Typography>

      {/* 文件選擇區域 */}
      <Paper
        sx={{
          p: 3,
          border: '2px dashed #e0e0e0',
          borderRadius: 2,
          textAlign: 'center',
          cursor: 'pointer',
          '&:hover': {
            borderColor: '#1976d2',
            bgcolor: '#f5f5f5'
          }
        }}
        onClick={() => fileInputRef.current?.click()}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={supportedFormats.join(',')}
          onChange={handleFileSelect}
          style={{ display: 'none' }}
        />

        <CloudUpload sx={{ fontSize: 48, color: '#1976d2', mb: 2 }} />
        
        <Typography variant="h6" gutterBottom>
          點擊選擇音頻文件
        </Typography>
        
        <Typography variant="body2" color="text.secondary" gutterBottom>
          支持格式: {supportedFormats.join(', ')}
        </Typography>
        
        <Typography variant="body2" color="text.secondary">
          最大文件大小: 100MB
        </Typography>
      </Paper>

      {/* 選中的文件信息 */}
      {selectedFile && (
        <Box sx={{ mt: 2 }}>
          <Paper sx={{ p: 2, bgcolor: '#f5f5f5' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <AudioFile color="primary" />
              <Box sx={{ flex: 1 }}>
                <Typography variant="body1" fontWeight="medium">
                  {selectedFile.name}
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
                  <Chip
                    label={formatFileSize(selectedFile.size)}
                    size="small"
                    variant="outlined"
                  />
                  <Chip
                    label={selectedFile.type || '未知格式'}
                    size="small"
                    variant="outlined"
                    color={isFileSupported(selectedFile) ? 'success' : 'error'}
                  />
                </Box>
              </Box>
              <IconButton onClick={handleClear} size="small">
                <Clear />
              </IconButton>
            </Box>
          </Paper>

          {/* 上傳按鈕 */}
          <Box sx={{ mt: 2, display: 'flex', gap: 2 }}>
            <Button
              variant="contained"
              onClick={handleUpload}
              disabled={isUploading || !isFileSupported(selectedFile)}
              startIcon={<CloudUpload />}
            >
              {isUploading ? '轉錄中...' : '開始轉錄'}
            </Button>
            
            <Button
              variant="outlined"
              onClick={handleClear}
              disabled={isUploading}
            >
              清除
            </Button>
          </Box>

          {/* 進度條 */}
          {isUploading && (
            <Box sx={{ mt: 2 }}>
              <LinearProgress />
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                正在處理音頻文件，請稍候...
              </Typography>
            </Box>
          )}
        </Box>
      )}

      {/* 錯誤提示 */}
      {error && (
        <Alert severity="error" sx={{ mt: 2 }}>
          {error}
        </Alert>
      )}

      {/* 轉錄結果 */}
      {result && (
        <Box sx={{ mt: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              轉錄結果
            </Typography>
            <Tooltip title="複製到剪貼板">
              <IconButton onClick={handleCopyResult} size="small">
                <ContentCopy />
              </IconButton>
            </Tooltip>
          </Box>
          
          <Paper
            sx={{
              p: 2,
              maxHeight: 300,
              overflow: 'auto',
              bgcolor: '#fafafa',
              border: '1px solid #e0e0e0'
            }}
          >
            <Typography
              variant="body1"
              sx={{
                lineHeight: 1.6,
                wordBreak: 'break-word',
                whiteSpace: 'pre-wrap'
              }}
            >
              {result}
            </Typography>
          </Paper>
        </Box>
      )}

      {/* 使用說明 */}
      <Box sx={{ mt: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
        <Typography variant="body2" color="text.secondary">
          <strong>使用說明：</strong>
          <br />
          • 支持多種音頻格式，建議使用 WAV 或 MP3 格式以獲得最佳效果
          • 文件大小限制為 100MB
          • 轉錄時間取決於音頻長度，通常為音頻時長的 10-30%
          • 清晰的錄音質量將獲得更好的轉錄效果
        </Typography>
      </Box>
    </Box>
  );
};
