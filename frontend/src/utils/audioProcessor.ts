// 音频处理工具函数

/**
 * 将 WebM 音频转换为 WAV 格式
 * 注意：这是一个简化版本，实际项目中可能需要使用 FFmpeg.wasm 或其他库
 */
export const convertWebMToWAV = async (webmBlob: Blob): Promise<Blob> => {
  return new Promise((resolve, reject) => {
    const audioContext = new AudioContext();
    const fileReader = new FileReader();

    fileReader.onload = async (e) => {
      try {
        const arrayBuffer = e.target?.result as ArrayBuffer;
        const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
        
        // 转换为 WAV 格式
        const wavBuffer = audioBufferToWav(audioBuffer);
        const wavBlob = new Blob([wavBuffer], { type: 'audio/wav' });
        
        resolve(wavBlob);
      } catch (error) {
        reject(error);
      }
    };

    fileReader.onerror = () => reject(new Error('Failed to read audio file'));
    fileReader.readAsArrayBuffer(webmBlob);
  });
};

/**
 * 将 AudioBuffer 转换为 WAV 格式的 ArrayBuffer
 */
function audioBufferToWav(buffer: AudioBuffer): ArrayBuffer {
  const length = buffer.length;
  const numberOfChannels = buffer.numberOfChannels;
  const sampleRate = buffer.sampleRate;
  const bytesPerSample = 2; // 16-bit
  const blockAlign = numberOfChannels * bytesPerSample;
  const byteRate = sampleRate * blockAlign;
  const dataSize = length * blockAlign;
  const bufferSize = 44 + dataSize;

  const arrayBuffer = new ArrayBuffer(bufferSize);
  const view = new DataView(arrayBuffer);

  // WAV 文件头
  const writeString = (offset: number, string: string) => {
    for (let i = 0; i < string.length; i++) {
      view.setUint8(offset + i, string.charCodeAt(i));
    }
  };

  writeString(0, 'RIFF');
  view.setUint32(4, bufferSize - 8, true);
  writeString(8, 'WAVE');
  writeString(12, 'fmt ');
  view.setUint32(16, 16, true); // PCM format
  view.setUint16(20, 1, true); // PCM
  view.setUint16(22, numberOfChannels, true);
  view.setUint32(24, sampleRate, true);
  view.setUint32(28, byteRate, true);
  view.setUint16(32, blockAlign, true);
  view.setUint16(34, 16, true); // 16-bit
  writeString(36, 'data');
  view.setUint32(40, dataSize, true);

  // 写入音频数据
  let offset = 44;
  for (let i = 0; i < length; i++) {
    for (let channel = 0; channel < numberOfChannels; channel++) {
      const sample = Math.max(-1, Math.min(1, buffer.getChannelData(channel)[i]));
      const intSample = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
      view.setInt16(offset, intSample, true);
      offset += 2;
    }
  }

  return arrayBuffer;
}

/**
 * 检查浏览器是否支持所需的 API
 */
export const checkBrowserSupport = (): { supported: boolean; missingFeatures: string[] } => {
  const missingFeatures: string[] = [];

  if (!navigator.mediaDevices) {
    missingFeatures.push('MediaDevices API');
  }

  if (!navigator.mediaDevices?.getUserMedia) {
    missingFeatures.push('getUserMedia');
  }

  if (!navigator.mediaDevices?.getDisplayMedia) {
    missingFeatures.push('getDisplayMedia');
  }

  if (!window.MediaRecorder) {
    missingFeatures.push('MediaRecorder API');
  }

  if (!window.AudioContext && !(window as any).webkitAudioContext) {
    missingFeatures.push('Web Audio API');
  }

  if (!window.WebSocket) {
    missingFeatures.push('WebSocket');
  }

  return {
    supported: missingFeatures.length === 0,
    missingFeatures
  };
};

/**
 * 获取支持的音频 MIME 类型
 */
export const getSupportedAudioMimeTypes = (): string[] => {
  const types = [
    'audio/webm;codecs=opus',
    'audio/webm',
    'audio/mp4',
    'audio/wav'
  ];

  return types.filter(type => MediaRecorder.isTypeSupported(type));
};

/**
 * 计算音频时长（毫秒）
 */
export const getAudioDuration = (audioBlob: Blob): Promise<number> => {
  return new Promise((resolve, reject) => {
    const audio = new Audio();
    const url = URL.createObjectURL(audioBlob);

    audio.onloadedmetadata = () => {
      URL.revokeObjectURL(url);
      resolve(audio.duration * 1000); // 转换为毫秒
    };

    audio.onerror = () => {
      URL.revokeObjectURL(url);
      reject(new Error('Failed to load audio'));
    };

    audio.src = url;
  });
};
