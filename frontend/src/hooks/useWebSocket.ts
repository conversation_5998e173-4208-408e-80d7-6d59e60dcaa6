import { useState, useEffect, useRef, useCallback } from 'react';
import { io, Socket } from 'socket.io-client';
import { WebSocketMessage, TranscriptionResult } from '../types';

interface UseWebSocketProps {
  url: string;
  onTranscription: (result: TranscriptionResult) => void;
  onError: (error: string) => void;
}

export const useWebSocket = ({ url, onTranscription, onError }: UseWebSocketProps) => {
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const socketRef = useRef<Socket | null>(null);

  const connect = useCallback(() => {
    try {
      socketRef.current = io(url, {
        transports: ['websocket'],
        upgrade: false,
      });

      socketRef.current.on('connect', () => {
        setIsConnected(true);
        setConnectionError(null);
        console.log('WebSocket connected');
      });

      socketRef.current.on('disconnect', () => {
        setIsConnected(false);
        console.log('WebSocket disconnected');
      });

      socketRef.current.on('transcription', (data: TranscriptionResult) => {
        onTranscription(data);
      });

      socketRef.current.on('error', (error: any) => {
        const errorMessage = typeof error === 'string' ? error : error.message || 'WebSocket error';
        setConnectionError(errorMessage);
        onError(errorMessage);
      });

      socketRef.current.on('connect_error', (error: any) => {
        const errorMessage = `Connection failed: ${error.message}`;
        setConnectionError(errorMessage);
        onError(errorMessage);
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create WebSocket connection';
      setConnectionError(errorMessage);
      onError(errorMessage);
    }
  }, [url, onTranscription, onError]);

  const disconnect = useCallback(() => {
    if (socketRef.current) {
      socketRef.current.disconnect();
      socketRef.current = null;
      setIsConnected(false);
    }
  }, []);

  const sendAudioData = useCallback((audioBlob: Blob) => {
    if (socketRef.current && isConnected) {
      // 将音频数据转换为 ArrayBuffer 并发送
      audioBlob.arrayBuffer().then(buffer => {
        socketRef.current?.emit('audio_data', buffer);
      });
    }
  }, [isConnected]);

  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  return {
    isConnected,
    connectionError,
    connect,
    disconnect,
    sendAudioData,
  };
};
