import { useState, useRef, useCallback } from 'react';
import { RecordingState, AudioRecordingConfig } from '../types';

interface UseAudioRecorderProps {
  onDataAvailable: (audioBlob: Blob) => void;
  onError: (error: string) => void;
  config?: AudioRecordingConfig;
}

export const useAudioRecorder = ({ onDataAvailable, onError, config }: UseAudioRecorderProps) => {
  const [recordingState, setRecordingState] = useState<RecordingState>(RecordingState.IDLE);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);

  const defaultConfig: AudioRecordingConfig = {
    echoCancellation: false,
    noiseSuppression: false,
    autoGainControl: false,
    sampleRate: 16000,
    channelCount: 1,
    ...config
  };

  const startRecording = useCallback(async (includeSystemAudio: boolean = false) => {
    try {
      setRecordingState(RecordingState.RECORDING);

      let finalStream: MediaStream;

      if (includeSystemAudio) {
        // 获取系统音频和麦克风音频并混合
        const systemStream = await navigator.mediaDevices.getDisplayMedia({
          video: false,
          audio: {
            echoCancellation: defaultConfig.echoCancellation,
            noiseSuppression: defaultConfig.noiseSuppression,
            autoGainControl: defaultConfig.autoGainControl,
            sampleRate: defaultConfig.sampleRate,
            channelCount: defaultConfig.channelCount
          }
        });

        const micStream = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: defaultConfig.echoCancellation,
            noiseSuppression: defaultConfig.noiseSuppression,
            autoGainControl: defaultConfig.autoGainControl
          }
        });

        // 创建音频上下文进行混合
        audioContextRef.current = new AudioContext();
        const destination = audioContextRef.current.createMediaStreamDestination();

        if (systemStream.getAudioTracks().length > 0) {
          const systemSource = audioContextRef.current.createMediaStreamSource(systemStream);
          systemSource.connect(destination);
        }

        if (micStream.getAudioTracks().length > 0) {
          const micSource = audioContextRef.current.createMediaStreamSource(micStream);
          micSource.connect(destination);
        }

        finalStream = destination.stream;
        streamRef.current = new MediaStream([
          ...systemStream.getTracks(),
          ...micStream.getTracks(),
          ...finalStream.getTracks()
        ]);
      } else {
        // 仅获取麦克风音频
        finalStream = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: defaultConfig.echoCancellation,
            noiseSuppression: defaultConfig.noiseSuppression,
            autoGainControl: defaultConfig.autoGainControl,
            sampleRate: defaultConfig.sampleRate,
            channelCount: defaultConfig.channelCount
          }
        });
        streamRef.current = finalStream;
      }

      // 创建 MediaRecorder
      const mimeType = MediaRecorder.isTypeSupported('audio/webm;codecs=opus') 
        ? 'audio/webm;codecs=opus' 
        : 'audio/webm';

      mediaRecorderRef.current = new MediaRecorder(finalStream, {
        mimeType,
        audioBitsPerSecond: 128000
      });

      mediaRecorderRef.current.ondataavailable = (event) => {
        if (event.data && event.data.size > 0) {
          onDataAvailable(event.data);
        }
      };

      mediaRecorderRef.current.onerror = (event) => {
        const error = event.error || new Error('MediaRecorder error');
        onError(`Recording error: ${error.message}`);
        setRecordingState(RecordingState.ERROR);
      };

      mediaRecorderRef.current.onstop = () => {
        setRecordingState(RecordingState.IDLE);
      };

      // 开始录制，每2秒产生一个数据块
      mediaRecorderRef.current.start(2000);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to start recording';
      onError(errorMessage);
      setRecordingState(RecordingState.ERROR);
    }
  }, [onDataAvailable, onError, defaultConfig]);

  const stopRecording = useCallback(() => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
      mediaRecorderRef.current.stop();
    }

    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }

    if (audioContextRef.current) {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }

    setRecordingState(RecordingState.IDLE);
  }, []);

  const isRecording = recordingState === RecordingState.RECORDING;

  return {
    recordingState,
    isRecording,
    startRecording,
    stopRecording
  };
};
