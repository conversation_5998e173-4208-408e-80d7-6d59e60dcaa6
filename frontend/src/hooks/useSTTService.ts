import { useState, useCallback, useRef, useEffect } from 'react';
import { TranscriptionResult } from '../types';
import { getWebSocketService } from '../services/websocketService';
import { useAudioRecorder } from './useAudioRecorder';
import { convertWebMToWAV } from '../utils/audioProcessor';

interface UseSTTServiceProps {
  websocketUrl: string;
  includeSystemAudio?: boolean;
}

export const useSTTService = ({ websocketUrl, includeSystemAudio = false }: UseSTTServiceProps) => {
  const [isActive, setIsActive] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [transcriptions, setTranscriptions] = useState<TranscriptionResult[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [status, setStatus] = useState<string>('');

  const websocketServiceRef = useRef(getWebSocketService(websocketUrl));

  // 处理音频数据的回调
  const handleAudioData = useCallback(async (audioBlob: Blob) => {
    try {
      // 转换音频格式为 WAV（如果需要）
      const wavBlob = await convertWebMToWAV(audioBlob);
      
      // 通过 WebSocket 发送音频数据
      websocketServiceRef.current.sendAudioData(wavBlob);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to process audio';
      setError(errorMessage);
    }
  }, []);

  // 处理录制错误的回调
  const handleRecordingError = useCallback((error: string) => {
    setError(error);
    setIsActive(false);
  }, []);

  // 使用音频录制 hook
  const { isRecording, startRecording, stopRecording, recordingState } = useAudioRecorder({
    onDataAvailable: handleAudioData,
    onError: handleRecordingError,
    config: {
      echoCancellation: false,
      noiseSuppression: false,
      autoGainControl: false,
      sampleRate: 16000,
      channelCount: 1
    }
  });

  // 处理转录结果
  const handleTranscription = useCallback((result: TranscriptionResult) => {
    setTranscriptions(prev => [...prev, result]);
  }, []);

  // 处理 WebSocket 错误
  const handleWebSocketError = useCallback((error: string) => {
    setError(error);
  }, []);

  // 处理状态更新
  const handleStatus = useCallback((status: string) => {
    setStatus(status);
  }, []);

  // 处理连接状态
  const handleConnect = useCallback(() => {
    setIsConnected(true);
    setError(null);
  }, []);

  const handleDisconnect = useCallback(() => {
    setIsConnected(false);
  }, []);

  // 初始化 WebSocket 事件监听器
  useEffect(() => {
    const ws = websocketServiceRef.current;
    
    ws.onTranscription(handleTranscription);
    ws.onError(handleWebSocketError);
    ws.onStatus(handleStatus);
    ws.onConnect(handleConnect);
    ws.onDisconnect(handleDisconnect);

    return () => {
      ws.disconnect();
    };
  }, [handleTranscription, handleWebSocketError, handleStatus, handleConnect, handleDisconnect]);

  // 开始 STT 服务
  const startSTT = useCallback(async () => {
    try {
      setError(null);
      setTranscriptions([]);
      
      // 连接 WebSocket
      if (!isConnected) {
        await websocketServiceRef.current.connect();
      }
      
      // 开始录制音频
      await startRecording(includeSystemAudio);
      setIsActive(true);
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to start STT service';
      setError(errorMessage);
      setIsActive(false);
    }
  }, [isConnected, startRecording, includeSystemAudio]);

  // 停止 STT 服务
  const stopSTT = useCallback(() => {
    stopRecording();
    setIsActive(false);
  }, [stopRecording]);

  // 清除转录结果
  const clearTranscriptions = useCallback(() => {
    setTranscriptions([]);
  }, []);

  // 重连 WebSocket
  const reconnect = useCallback(async () => {
    try {
      setError(null);
      await websocketServiceRef.current.connect();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to reconnect';
      setError(errorMessage);
    }
  }, []);

  return {
    isActive,
    isConnected,
    isRecording,
    recordingState,
    transcriptions,
    error,
    status,
    startSTT,
    stopSTT,
    clearTranscriptions,
    reconnect
  };
};
