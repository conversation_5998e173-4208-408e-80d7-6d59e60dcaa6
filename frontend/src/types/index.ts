// 音频质量配置
export interface AudioQuality {
  bitRate: number;
  sampleRate: number;
  label: string;
}

// 视频质量配置
export interface VideoQuality {
  width: number;
  height: number;
  frameRate: number;
  label: string;
}

// 录制状态
export enum RecordingState {
  IDLE = 'idle',
  RECORDING = 'recording',
  PROCESSING = 'processing',
  ERROR = 'error'
}

// STT 转录结果
export interface TranscriptionResult {
  id: string;
  text: string;
  timestamp: number;
  confidence?: number;
}

// WebSocket 消息类型
export interface WebSocketMessage {
  type: 'transcription' | 'error' | 'status';
  data: any;
  timestamp: number;
}

// 音频录制配置
export interface AudioRecordingConfig {
  echoCancellation: boolean;
  noiseSuppression: boolean;
  autoGainControl: boolean;
  sampleRate?: number;
  channelCount?: number;
}

// 录制会话信息
export interface RecordingSession {
  id: string;
  startTime: number;
  endTime?: number;
  videoBlob?: Blob;
  transcriptions: TranscriptionResult[];
}

// 应用状态
export interface AppState {
  isRecording: boolean;
  isSTTActive: boolean;
  recordingState: RecordingState;
  currentSession?: RecordingSession;
  transcriptions: TranscriptionResult[];
  error?: string;
}
