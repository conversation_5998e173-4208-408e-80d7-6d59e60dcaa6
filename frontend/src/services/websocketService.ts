import { TranscriptionResult, WebSocketMessage } from '../types';

export class WebSocketService {
  private socket: WebSocket | null = null;
  private url: string;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private isManualClose = false;

  // 事件回调
  private onTranscriptionCallback?: (result: TranscriptionResult) => void;
  private onErrorCallback?: (error: string) => void;
  private onStatusCallback?: (status: string) => void;
  private onConnectCallback?: () => void;
  private onDisconnectCallback?: () => void;

  constructor(url: string) {
    this.url = url;
  }

  // 设置事件回调
  onTranscription(callback: (result: TranscriptionResult) => void) {
    this.onTranscriptionCallback = callback;
  }

  onError(callback: (error: string) => void) {
    this.onErrorCallback = callback;
  }

  onStatus(callback: (status: string) => void) {
    this.onStatusCallback = callback;
  }

  onConnect(callback: () => void) {
    this.onConnectCallback = callback;
  }

  onDisconnect(callback: () => void) {
    this.onDisconnectCallback = callback;
  }

  // 连接 WebSocket
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.isManualClose = false;
        this.socket = new WebSocket(this.url);

        this.socket.onopen = () => {
          console.log('WebSocket connected');
          this.reconnectAttempts = 0;
          this.onConnectCallback?.();
          resolve();
        };

        this.socket.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            console.error('Failed to parse WebSocket message:', error);
            this.onErrorCallback?.('Failed to parse server message');
          }
        };

        this.socket.onclose = (event) => {
          console.log('WebSocket closed:', event.code, event.reason);
          this.onDisconnectCallback?.();
          
          if (!this.isManualClose && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.attemptReconnect();
          }
        };

        this.socket.onerror = (error) => {
          console.error('WebSocket error:', error);
          this.onErrorCallback?.('WebSocket connection error');
          reject(new Error('WebSocket connection failed'));
        };

      } catch (error) {
        reject(error);
      }
    });
  }

  // 断开连接
  disconnect() {
    this.isManualClose = true;
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
  }

  // 发送音频数据
  sendAudioData(audioBlob: Blob) {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      // 将 Blob 转换为 ArrayBuffer 并发送
      audioBlob.arrayBuffer().then(buffer => {
        if (this.socket && this.socket.readyState === WebSocket.OPEN) {
          this.socket.send(buffer);
        }
      });
    } else {
      this.onErrorCallback?.('WebSocket is not connected');
    }
  }

  // 发送文本消息
  sendMessage(message: any) {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify(message));
    } else {
      this.onErrorCallback?.('WebSocket is not connected');
    }
  }

  // 获取连接状态
  isConnected(): boolean {
    return this.socket?.readyState === WebSocket.OPEN;
  }

  // 处理接收到的消息
  private handleMessage(message: WebSocketMessage) {
    switch (message.type) {
      case 'transcription':
        if (this.onTranscriptionCallback) {
          const result: TranscriptionResult = {
            id: message.data.id || Date.now().toString(),
            text: message.data.text || message.data,
            timestamp: message.timestamp || Date.now(),
            confidence: message.data.confidence
          };
          this.onTranscriptionCallback(result);
        }
        break;

      case 'error':
        this.onErrorCallback?.(message.data);
        break;

      case 'status':
        this.onStatusCallback?.(message.data);
        break;

      default:
        console.warn('Unknown message type:', message.type);
    }
  }

  // 尝试重连
  private attemptReconnect() {
    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    
    console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms`);
    
    setTimeout(() => {
      if (!this.isManualClose) {
        this.connect().catch(error => {
          console.error('Reconnection failed:', error);
          if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            this.onErrorCallback?.('Failed to reconnect after maximum attempts');
          }
        });
      }
    }, delay);
  }
}

// 创建单例实例
let websocketService: WebSocketService | null = null;

export const getWebSocketService = (url?: string): WebSocketService => {
  if (!websocketService && url) {
    websocketService = new WebSocketService(url);
  } else if (!websocketService) {
    throw new Error('WebSocket service not initialized. Please provide URL.');
  }
  return websocketService;
};
