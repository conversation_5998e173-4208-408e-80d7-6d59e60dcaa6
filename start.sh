#!/bin/bash

# 智能语音转录系统启动脚本
# 作者: AI Assistant
# 日期: 2025-07-18

set -e

echo "🚀 启动智能语音转录系统..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

# 检查Docker Compose是否安装
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p app/logs
mkdir -p app/models
mkdir -p nginx/ssl

# 停止现有容器
echo "🛑 停止现有容器..."
docker-compose down --remove-orphans

# 构建并启动服务
echo "🔨 构建并启动服务..."
docker-compose up --build -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose ps

# 等待Ollama服务启动
echo "⏳ 等待Ollama服务启动..."
timeout=60
counter=0
while ! curl -s http://localhost:11434/api/tags > /dev/null; do
    if [ $counter -ge $timeout ]; then
        echo "❌ Ollama服务启动超时"
        exit 1
    fi
    echo "等待Ollama服务... ($counter/$timeout)"
    sleep 2
    counter=$((counter + 2))
done

# 下载Gemma3模型
echo "📥 下载Gemma3翻译模型..."
docker-compose exec ollama ollama pull gemma2:2b || echo "⚠️ 模型下载失败，请手动执行: docker-compose exec ollama ollama pull gemma2:2b"

# 检查Whisper服务
echo "🔍 检查Whisper转录服务..."
timeout=60
counter=0
while ! curl -s http://localhost:9004/health > /dev/null; do
    if [ $counter -ge $timeout ]; then
        echo "❌ Whisper服务启动超时"
        exit 1
    fi
    echo "等待Whisper服务... ($counter/$timeout)"
    sleep 2
    counter=$((counter + 2))
done

echo "✅ 系统启动完成！"
echo ""
echo "🌐 访问地址："
echo "   - 主页面: http://localhost:9004"
echo "   - 智能转录: http://localhost:9004/static/adaptive_transcription.html"
echo "   - API文档: http://localhost:9004/docs"
echo "   - 健康检查: http://localhost:9004/health"
echo ""
echo "🔧 管理命令："
echo "   - 查看日志: docker-compose logs -f"
echo "   - 停止服务: docker-compose down"
echo "   - 重启服务: docker-compose restart"
echo ""
echo "📊 服务状态："
docker-compose ps
